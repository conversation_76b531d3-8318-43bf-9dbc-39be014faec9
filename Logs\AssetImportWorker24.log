Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker24
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker24.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23924] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2959864066 [EditorId] 2959864066 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [23924] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2959864066 [EditorId] 2959864066 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 960.38 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56936
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002415 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 895.32 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.496 seconds
Domain Reload Profiling:
	ReloadAssembly (1496ms)
		BeginReloadAssembly (77ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1308ms)
			LoadAssemblies (76ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (116ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (1097ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (895ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.018466 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1124.28 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.

=================================================================
	Native Crash Reporting
=================================================================
Got a UNKNOWN while executing native code. This usually indicates
a fatal error in the mono runtime or one of the native libraries 
used by your application.
=================================================================

=================================================================
	Managed Stacktrace:
=================================================================
	  at <unknown> <0xffffffff>
	  at System.String:FastAllocateString <0x000c1>
	  at System.Text.StringBuilder:ToString <0x00172>
	  at System.Reflection.AssemblyName:get_FullName <0x00933>
	  at System.Reflection.AssemblyName:ToString <0x0007a>
	  at Sirenix.Utilities.AssemblyUtilities:IsDependentOn <0x00324>
	  at Sirenix.Utilities.AssemblyUtilities:GetAssemblyTypeFlagNoLookup <0x00642>
	  at Sirenix.Utilities.AssemblyUtilities:GetAssemblyTypeFlag <0x00262>
	  at IdentifierLookups:RunInitializeTask <0x00682>
	  at System.Threading.ThreadHelper:ThreadStart_Context <0x00131>
	  at System.Threading.ExecutionContext:RunInternal <0x0061d>
	  at System.Threading.ExecutionContext:Run <0x0008a>
	  at System.Threading.ExecutionContext:Run <0x001e2>
	  at System.Threading.ThreadHelper:ThreadStart <0x000aa>
	  at System.Object:runtime_invoke_void__this__ <0x00337>
=================================================================
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Mono: successfully reloaded assembly
- Completed reload, in  6.820 seconds
Domain Reload Profiling:
	ReloadAssembly (6821ms)
		BeginReloadAssembly (194ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (87ms)
		EndReloadAssembly (6526ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (2631ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (275ms)
			SetupLoadedEditorAssemblies (3407ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1124ms)
				BeforeProcessingInitializeOnLoad (150ms)
				ProcessInitializeOnLoadAttributes (1784ms)
				ProcessInitializeOnLoadMethodAttributes (333ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 14.40 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10224 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 30.126800 ms (FindLiveObjects: 1.111200 ms CreateObjectMapping: 1.250100 ms MarkObjects: 26.977300 ms  DeleteObjects: 0.786300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 54.66 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10745.
Memory consumption went from 295.2 MB to 293.2 MB.
Total: 36.553300 ms (FindLiveObjects: 1.155200 ms CreateObjectMapping: 1.424000 ms MarkObjects: 31.589300 ms  DeleteObjects: 2.383300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018828 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.91 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.163 seconds
Domain Reload Profiling:
	ReloadAssembly (3164ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2881ms)
			LoadAssemblies (138ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (541ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (2009ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1588ms)
				ProcessInitializeOnLoadMethodAttributes (284ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.68 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10758.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 39.771400 ms (FindLiveObjects: 1.173500 ms CreateObjectMapping: 3.565100 ms MarkObjects: 34.251300 ms  DeleteObjects: 0.779300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 62.31 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10758.
Memory consumption went from 296.2 MB to 294.1 MB.
Total: 38.019000 ms (FindLiveObjects: 1.042200 ms CreateObjectMapping: 1.554800 ms MarkObjects: 33.822300 ms  DeleteObjects: 1.598400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 494055.772674 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png
  artifactKey: Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png using Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '69e21d90c52ece0a5fa4f52163b7d73e') in 0.186034 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.008239 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd873ee33231a9224fe54d10f678c944a') in 0.109619 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.873864 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '85fd25718c656b31e4d802fd3f7cccc8') in 0.069483 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.989746 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Panel_ModuleSelect.prefab
  artifactKey: Guid(a4268dcd9c25986488095fb53d820a54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Panel_ModuleSelect.prefab using Guid(a4268dcd9c25986488095fb53d820a54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a27fa11027fa97fb495deeebf4d4a78c') in 0.008570 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017841 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 15.08 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.681 seconds
Domain Reload Profiling:
	ReloadAssembly (4682ms)
		BeginReloadAssembly (1338ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (187ms)
		EndReloadAssembly (3225ms)
			LoadAssemblies (145ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (578ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (105ms)
			SetupLoadedEditorAssemblies (2300ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (15ms)
				BeforeProcessingInitializeOnLoad (105ms)
				ProcessInitializeOnLoadAttributes (1873ms)
				ProcessInitializeOnLoadMethodAttributes (286ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 35.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9735 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10780.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 35.987200 ms (FindLiveObjects: 1.143100 ms CreateObjectMapping: 1.189100 ms MarkObjects: 32.912200 ms  DeleteObjects: 0.740700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 81.67 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10780.
Memory consumption went from 300.4 MB to 298.3 MB.
Total: 35.801200 ms (FindLiveObjects: 2.186700 ms CreateObjectMapping: 1.079500 ms MarkObjects: 31.767700 ms  DeleteObjects: 0.765700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1901.952392 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '63b584fcd483ae4e71c82e42fdde76a6') in 0.131025 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020284 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.05 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.204 seconds
Domain Reload Profiling:
	ReloadAssembly (4206ms)
		BeginReloadAssembly (357ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (19ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (203ms)
		EndReloadAssembly (3730ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (534ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (105ms)
			SetupLoadedEditorAssemblies (2872ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (2463ms)
				ProcessInitializeOnLoadMethodAttributes (293ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.69 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10802.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.313200 ms (FindLiveObjects: 0.836500 ms CreateObjectMapping: 0.820600 ms MarkObjects: 19.079100 ms  DeleteObjects: 0.575700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 55152.812832 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Item_Entry.prefab
  artifactKey: Guid(e542cf11460a5d545a30d684c0370d09) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Item_Entry.prefab using Guid(e542cf11460a5d545a30d684c0370d09) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '59f3c93c7b6cc30e32478ed4e43c046b') in 0.148410 seconds 
========================================================================
Received Import Request.
  Time since last request: 93.952640 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/PassBuyBg
  artifactKey: Guid(e8952217d70e5234f80255102feab53c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/PassBuyBg using Guid(e8952217d70e5234f80255102feab53c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a061505338d1f46da7b07bf4b82e25ef') in 0.001316 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.097165 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/PassBuyBg/pass_buy_bg.png
  artifactKey: Guid(d2305e3fa27976b40a27c444c512fab6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/PassBuyBg/pass_buy_bg.png using Guid(d2305e3fa27976b40a27c444c512fab6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2c5b7d26e693f92c04fe567cc0ce877') in 0.198785 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.434473 seconds.
  path: Assets/AssetBundle/UI2/UITexture/SkillIcon
  artifactKey: Guid(8e74a2f558831624ebf4f734fa3d782d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/SkillIcon using Guid(8e74a2f558831624ebf4f734fa3d782d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '65b262c4c74de8c6036017612755039a') in 0.001221 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.074979 seconds.
  path: Assets/AssetBundle/UI2/UITexture/SkillIcon/3.png
  artifactKey: Guid(1c3a0554e1e8e2947aee1aa4ad8bc577) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/SkillIcon/3.png using Guid(1c3a0554e1e8e2947aee1aa4ad8bc577) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2e6dce07062f9fa4a4f9dfde1737f3c7') in 0.017201 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.761191 seconds.
  path: Assets/AssetBundle/UI2/UITexture/RideIcon
  artifactKey: Guid(0b2bec54943696340bc809fae7c67edc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/RideIcon using Guid(0b2bec54943696340bc809fae7c67edc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f14e2dcdf2370fad370caf7dfe8b15a5') in 0.001872 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.081505 seconds.
  path: Assets/AssetBundle/UI2/UITexture/RideIcon/10001.png
  artifactKey: Guid(d08ea5cefce5002438d9edfcb2b4a1fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/RideIcon/10001.png using Guid(d08ea5cefce5002438d9edfcb2b4a1fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a4661fbe225dc5255f9f494b7a664a5') in 0.340704 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/RideIcon/10004.png
  artifactKey: Guid(6b5fe43608d240a4a9099358e29672a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/RideIcon/10004.png using Guid(6b5fe43608d240a4a9099358e29672a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6ee1f94011ed882240ac7331edb44b4c') in 0.336493 seconds 
========================================================================
Received Import Request.
  Time since last request: 29.169407 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame
  artifactKey: Guid(c36db31e75bb2de43bf36da7afcf4edc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame using Guid(c36db31e75bb2de43bf36da7afcf4edc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f96a447b6a56ff7ecaf079e260366044') in 0.002378 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.083190 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215001.png
  artifactKey: Guid(3a68708bb1d721a40a259d603ec86437) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215001.png using Guid(3a68708bb1d721a40a259d603ec86437) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cbf2c87b6b6a961c82600784d8873c99') in 0.020721 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.941489 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215004.png
  artifactKey: Guid(f867fa1cf58f9714888504ef25e7bfba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215004.png using Guid(f867fa1cf58f9714888504ef25e7bfba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9ad000d08f297e934eb37cd70eefb7cd') in 2.310580 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215027.png
  artifactKey: Guid(df925176a11d074438bc5dd9534b2065) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215027.png using Guid(df925176a11d074438bc5dd9534b2065) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3fdddb7228cc5e756c1fd42b6cf7c854') in 0.030183 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215010.png
  artifactKey: Guid(cdeb07c61ac32e24bb70bcb05e8dde7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215010.png using Guid(cdeb07c61ac32e24bb70bcb05e8dde7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0fd6442ece3b9c4c7a55cef124cb5ae2') in 0.051187 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215022.png
  artifactKey: Guid(f7b295f3522b7c44b8e3d2fca64c9976) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215022.png using Guid(f7b295f3522b7c44b8e3d2fca64c9976) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a815f635ff97bece286b7438ef7f0677') in 0.067552 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215012.png
  artifactKey: Guid(8534e30899e98614c8a1cd1277b4440f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215012.png using Guid(8534e30899e98614c8a1cd1277b4440f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03aa8675567d85b3cd831de08031f890') in 0.043954 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215023.png
  artifactKey: Guid(57d08b8336abdac4184446dc66e0b817) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215023.png using Guid(57d08b8336abdac4184446dc66e0b817) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'beed6e417fa69a5b9485b744c79b90ee') in 0.031484 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215009.png
  artifactKey: Guid(8d23ef9355637384a8487637af2f7185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215009.png using Guid(8d23ef9355637384a8487637af2f7185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f9002d25ea2d6f09818373cea490ccad') in 0.025515 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215006.png
  artifactKey: Guid(be74dfe4787ccff4fb0cd5600e8827d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215006.png using Guid(be74dfe4787ccff4fb0cd5600e8827d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b81f1fa9872706227976446d65719657') in 0.027153 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215016.png
  artifactKey: Guid(cba956bed22b6a447b8e4d54542ee16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215016.png using Guid(cba956bed22b6a447b8e4d54542ee16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e57ff4b595d3e8642d9a17a696a23a84') in 0.049224 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215024.png
  artifactKey: Guid(89c0a8447f40b384fb9854e4ec747db3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215024.png using Guid(89c0a8447f40b384fb9854e4ec747db3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '412223ec23e9ffb529a93b0300bd270f') in 0.019905 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215011.png
  artifactKey: Guid(18dd36ed0279aab48acf94ba5d448dbb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215011.png using Guid(18dd36ed0279aab48acf94ba5d448dbb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4056662bbab0221a32fc8d21447eb7d5') in 0.038882 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215018.png
  artifactKey: Guid(a97be944ea2d5604b8364e870bbdd86e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215018.png using Guid(a97be944ea2d5604b8364e870bbdd86e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '33c4bb7ede1c706ed0b85b4a68c3417e') in 0.022085 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215029.png
  artifactKey: Guid(fb1cbf6013667cb43845a7bfda53f013) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215029.png using Guid(fb1cbf6013667cb43845a7bfda53f013) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '14375460f3ba4e4988fe4d5641c7ed8f') in 0.041861 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215008.png
  artifactKey: Guid(fab112e54db44d34eaced139ec1986b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215008.png using Guid(fab112e54db44d34eaced139ec1986b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1794a309b5f7de50ea2cc63e24124133') in 0.045462 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.250656 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215032.png
  artifactKey: Guid(9949c004fd425434aa4f2b20106c815f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215032.png using Guid(9949c004fd425434aa4f2b20106c815f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61dd458198e9b9d367a2eda521b8e8d7') in 0.024161 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215042.png
  artifactKey: Guid(a457e581149b00e42b2ccf90d9cf518c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215042.png using Guid(a457e581149b00e42b2ccf90d9cf518c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b376e218a07f785d5ad8c88c2f7bdad') in 0.015307 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215045.png
  artifactKey: Guid(ec7ec5bc12981da4ba8d814b1cd25559) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215045.png using Guid(ec7ec5bc12981da4ba8d814b1cd25559) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b30546f21c9d6444b9d88dd118cce04f') in 0.022691 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215036.png
  artifactKey: Guid(8625d52925d8df148bf5799d3d0d6eab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215036.png using Guid(8625d52925d8df148bf5799d3d0d6eab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c1f07df4617d522d9c2427711215ec2d') in 0.035972 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215044.png
  artifactKey: Guid(03284fd21eec18c4fba1973efaf76496) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215044.png using Guid(03284fd21eec18c4fba1973efaf76496) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9a3bc9dd5ac92fe048c07c3e9ef2286a') in 0.034011 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215041.png
  artifactKey: Guid(ea1989295b016594c8d7f49b75a8420e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215041.png using Guid(ea1989295b016594c8d7f49b75a8420e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f2c961d43820dc2a0376eee1156bbb40') in 0.033102 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215039.png
  artifactKey: Guid(8181af8003d8b444796cb7e1cb7f56ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215039.png using Guid(8181af8003d8b444796cb7e1cb7f56ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32f4fdc6e686b8e71c4c668d6a365a22') in 0.020611 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0