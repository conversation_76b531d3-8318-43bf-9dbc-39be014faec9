<?xml version="1.0" encoding="utf-8"?>
<BuffTemplate type="DeepCore.GameData.Zone.BuffTemplate">
  <AppearanceId>0</AppearanceId>
  <BanAttack>False</BanAttack>
  <BanInteract>False</BanInteract>
  <BanMove>False</BanMove>
  <BanUseItem>False</BanUseItem>
  <BindingEffectList element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <BodyHitSize>0.00</BodyHitSize>
  <BodyScale>1.00</BodyScale>
  <BodySize>0.00</BodySize>
  <ClientVisible>True</ClientVisible>
  <ExclusiveCatgory>0</ExclusiveCatgory>
  <ExclusivePriority>0</ExclusivePriority>
  <FirstTimeEnable>True</FirstTimeEnable>
  <HitIntervalMS>0</HitIntervalMS>
  <IconName>400002</IconName>
  <ID>103011</ID>
  <IsCancelBySelf>False</IsCancelBySelf>
  <IsClientManagedMove>False</IsClientManagedMove>
  <IsDuplicating>False</IsDuplicating>
  <IsHarmful>False</IsHarmful>
  <IsInvincible>False</IsInvincible>
  <IsInvisible>False</IsInvisible>
  <IsNoneBlock>False</IsNoneBlock>
  <IsOverlay>False</IsOverlay>
  <IsPassive>False</IsPassive>
  <IsRemoveOnSenderRemoved>False</IsRemoveOnSenderRemoved>
  <IsSilent>False</IsSilent>
  <KeyFrames element_type="DeepCore.GameData.Zone.BuffTemplate+KeyFrame" />
  <LifeTimeMS>30000</LifeTimeMS>
  <MakeAvatar>False</MakeAvatar>
  <MakeStun>False</MakeStun>
  <MaxOverlay>1</MaxOverlay>
  <Name>透视眼buff</Name>
  <OverlayBindingEffect element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <Properties type="BWBattle.Common.Plugins.BWBuffProperties">
    <BuffData type="DeepCore.ArrayList`1[[BWBattle.Common.Plugins.BWBuffData, BeeWorld.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]" element_type="BWBattle.Common.Plugins.BWBuffData" />
    <CanBePurged>True</CanBePurged>
    <SaveOnChangeScene>True</SaveOnChangeScene>
  </Properties>
  <TriggerClientEvt>BuffScript\Buff103021</TriggerClientEvt>
  <UnitChangeSkills>True</UnitChangeSkills>
  <UnitFileName>prefab/object_prefab/prop_prefab/c_prop_jiuping02.prefab</UnitFileName>
  <UnitKeepSkillsID element_type="System.Int32" />
  <UnitSkills element_type="DeepCore.GameData.Zone.LaunchSkill" />
  <property.EditorPath>BUFF/buff第二期/透视眼buff(103011)</property.EditorPath>
</BuffTemplate>