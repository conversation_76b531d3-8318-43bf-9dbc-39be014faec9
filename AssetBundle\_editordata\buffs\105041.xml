<?xml version="1.0" encoding="utf-8"?>
<BuffTemplate type="DeepCore.GameData.Zone.BuffTemplate">
  <AppearanceId>0</AppearanceId>
  <BanAttack>True</BanAttack>
  <BanInteract>True</BanInteract>
  <BanMove>True</BanMove>
  <BanUseItem>True</BanUseItem>
  <BindingEffectList element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <BodyHitSize>0.00</BodyHitSize>
  <BodyScale>1.00</BodyScale>
  <BodySize>0.00</BodySize>
  <ClientVisible>True</ClientVisible>
  <ExclusiveCatgory>0</ExclusiveCatgory>
  <ExclusivePriority>0</ExclusivePriority>
  <FirstTimeEnable>True</FirstTimeEnable>
  <HitIntervalMS>0</HitIntervalMS>
  <HitKeyFrame>
    <Actions element_type="DeepCore.GameData.Zone.BuffKeyFrameAction" />
    <FrameMS>0</FrameMS>
  </HitKeyFrame>
  <IconName>400006</IconName>
  <ID>105041</ID>
  <IsCancelBySelf>False</IsCancelBySelf>
  <IsClientManagedMove>False</IsClientManagedMove>
  <IsDuplicating>False</IsDuplicating>
  <IsHarmful>False</IsHarmful>
  <IsInvincible>False</IsInvincible>
  <IsInvisible>False</IsInvisible>
  <IsNoneBlock>False</IsNoneBlock>
  <IsOverlay>False</IsOverlay>
  <IsPassive>False</IsPassive>
  <IsRemoveOnSenderRemoved>False</IsRemoveOnSenderRemoved>
  <IsSilent>False</IsSilent>
  <KeyFrames element_type="DeepCore.GameData.Zone.BuffTemplate+KeyFrame" />
  <LifeTimeMS>18000</LifeTimeMS>
  <LockStateAction>Sergeant@tps_01_stand_knockoff_fly</LockStateAction>
  <MakeAvatar>False</MakeAvatar>
  <MakeStun>False</MakeStun>
  <MaxOverlay>1</MaxOverlay>
  <Name>香蕉皮-滑倒buff</Name>
  <OverlayBindingEffect element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <Properties type="BWBattle.Common.Plugins.BWBuffProperties">
    <BuffData type="DeepCore.ArrayList`1[[BWBattle.Common.Plugins.BWBuffData, BeeWorld.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]" element_type="BWBattle.Common.Plugins.BWBuffData">
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>SPEED</ChangeType>
        <Value>-2000</Value>
        <ValueType>Value</ValueType>
      </element>
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>DEF</ChangeType>
        <Value>-50</Value>
        <ValueType>Percent</ValueType>
      </element>
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>HP_MAX</ChangeType>
        <Value>-300</Value>
        <ValueType>Value</ValueType>
      </element>
    </BuffData>
    <CanBePurged>True</CanBePurged>
    <SaveOnChangeScene>True</SaveOnChangeScene>
  </Properties>
  <UnitChangeSkills>False</UnitChangeSkills>
  <UnitKeepSkillsID element_type="System.Int32" />
  <UnitSkills element_type="DeepCore.GameData.Zone.LaunchSkill" />
  <property.EditorPath>BUFF/buff第三期/香蕉皮-滑倒buff(105041)</property.EditorPath>
</BuffTemplate>