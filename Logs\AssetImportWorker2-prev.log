Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker2.log
-srvPort
2710
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19624] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2907402795 [EditorId] 2907402795 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19624] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2907402795 [EditorId] 2907402795 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 1197.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56560
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003107 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1544.46 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.629 seconds
Domain Reload Profiling:
	ReloadAssembly (2629ms)
		BeginReloadAssembly (226ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (2131ms)
			LoadAssemblies (225ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (199ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (1785ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1545ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (163ms)
				ProcessInitializeOnLoadMethodAttributes (65ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.023591 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1080.77 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.12 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.524 seconds
Domain Reload Profiling:
	ReloadAssembly (4525ms)
		BeginReloadAssembly (101ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (18ms)
		EndReloadAssembly (4327ms)
			LoadAssemblies (99ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (364ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (132ms)
			SetupLoadedEditorAssemblies (3669ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1081ms)
				BeforeProcessingInitializeOnLoad (127ms)
				ProcessInitializeOnLoadAttributes (1798ms)
				ProcessInitializeOnLoadMethodAttributes (649ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 13.73 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10226 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.53 GB to 0.53 GB.
Total: 27.872000 ms (FindLiveObjects: 1.011700 ms CreateObjectMapping: 0.962100 ms MarkObjects: 25.081500 ms  DeleteObjects: 0.815600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 577.274179 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(25f6db615b6c41e4285e51aa0f6a01b9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(25f6db615b6c41e4285e51aa0f6a01b9) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 42.768536%;
File 'Anim_Giant@hit1' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@hit1' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'ce550ea2186a3b7b104f472492a0ef4b') in 0.275503 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@hit1' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@hit1' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk.FBX
  artifactKey: Guid(e2374a51f0989ef4b8a2113f76afc47d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk.FBX using Guid(e2374a51f0989ef4b8a2113f76afc47d) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 2.195384%;
File 'Anim_Warrior@damaged_walk' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Warrior@damaged_walk' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '066d479a40e7cc34f1a57f93f45854b0') in 0.356770 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk.FBX' had errors: 
 File 'Anim_Warrior@damaged_walk' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Warrior@damaged_walk' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk-inplace.FBX
  artifactKey: Guid(4bbae62780582eb4d8809f7f4187c813) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk-inplace.FBX using Guid(4bbae62780582eb4d8809f7f4187c813) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 2.200416%;
File 'Anim_Warrior@damaged_walk-inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Warrior@damaged_walk-inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'ee2bb35e74a507f124d218a40016f037') in 0.500794 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Warrior@damaged_walk-inplace.FBX' had errors: 
 File 'Anim_Warrior@damaged_walk-inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Warrior@damaged_walk-inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(e5ed1ffb2f290a04db53c5a44f96cf05) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(e5ed1ffb2f290a04db53c5a44f96cf05) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.864480%;
File 'Anim_Giant@death' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@death' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '8a27baa9bfb2a3c16c9069673f4f207e') in 0.173226 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@death' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@death' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(9e8b2fb791ed67f44a57939165aad586) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(9e8b2fb791ed67f44a57939165aad586) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.740702%;
File 'Anim_Giant@taunt' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@taunt' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '16e33d170c84f1ca6499a1bbfb0b9a52') in 0.125236 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@taunt' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@taunt' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/RawData/object/models/outdoors/o_build_shandong01_diantijing.fbx
  artifactKey: Guid(1c1448a6fdb2c36459803010b2c067a8) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/o_build_shandong01_diantijing.fbx using Guid(1c1448a6fdb2c36459803010b2c067a8) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.05 seconds
 -> (artifact id: 'bdaebfabeddc77e9b5c1fec565b28e50') in 0.120367 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawData/object/models/outdoors/o_build_huoshancangku02.FBX
  artifactKey: Guid(aa63f970bb9dc4b4ca81015cd7593c40) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/o_build_huoshancangku02.FBX using Guid(aa63f970bb9dc4b4ca81015cd7593c40) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.09 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.09 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.10 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.10 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.10 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.10 seconds
 -> (artifact id: 'a18ebf8fd55a6125ada46f8d3e740707') in 0.242154 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/outdoors/bake_rongyandong01_box.FBX
  artifactKey: Guid(********************************) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_rongyandong01_box.FBX using Guid(********************************) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7be5efec3ff38892a6e6f37616aeb27f') in 0.054339 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0