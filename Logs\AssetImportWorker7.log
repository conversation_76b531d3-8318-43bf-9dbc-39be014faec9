Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker7.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32920] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2602141353 [EditorId] 2602141353 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [32920] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2602141353 [EditorId] 2602141353 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 932.19 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56556
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002238 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 833.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.364 seconds
Domain Reload Profiling:
	ReloadAssembly (1364ms)
		BeginReloadAssembly (68ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1198ms)
			LoadAssemblies (67ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (102ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (1009ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (834ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (122ms)
				ProcessInitializeOnLoadMethodAttributes (44ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.017226 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 855.86 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.662 seconds
Domain Reload Profiling:
	ReloadAssembly (3663ms)
		BeginReloadAssembly (102ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (18ms)
		EndReloadAssembly (3467ms)
			LoadAssemblies (113ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (467ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (197ms)
			SetupLoadedEditorAssemblies (2630ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (856ms)
				BeforeProcessingInitializeOnLoad (128ms)
				ProcessInitializeOnLoadAttributes (1339ms)
				ProcessInitializeOnLoadMethodAttributes (280ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 11.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 19.741300 ms (FindLiveObjects: 0.842500 ms CreateObjectMapping: 0.863600 ms MarkObjects: 17.477700 ms  DeleteObjects: 0.556500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 300758.775594 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/facility_instruction.png
  artifactKey: Guid(9ca02579b4c4b2f469c694b3f1bafb74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/facility_instruction.png using Guid(9ca02579b4c4b2f469c694b3f1bafb74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee1d467999e9d814e118cc3c9258cc83') in 0.156751 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.658433 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_battery_01.png
  artifactKey: Guid(d7276314d26185b48b11d3b43ce3c2d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_battery_01.png using Guid(d7276314d26185b48b11d3b43ce3c2d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e7c90ece67ae3d0edc09f0492aaa67c') in 0.014089 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic_square.png
  artifactKey: Guid(cb105c0eca3c9624aa85ed9c09155057) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic_square.png using Guid(cb105c0eca3c9624aa85ed9c09155057) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1a16e154a20c32e9cbf6fea9be14c999') in 0.014032 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic3.png
  artifactKey: Guid(4839894aeb590c84b8fb4f11d5aad9c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic3.png using Guid(4839894aeb590c84b8fb4f11d5aad9c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61080594498fbac62acaae7c1e5c4d1c') in 0.018406 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_battery_02.png
  artifactKey: Guid(f792dc88bc568e341a557550745318ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_battery_02.png using Guid(f792dc88bc568e341a557550745318ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '72b7ab084ec22f1eb9e38d89aaa91057') in 0.020444 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.510731 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_2.png
  artifactKey: Guid(f14450630d1837b42aeaba02e4e5338d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_2.png using Guid(f14450630d1837b42aeaba02e4e5338d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '152d5c5e4b69e42203b8166bcf63b12c') in 0.018720 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_3.png
  artifactKey: Guid(27cd162d336ab6c4d8678396fd22604b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_3.png using Guid(27cd162d336ab6c4d8678396fd22604b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a594f0304dc7a48ebc2864ea4100d3b8') in 0.012578 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_tip.png
  artifactKey: Guid(d6e5ac971dc6f934dbb4a45e1694cdf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_tip.png using Guid(d6e5ac971dc6f934dbb4a45e1694cdf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c582d440ea4857a04897ee0ceb5930a2') in 0.012060 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_safebox.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_safebox.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '73daa8154219c6f8d314981d18752a21') in 0.013857 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_box.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_box.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7df8426c71892f0c8caf523bff3ab1fb') in 0.009209 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.262959 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi3.png
  artifactKey: Guid(eb9b218335b6984459f547610fc93891) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi3.png using Guid(eb9b218335b6984459f547610fc93891) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0d64b9d1a7848ed6386c4a9ab12744f6') in 0.011764 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/ldg_img_04.png
  artifactKey: Guid(b0c766ed20267d5458dbe50c2b53a988) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/ldg_img_04.png using Guid(b0c766ed20267d5458dbe50c2b53a988) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5c4008cb68b51e3f734a4e495b810020') in 0.015378 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_01.png
  artifactKey: Guid(3b45204aec917d549a13ab2cf65fdcab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_01.png using Guid(3b45204aec917d549a13ab2cf65fdcab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e96f38dc60923e71aea3d296cf2016c') in 0.015121 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/line.png
  artifactKey: Guid(7dd7a9a0433e76d4eb99da0f78ef9578) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/line.png using Guid(7dd7a9a0433e76d4eb99da0f78ef9578) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd773808ef88aa9654c71cca2e002b135') in 0.015047 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/img_mask.png
  artifactKey: Guid(157b97fe4c286404eb1f8db2af226821) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/img_mask.png using Guid(157b97fe4c286404eb1f8db2af226821) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8de64881961172916bfbc770091c4cdd') in 0.013355 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.344793 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/m_slider.png
  artifactKey: Guid(a53ccffdb970ef945bd31c2259c79bca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/m_slider.png using Guid(a53ccffdb970ef945bd31c2259c79bca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '23a72da83711e8b976b0abe4a0c02129') in 0.016020 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/progress_bottom_black.png
  artifactKey: Guid(14f59d97765048d479c55514a7f0c55f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/progress_bottom_black.png using Guid(14f59d97765048d479c55514a7f0c55f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ace318837626d3a21f2680c2f063e34e') in 0.016729 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/parts_select1.png
  artifactKey: Guid(7410f1e2590b7e1418ba220c5a76d4e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/parts_select1.png using Guid(7410f1e2590b7e1418ba220c5a76d4e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a554c0e0da4aec12679ee4a144aa006a') in 0.017315 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/mrjh_img_01.png
  artifactKey: Guid(67cfb330eb00a8d40a5646a798133a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/mrjh_img_01.png using Guid(67cfb330eb00a8d40a5646a798133a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc68e2bced29a60eef7ff77c0c91d1c7') in 0.024696 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/main_tou.png
  artifactKey: Guid(dd611f058b8bfd34ab3596681294c0f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/main_tou.png using Guid(dd611f058b8bfd34ab3596681294c0f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7ca30673bb7ed2442558ea1ae908adee') in 0.016557 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.295438 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/progress_fill_red.png
  artifactKey: Guid(3bdeb57baa9389c4583589a348d7c36c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/progress_fill_red.png using Guid(3bdeb57baa9389c4583589a348d7c36c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6538f6e84b9b05f3b94d94d750d00c88') in 0.012566 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/rl_bg_01.png
  artifactKey: Guid(4083e2fa4a07df84dba4b7ed3bd223ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/rl_bg_01.png using Guid(4083e2fa4a07df84dba4b7ed3bd223ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6a88e1a6515716919b663b15e2780294') in 0.039198 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_pvp_tip.png
  artifactKey: Guid(249edfd16f2137a41827fc62b57557c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_pvp_tip.png using Guid(249edfd16f2137a41827fc62b57557c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '33abcccaacff8032cce5db6ce9e58c5e') in 0.012349 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/rljs_img_03.png
  artifactKey: Guid(8b49b6ef1c0cfff4d9c746db0fca00b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/rljs_img_03.png using Guid(8b49b6ef1c0cfff4d9c746db0fca00b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a299fac1856af84a8fb00931ceee4271') in 0.013115 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.273962 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/slider_handle.png
  artifactKey: Guid(662c9dca819a9d94595c12edc365d63c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/slider_handle.png using Guid(662c9dca819a9d94595c12edc365d63c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '24f4df77fafd3cf5f325f79a4b5f19ec') in 0.013371 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/sssj_img_04.png
  artifactKey: Guid(561cdbd548d100b46bc4441ef2947634) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/sssj_img_04.png using Guid(561cdbd548d100b46bc4441ef2947634) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5491a4dee3c803b07ce6fe1f0228c48b') in 0.013780 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/titlehuawen.png
  artifactKey: Guid(2bea9995581628145b1432203f65eea7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/titlehuawen.png using Guid(2bea9995581628145b1432203f65eea7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0eca11827424cdfc46891da81644a1f5') in 0.016073 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/titlebg.png
  artifactKey: Guid(340030700cbefdd4ea5a4c4d05870705) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/titlebg.png using Guid(340030700cbefdd4ea5a4c4d05870705) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '560f8f361ac01a81dc403d14b03f2e32') in 0.018683 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/top_bg.png
  artifactKey: Guid(c84775d467c5fa044a947ca1218dde07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/top_bg.png using Guid(c84775d467c5fa044a947ca1218dde07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '871a155a63cb88a89b9eb0c3949309d0') in 0.015069 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.668087 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/xzms_icon_02.png
  artifactKey: Guid(6d38a7ee763946a43ba60449a5741722) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/xzms_icon_02.png using Guid(6d38a7ee763946a43ba60449a5741722) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03b8cf02443217c37e8f8a3ed2ba46f8') in 0.013781 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/ykyh_img_01.png
  artifactKey: Guid(2a860af36829f214ba15cdd08d4fa990) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/ykyh_img_01.png using Guid(2a860af36829f214ba15cdd08d4fa990) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '534cbec88d64f4bffcde0816d943aafd') in 0.011693 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0