Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker2.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25524] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 646322196 [EditorId] 646322196 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [25524] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 646322196 [EditorId] 646322196 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 971.20 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56508
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002203 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 893.53 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.468 seconds
Domain Reload Profiling:
	ReloadAssembly (1469ms)
		BeginReloadAssembly (72ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1292ms)
			LoadAssemblies (72ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (115ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (1079ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (894ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (128ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016894 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 905.03 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.909 seconds
Domain Reload Profiling:
	ReloadAssembly (3910ms)
		BeginReloadAssembly (108ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3702ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (512ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (211ms)
			SetupLoadedEditorAssemblies (2798ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (905ms)
				BeforeProcessingInitializeOnLoad (135ms)
				ProcessInitializeOnLoadAttributes (1436ms)
				ProcessInitializeOnLoadMethodAttributes (309ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 12.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 30.660000 ms (FindLiveObjects: 1.506100 ms CreateObjectMapping: 1.452400 ms MarkObjects: 26.984000 ms  DeleteObjects: 0.716100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 223791.597145 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b305192e09fee50644908303cac03007') in 0.087582 seconds 
========================================================================
Received Import Request.
  Time since last request: 46.850531 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResult.prefab
  artifactKey: Guid(de47e0e52b1bbb548b176abdec23debb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResult.prefab using Guid(de47e0e52b1bbb548b176abdec23debb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd17cc4128315e4c408fbbc0543c39d9') in 0.005874 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019969 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.11 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.

=================================================================
	Native Crash Reporting
=================================================================
Got a UNKNOWN while executing native code. This usually indicates
a fatal error in the mono runtime or one of the native libraries 
used by your application.
=================================================================

=================================================================
	Managed Stacktrace:
=================================================================
	  at <unknown> <0xffffffff>
	  at System.String:FastAllocateString <0x000c1>
	  at System.String:CreateStringFromEncoding <0x0015a>
	  at System.Text.Encoding:GetString <0x001fa>
	  at System.Text.Encoding:GetString <0x00162>
	  at System.String:Ctor <0x004b2>
	  at System.String:CreateString <0x0008a>
	  at System.String:.ctor <0x00092>
	  at Mono.RuntimeMarshal:PtrToUtf8String <0x00362>
	  at System.Reflection.AssemblyName:FillName <0x000a2>
	  at System.Reflection.Assembly:GetReferencedAssemblies <0x0036a>
	  at System.Reflection.RuntimeAssembly:GetReferencedAssemblies <0x00072>
	  at CollectGizmoDrawers:.cctor <0x004cb>
	  at System.Object:runtime_invoke_void <0x00184>
	  at <unknown> <0xffffffff>
	  at System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor <0x000c1>
	  at System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor <0x0019a>
	  at UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes <0x00652>
	  at <Module>:runtime_invoke_void_object <0x0018a>
=================================================================
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.819 seconds
Domain Reload Profiling:
	ReloadAssembly (2819ms)
		BeginReloadAssembly (157ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2548ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (486ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1750ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1372ms)
				ProcessInitializeOnLoadMethodAttributes (261ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 34.05 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 175 unused Assets / (2.0 MB). Loaded Objects now: 10760.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 39.132800 ms (FindLiveObjects: 5.303400 ms CreateObjectMapping: 1.718900 ms MarkObjects: 31.293100 ms  DeleteObjects: 0.816100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 59.40 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10760.
Memory consumption went from 295.8 MB to 293.8 MB.
Total: 38.138800 ms (FindLiveObjects: 1.116800 ms CreateObjectMapping: 1.784500 ms MarkObjects: 33.896900 ms  DeleteObjects: 1.339100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 51.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10760.
Memory consumption went from 295.8 MB to 293.8 MB.
Total: 33.705000 ms (FindLiveObjects: 1.006800 ms CreateObjectMapping: 1.018600 ms MarkObjects: 30.887100 ms  DeleteObjects: 0.791200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 227.086854 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2dfd7d43579674615df1d120427a78a1') in 0.100743 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016692 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 16.73 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.789 seconds
Domain Reload Profiling:
	ReloadAssembly (2790ms)
		BeginReloadAssembly (173ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (78ms)
		EndReloadAssembly (2518ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (108ms)
			SetupLoadedEditorAssemblies (1712ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (17ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1353ms)
				ProcessInitializeOnLoadMethodAttributes (223ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 35.58 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10777.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 35.298000 ms (FindLiveObjects: 1.249700 ms CreateObjectMapping: 1.565000 ms MarkObjects: 31.110900 ms  DeleteObjects: 1.366500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 67.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10777.
Memory consumption went from 296.2 MB to 294.2 MB.
Total: 37.996200 ms (FindLiveObjects: 2.511200 ms CreateObjectMapping: 1.995500 ms MarkObjects: 32.585000 ms  DeleteObjects: 0.903400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 86.38 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10777.
Memory consumption went from 296.2 MB to 294.2 MB.
Total: 62.311500 ms (FindLiveObjects: 1.199600 ms CreateObjectMapping: 1.151400 ms MarkObjects: 59.134400 ms  DeleteObjects: 0.824800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 6509.255576 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack5.fbx
  artifactKey: Guid(f7d499aad33b3bb4d9ebd454d4bf05c9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack5.fbx using Guid(f7d499aad33b3bb4d9ebd454d4bf05c9) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.627284%;
[16:38:33.9863]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Attack5
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '9929aa42fb1481006581891016d4d600') in 1.253555 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Rage.fbx
  artifactKey: Guid(262fa47b41d97c245925696cd2b3b2b5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Rage.fbx using Guid(262fa47b41d97c245925696cd2b3b2b5) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 4.709598%;
[16:38:35.0370]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Rage
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'e729ef8b47ed8521c620846ea98f3989') in 0.822414 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_L_Light.fbx
  artifactKey: Guid(2ca45f39c612a43479287212f1165cec) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_L_Light.fbx using Guid(2ca45f39c612a43479287212f1165cec) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 9.591300%;
[16:38:35.9156]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Gethit_L_Light
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '65cfbd5e38e2a0f6c4de8eb402f564e7') in 0.730735 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Walk_B.fbx
  artifactKey: Guid(6bb68183a838e074aae6790e249ab624) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Walk_B.fbx using Guid(6bb68183a838e074aae6790e249ab624) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 8.383893%;
[16:38:36.6727]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Walk_B
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'de552e576f0b22b2dcc02884ee303dea') in 0.767331 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack2.fbx
  artifactKey: Guid(a027bdaf3f67aa04296510e63b61bf37) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack2.fbx using Guid(a027bdaf3f67aa04296510e63b61bf37) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 14.137708%;
[16:38:37.4555]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Attack2
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'a65e70fde68816cd2579d1b5723e0778') in 0.878379 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_F.fbx
  artifactKey: Guid(444543bfd3137c047b79e0c18a5506a1) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_F.fbx using Guid(444543bfd3137c047b79e0c18a5506a1) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.941320%;
[16:38:38.3615]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Gethit_F
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '75a36135901338091e6b9c4590054f57') in 0.809666 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_L.fbx
  artifactKey: Guid(853e9ffec65cc694694a9638a08ae05c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_L.fbx using Guid(853e9ffec65cc694694a9638a08ae05c) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.431381%;
[16:38:39.1856]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Gethit_L
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '0559f95eeab9c13783785c3cedab8d6a') in 0.784412 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Turn180.fbx
  artifactKey: Guid(35fa2996cb420644baf8831ba443380e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Turn180.fbx using Guid(35fa2996cb420644baf8831ba443380e) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 6.578948%;
[16:38:40.0163]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Turn180
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '6868155df1d0005aacb0109175935040') in 0.918683 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Walk_R.fbx
  artifactKey: Guid(5b422d938e5e98f4b86978be66ad5342) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Walk_R.fbx using Guid(5b422d938e5e98f4b86978be66ad5342) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 10.029538%;
[16:38:40.9562]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Walk_R
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '10ba145b46b5e416a1333e18b7d07c19') in 0.862848 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack3.fbx
  artifactKey: Guid(372be330aa706914e960608e0261fecc) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Attack3.fbx using Guid(372be330aa706914e960608e0261fecc) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.430090%;
[16:38:41.8284]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Attack3
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '********************************') in 0.669321 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_R_Light.fbx
  artifactKey: Guid(0b016aead9d36e94ab49dba38056a9b0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_R_Light.fbx using Guid(0b016aead9d36e94ab49dba38056a9b0) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 9.878083%;
[16:38:42.5508]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Gethit_R_Light
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'e09dd97dbd02b2614575eea746879d65') in 0.795286 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Death.fbx
  artifactKey: Guid(7abf938d90d9a824eb1791862dffefac) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Death.fbx using Guid(7abf938d90d9a824eb1791862dffefac) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 10.278666%;
[16:38:43.3623]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Death
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '1bd385ce79c71c332a95f4ceef568806') in 0.876693 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_F_Light.fbx
  artifactKey: Guid(fc404a33b6b3af645b33168564659f64) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dinosaur@Elite_Stronghold_01/DinosaurArmor@Elite_Stronghold_01Gethit_F_Light.fbx using Guid(fc404a33b6b3af645b33168564659f64) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.118426%;
[16:38:44.2554]:OnPostprocessModel=DinosaurArmor@Elite_Stronghold_01Gethit_F_Light
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'f90e067bf007567eab28532ad10db7f1') in 0.743291 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016556 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.41 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.053 seconds
Domain Reload Profiling:
	ReloadAssembly (3054ms)
		BeginReloadAssembly (282ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (160ms)
		EndReloadAssembly (2668ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (502ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1856ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1452ms)
				ProcessInitializeOnLoadMethodAttributes (285ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (25ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 67.18 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.492600 ms (FindLiveObjects: 1.721600 ms CreateObjectMapping: 1.590500 ms MarkObjects: 31.985200 ms  DeleteObjects: 1.193600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 70.53 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 296.4 MB to 294.4 MB.
Total: 41.425000 ms (FindLiveObjects: 1.335100 ms CreateObjectMapping: 1.468700 ms MarkObjects: 37.744300 ms  DeleteObjects: 0.875300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 56.79 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 296.4 MB to 294.4 MB.
Total: 85.340200 ms (FindLiveObjects: 1.082000 ms CreateObjectMapping: 1.145000 ms MarkObjects: 82.035800 ms  DeleteObjects: 1.076300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 65.61 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 296.4 MB to 294.4 MB.
Total: 42.579100 ms (FindLiveObjects: 2.513600 ms CreateObjectMapping: 2.359300 ms MarkObjects: 36.867200 ms  DeleteObjects: 0.837400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 3774.804630 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResonance.prefab
  artifactKey: Guid(503e5ad9c08a702439b7a8ebe2209dbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResonance.prefab using Guid(503e5ad9c08a702439b7a8ebe2209dbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c59ef13399496680ddb0a26a1f8283db') in 0.505001 seconds 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 57.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 296.6 MB to 294.6 MB.
Total: 37.660100 ms (FindLiveObjects: 2.154600 ms CreateObjectMapping: 1.795600 ms MarkObjects: 32.930400 ms  DeleteObjects: 0.778300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 31.667797 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab
  artifactKey: Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab using Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '43ff1f2d117f30e78190b0c2bb0fe884') in 0.035797 seconds 
========================================================================
Received Import Request.
  Time since last request: 52.313001 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_ModuleSelectEntry.prefab
  artifactKey: Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_ModuleSelectEntry.prefab using Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '159b0a756b10df08dc9ebb3f74258a5f') in 0.004381 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016505 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 7.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.875 seconds
Domain Reload Profiling:
	ReloadAssembly (3875ms)
		BeginReloadAssembly (412ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (17ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (198ms)
		EndReloadAssembly (3355ms)
			LoadAssemblies (260ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (581ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (2363ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1882ms)
				ProcessInitializeOnLoadMethodAttributes (369ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (48ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 276.82 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10811.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 25.160500 ms (FindLiveObjects: 0.903700 ms CreateObjectMapping: 1.120600 ms MarkObjects: 22.527300 ms  DeleteObjects: 0.606900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 240.233665 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'acc0ffd9abc7ad47ce0384c50742d213') in 0.148144 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.458484 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab
  artifactKey: Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab using Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a1e9b8a5fecb5725d7e7c92d7886259f') in 0.004783 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.280435 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_AffixLibrary.prefab
  artifactKey: Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_AffixLibrary.prefab using Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7ea449c62390c9fe9543ab174fe6e540') in 0.007669 seconds 
========================================================================
Received Import Request.
  Time since last request: 27.313578 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_EntryLibrary.prefab
  artifactKey: Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_EntryLibrary.prefab using Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8da5df37a9e2d45ff028e1b9d7f0a154') in 0.006323 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.137458 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Item_EntryLibrary.prefab
  artifactKey: Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Item_EntryLibrary.prefab using Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e79bec43abe7190d2a604e2980c6f9c') in 0.003085 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.257305 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_EntryLibrary.prefab
  artifactKey: Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Panel_EntryLibrary.prefab using Guid(fd3501f2a6ed0a34693062461a8fc4b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '64ebc1fd9dc5664f99f9ebce69df0f1c') in 0.005863 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015927 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.693 seconds
Domain Reload Profiling:
	ReloadAssembly (2694ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2409ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (476ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1635ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1292ms)
				ProcessInitializeOnLoadMethodAttributes (225ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 28.90 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10828.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 29.403300 ms (FindLiveObjects: 0.858700 ms CreateObjectMapping: 0.913400 ms MarkObjects: 27.052300 ms  DeleteObjects: 0.577500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020469 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.027 seconds
Domain Reload Profiling:
	ReloadAssembly (3028ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2773ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (507ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1936ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1541ms)
				ProcessInitializeOnLoadMethodAttributes (279ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 170 unused Assets / (2.0 MB). Loaded Objects now: 10845.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 46.972300 ms (FindLiveObjects: 1.283600 ms CreateObjectMapping: 3.607800 ms MarkObjects: 41.199500 ms  DeleteObjects: 0.880000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 49.88 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10845.
Memory consumption went from 296.8 MB to 294.8 MB.
Total: 30.003800 ms (FindLiveObjects: 1.142300 ms CreateObjectMapping: 1.250500 ms MarkObjects: 26.965900 ms  DeleteObjects: 0.643500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016289 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 11.27 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.916 seconds
Domain Reload Profiling:
	ReloadAssembly (2917ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2644ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (504ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (108ms)
			SetupLoadedEditorAssemblies (1812ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1435ms)
				ProcessInitializeOnLoadMethodAttributes (256ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.61 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10862.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.621100 ms (FindLiveObjects: 1.217400 ms CreateObjectMapping: 1.159600 ms MarkObjects: 33.082900 ms  DeleteObjects: 1.159600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 57.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10862.
Memory consumption went from 296.8 MB to 294.8 MB.
Total: 45.380400 ms (FindLiveObjects: 1.894600 ms CreateObjectMapping: 1.431700 ms MarkObjects: 41.063600 ms  DeleteObjects: 0.971400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 311.653799 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Item_EntryLibrary.prefab
  artifactKey: Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/AffixLibrary/Item_EntryLibrary.prefab using Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4b752696070b725f15189b9c08fb4712') in 0.052746 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.573023 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab
  artifactKey: Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Item_Entry.prefab using Guid(0c0343d7b4274a44cbad96cd88522b55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ef104bc60ae065dc208c8e5a61363697') in 0.007163 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015730 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.680 seconds
Domain Reload Profiling:
	ReloadAssembly (2682ms)
		BeginReloadAssembly (164ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2422ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (477ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1644ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1303ms)
				ProcessInitializeOnLoadMethodAttributes (228ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 33.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10879.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 35.278700 ms (FindLiveObjects: 1.614300 ms CreateObjectMapping: 1.386400 ms MarkObjects: 31.471900 ms  DeleteObjects: 0.804800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021409 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.81 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.696 seconds
Domain Reload Profiling:
	ReloadAssembly (2697ms)
		BeginReloadAssembly (179ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2414ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (470ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1630ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1282ms)
				ProcessInitializeOnLoadMethodAttributes (235ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 41.30 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10896.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.196800 ms (FindLiveObjects: 1.505500 ms CreateObjectMapping: 2.108400 ms MarkObjects: 31.761000 ms  DeleteObjects: 0.820800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 53.10 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10896.
Memory consumption went from 296.8 MB to 294.8 MB.
Total: 36.731900 ms (FindLiveObjects: 1.109700 ms CreateObjectMapping: 1.273000 ms MarkObjects: 33.499200 ms  DeleteObjects: 0.847300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 51.46 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10896.
Memory consumption went from 296.8 MB to 294.8 MB.
Total: 34.267100 ms (FindLiveObjects: 1.023200 ms CreateObjectMapping: 1.731500 ms MarkObjects: 30.730900 ms  DeleteObjects: 0.779800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 210.528423 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cf4b23cce5d2cc804c868bf1075f0a82') in 0.139749 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015862 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.24 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.685 seconds
Domain Reload Profiling:
	ReloadAssembly (2686ms)
		BeginReloadAssembly (164ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2427ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (455ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1681ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1333ms)
				ProcessInitializeOnLoadMethodAttributes (220ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.86 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10913.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 34.271300 ms (FindLiveObjects: 1.101000 ms CreateObjectMapping: 1.054900 ms MarkObjects: 31.216000 ms  DeleteObjects: 0.897500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 41.42 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10913.
Memory consumption went from 296.9 MB to 294.8 MB.
Total: 24.492100 ms (FindLiveObjects: 1.008000 ms CreateObjectMapping: 1.060900 ms MarkObjects: 21.805200 ms  DeleteObjects: 0.616900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 65.34 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10913.
Memory consumption went from 296.9 MB to 294.8 MB.
Total: 128.607900 ms (FindLiveObjects: 3.202300 ms CreateObjectMapping: 1.381300 ms MarkObjects: 123.023800 ms  DeleteObjects: 0.999000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 56459.069452 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床_选择材料.png
  artifactKey: Guid(85c887bfadf190f4793c270c4d5ee40c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床_选择材料.png using Guid(85c887bfadf190f4793c270c4d5ee40c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '623a744a825abf4a7cb213a29dab3c9f') in 0.327902 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.854752 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床_选择材料.png
  artifactKey: Guid(85c887bfadf190f4793c270c4d5ee40c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床_选择材料.png using Guid(85c887bfadf190f4793c270c4d5ee40c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bd5af9946f81cf96a72aeae6674aba7') in 0.568163 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.619581 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床.png
  artifactKey: Guid(dafba24ede692a642ab51e06fad8dbd7) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床.png using Guid(dafba24ede692a642ab51e06fad8dbd7) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7e4fd84d761c76d6fc358cee532ef86d') in 0.090350 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.418764 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床.png
  artifactKey: Guid(dafba24ede692a642ab51e06fad8dbd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/装备车床.png using Guid(dafba24ede692a642ab51e06fad8dbd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b60b19c573063fa6d6fbc0fe8184462') in 0.115288 seconds 
========================================================================
Received Import Request.
  Time since last request: 12.546869 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/材料选择.png
  artifactKey: Guid(09f30115ccb719948b08ab1279a259c6) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/材料选择.png using Guid(09f30115ccb719948b08ab1279a259c6) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '28b2ac7bdb1c640a10490c47bec34574') in 0.085625 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.388721 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/材料选择.png
  artifactKey: Guid(09f30115ccb719948b08ab1279a259c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/材料选择.png using Guid(09f30115ccb719948b08ab1279a259c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '341358ff849ab4a799a9a17fb3eed0f2') in 0.076985 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.422044 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/提示.png
  artifactKey: Guid(bead5f8ee7c461340af506aae4424742) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/提示.png using Guid(bead5f8ee7c461340af506aae4424742) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f23111a49b669552e4fb1a6e84a3e88e') in 0.093908 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.473243 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/提示.png
  artifactKey: Guid(bead5f8ee7c461340af506aae4424742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/提示.png using Guid(bead5f8ee7c461340af506aae4424742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c1c509840111c3dc803e19bf4a7e5474') in 0.670602 seconds 
========================================================================
Received Import Request.
  Time since last request: 14.440029 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png
  artifactKey: Guid(3ebd166554281e54fb2f27d020b35595) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png using Guid(3ebd166554281e54fb2f27d020b35595) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'a1e66fc3a584067b1ba0fc27df238bd1') in 0.090746 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.418426 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png
  artifactKey: Guid(3ebd166554281e54fb2f27d020b35595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png using Guid(3ebd166554281e54fb2f27d020b35595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '56828219f504e42d7c30a05c9e9ada3d') in 0.093475 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.863626 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png
  artifactKey: Guid(520d10641f7222e478c3139b4cfcd20f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png using Guid(520d10641f7222e478c3139b4cfcd20f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f155ad181153db6d093a3881ff0a7e9d') in 0.087220 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.427151 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png
  artifactKey: Guid(520d10641f7222e478c3139b4cfcd20f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png using Guid(520d10641f7222e478c3139b4cfcd20f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '72672bed518f4eb65a34677a09712049') in 0.075947 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.128113 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png
  artifactKey: Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png using Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '747b79b8d5cdf559992e6cec4a459d2f') in 0.091090 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.427691 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png
  artifactKey: Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png using Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b07f612d1ba7a541c62ef5eb369fd908') in 0.093512 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.323539 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png
  artifactKey: Guid(31656d2137049864e8f71920f0239d5b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png using Guid(31656d2137049864e8f71920f0239d5b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '99b02841f0a3d3eceeb7514a3934eee2') in 0.093917 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.428859 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png
  artifactKey: Guid(31656d2137049864e8f71920f0239d5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png using Guid(31656d2137049864e8f71920f0239d5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e4858bb33e401902535bda56a2bfc79b') in 0.093447 seconds 
========================================================================
Received Import Request.
  Time since last request: 13.662972 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承.png
  artifactKey: Guid(4f3478b6e44be974691a2d6ac155f058) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承.png using Guid(4f3478b6e44be974691a2d6ac155f058) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '42a4184afe0cc7c1b95fdc58b4cb1d35') in 0.094696 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.441563 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承.png
  artifactKey: Guid(4f3478b6e44be974691a2d6ac155f058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承.png using Guid(4f3478b6e44be974691a2d6ac155f058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'de8f3cb83c66f533c394c4b5474753e6') in 0.097072 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.914624 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造共鸣.png
  artifactKey: Guid(65917e0a29e049847833c9336cf760c8) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造共鸣.png using Guid(65917e0a29e049847833c9336cf760c8) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '574674afd9ffde307efc7ed9e26fea14') in 0.085050 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.422785 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造共鸣.png
  artifactKey: Guid(65917e0a29e049847833c9336cf760c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造共鸣.png using Guid(65917e0a29e049847833c9336cf760c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '67ce040af2ac9ee0cb57e55c87335388') in 0.066305 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.038634 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功_无词条.png
  artifactKey: Guid(9c3be7b4802f9ac43a6cd77737afabce) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功_无词条.png using Guid(9c3be7b4802f9ac43a6cd77737afabce) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ba5b1fa700647cfc3f61c94ab466aeb2') in 0.086345 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.424014 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功_无词条.png
  artifactKey: Guid(9c3be7b4802f9ac43a6cd77737afabce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功_无词条.png using Guid(9c3be7b4802f9ac43a6cd77737afabce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '016e0840658fd7214e2c9d8385d35500') in 0.072720 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.968806 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功.png
  artifactKey: Guid(0559f9b52b046b746a0f6c682859bc65) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功.png using Guid(0559f9b52b046b746a0f6c682859bc65) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '97ec7207f233854a781a7870391b7f71') in 0.086052 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.385117 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功.png
  artifactKey: Guid(0559f9b52b046b746a0f6c682859bc65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造成功.png using Guid(0559f9b52b046b746a0f6c682859bc65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f7a40e727587cccb9598785063b70af7') in 0.074967 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.548450 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png
  artifactKey: Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png using Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f1eb2e8a7ce232f9d72b079d9edb2219') in 0.085083 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.423760 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png
  artifactKey: Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png using Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb93f71a00b646755a3f308ee552bc5c') in 0.074916 seconds 
========================================================================
Received Import Request.
  Time since last request: 19.676977 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/当前共鸣.png
  artifactKey: Guid(e3a348ff34d7ec0479af783dfd31ebaa) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/当前共鸣.png using Guid(e3a348ff34d7ec0479af783dfd31ebaa) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '94bea554ff287c7fd83707f9a52b38e7') in 0.083741 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.420165 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/当前共鸣.png
  artifactKey: Guid(e3a348ff34d7ec0479af783dfd31ebaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/当前共鸣.png using Guid(e3a348ff34d7ec0479af783dfd31ebaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1a3da0d35904e31c17549b5f5710b39b') in 0.071752 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.358165 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/词条替换.png
  artifactKey: Guid(270c53e2cd8de3e4a904aee5d0fba0ca) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/词条替换.png using Guid(270c53e2cd8de3e4a904aee5d0fba0ca) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0a3b67cd82929e783d453b34c45849ff') in 0.087984 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.415235 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/词条替换.png
  artifactKey: Guid(270c53e2cd8de3e4a904aee5d0fba0ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/词条替换.png using Guid(270c53e2cd8de3e4a904aee5d0fba0ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8758fbd5f7ac67833f093dc3ff21a0df') in 0.070095 seconds 
========================================================================
Received Import Request.
  Time since last request: 714.890765 seconds.
  path: Assets/AssetBundle/UI2/Forms/Chat/GenderIcon/gender_1.png
  artifactKey: Guid(7676c2a03e12c29438a2822214d0be2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Chat/GenderIcon/gender_1.png using Guid(7676c2a03e12c29438a2822214d0be2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2d00f5a4db16f6c9d9e26dde580d544a') in 0.019760 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018053 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.68 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.288 seconds
Domain Reload Profiling:
	ReloadAssembly (3289ms)
		BeginReloadAssembly (319ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (198ms)
		EndReloadAssembly (2866ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (676ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1847ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1448ms)
				ProcessInitializeOnLoadMethodAttributes (274ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.46 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10955.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.977200 ms (FindLiveObjects: 1.141700 ms CreateObjectMapping: 1.404300 ms MarkObjects: 33.546700 ms  DeleteObjects: 0.882900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 219.689705 seconds.
  path: Assets/SDK/MeshBaker/Examples/CharacterCustomization/paintwars_gameassets.fbx
  artifactKey: Guid(33dc55d6aa48f95498362c733e434014) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SDK/MeshBaker/Examples/CharacterCustomization/paintwars_gameassets.fbx using Guid(33dc55d6aa48f95498362c733e434014) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ad69edd3b71e86e00798b1a1ada2910c') in 0.487366 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.148204 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay
  artifactKey: Guid(c104383f172b5c34f968e88b33a5c291) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay using Guid(c104383f172b5c34f968e88b33a5c291) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5629a0b86b75b0bd465831c88e326e4e') in 0.002348 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.137823 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay/BaoxiangAnimation.prefab
  artifactKey: Guid(ef6aceae0ac70ee4aa7db9d40b83ae15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay/BaoxiangAnimation.prefab using Guid(ef6aceae0ac70ee4aa7db9d40b83ae15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '22aa7fc6ff11bdced443044d0ad2fc0c') in 0.494063 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.321720 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay/DisplayRawImage
  artifactKey: Guid(08951140b786b854f9fb53116669b012) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay/DisplayRawImage using Guid(08951140b786b854f9fb53116669b012) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a8ac38a4c7579cfe828f73106539d132') in 0.002153 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016061 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.30 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.780 seconds
Domain Reload Profiling:
	ReloadAssembly (2780ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2508ms)
			LoadAssemblies (114ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (475ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1708ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1356ms)
				ProcessInitializeOnLoadMethodAttributes (240ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 23.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11028.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.840300 ms (FindLiveObjects: 0.878800 ms CreateObjectMapping: 0.843400 ms MarkObjects: 19.534600 ms  DeleteObjects: 0.582500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016528 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.29 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.731 seconds
Domain Reload Profiling:
	ReloadAssembly (2732ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2457ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (496ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (107ms)
			SetupLoadedEditorAssemblies (1626ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1278ms)
				ProcessInitializeOnLoadMethodAttributes (226ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (29ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 47.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11045.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 43.758000 ms (FindLiveObjects: 1.374200 ms CreateObjectMapping: 1.316100 ms MarkObjects: 40.296600 ms  DeleteObjects: 0.769200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 512.094915 seconds.
  path: Assets/AssetBundle/prefab/object_prefab
  artifactKey: Guid(4c9d41ebf24518c40b029b68665ff5de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab using Guid(4c9d41ebf24518c40b029b68665ff5de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2ead07da6de9c7de3e8d07661016b908') in 0.017680 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.587586 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab
  artifactKey: Guid(a7c1bd5a056b6a14d9ad2b41176a7552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab using Guid(a7c1bd5a056b6a14d9ad2b41176a7552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ed100abd92f1d83a8b167554e67f539') in 0.001198 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.118902 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen01.prefab
  artifactKey: Guid(cdf44cc72f821154fbf64c10cc1f107e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen01.prefab using Guid(cdf44cc72f821154fbf64c10cc1f107e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0f1bee5b6ec0e3be339de6cb47d6cbab') in 0.436944 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.055664 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door.prefab
  artifactKey: Guid(23e88668587d4bc41bf04600de800110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door.prefab using Guid(23e88668587d4bc41bf04600de800110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd756c67e13a37ef002ba6d710e1ca366') in 0.029629 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_yiyuan01_dianti01.prefab
  artifactKey: Guid(f28de2d168e3bc34f83a6e34cfd44592) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_yiyuan01_dianti01.prefab using Guid(f28de2d168e3bc34f83a6e34cfd44592) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '17eb718d30b708883ae3eeec16dfdbcb') in 0.384438 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chat_icon.prefab
  artifactKey: Guid(26524487348b1a549828080bf7251a15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chat_icon.prefab using Guid(26524487348b1a549828080bf7251a15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '46563f8b540f03b1b96b0df09f7e7651') in 0.023385 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot02.prefab
  artifactKey: Guid(5c9aa9688ada35845a97ee085d76890e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot02.prefab using Guid(5c9aa9688ada35845a97ee085d76890e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dd7d899c7985ed19567de507a2c2b531') in 0.005479 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtaimen.prefab
  artifactKey: Guid(a69b5bc278ce14641b2725ca30061b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtaimen.prefab using Guid(a69b5bc278ce14641b2725ca30061b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '341e69ee18c2ad78700e3ef82a68240b') in 0.396529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop.prefab
  artifactKey: Guid(745919c3975483c44a5b3befb561d08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop.prefab using Guid(745919c3975483c44a5b3befb561d08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd700010490d384a1d97b7c9c4c09c67f') in 1.878265 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_bingzhu.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_bingzhu.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '459bfcb81d03e532e9bc17355e704ccf') in 0.027037 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop2.prefab
  artifactKey: Guid(a03ea8337c110664a84c855cd01c46ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop2.prefab using Guid(a03ea8337c110664a84c855cd01c46ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f2c01102f81ab7175e977eda7e139cca') in 0.138776 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chuangtougui.prefab
  artifactKey: Guid(5f2024734158ed84689bd4e24dfed68d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chuangtougui.prefab using Guid(5f2024734158ed84689bd4e24dfed68d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '451006615d6ea1f9c5f11374bb8a56bd') in 0.050726 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_01.prefab
  artifactKey: Guid(f5ed01e6041f91346ad817df652a20ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_01.prefab using Guid(f5ed01e6041f91346ad817df652a20ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'da30082590f8739b046d784b1f96ca34') in 0.019484 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dengshanbao01.prefab
  artifactKey: Guid(85a8a86c7be8524479475de498a47837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dengshanbao01.prefab using Guid(85a8a86c7be8524479475de498a47837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f36cdd284fcbffab2c79a77fdb7bb0d3') in 0.018031 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot03.prefab
  artifactKey: Guid(98f64b60e2750434fb5e4fce7151ad76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot03.prefab using Guid(98f64b60e2750434fb5e4fce7151ad76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '01253dd1ad00d8501515857b9d4f85fb') in 0.005829 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point02.prefab
  artifactKey: Guid(d487e670e7646c34a809721d0201ed06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point02.prefab using Guid(d487e670e7646c34a809721d0201ed06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0d4b9b479c27119c24e5e22d959db9f2') in 0.014000 seconds 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 87.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11122.
Memory consumption went from 305.8 MB to 303.7 MB.
Total: 46.664600 ms (FindLiveObjects: 1.760200 ms CreateObjectMapping: 1.704800 ms MarkObjects: 41.619900 ms  DeleteObjects: 1.576900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 28.749109 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot02.prefab
  artifactKey: Guid(5c9aa9688ada35845a97ee085d76890e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot02.prefab using Guid(5c9aa9688ada35845a97ee085d76890e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '04cc6fdf7a6052c49c4986052ae125be') in 0.035335 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtaimen.prefab
  artifactKey: Guid(a69b5bc278ce14641b2725ca30061b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtaimen.prefab using Guid(a69b5bc278ce14641b2725ca30061b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aec12d9a07faf57976db5af20954e998') in 0.026316 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtai.prefab
  artifactKey: Guid(e7488c5c172e9e741843b5d678eccad3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dixiashiyanchang_pingtai.prefab using Guid(e7488c5c172e9e741843b5d678eccad3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e60b2f98ec00d06754a2807e53d8fa9') in 0.045546 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop.prefab
  artifactKey: Guid(745919c3975483c44a5b3befb561d08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop.prefab using Guid(745919c3975483c44a5b3befb561d08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f722a10d3e71d719b339551748d41ffa') in 0.117811 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point01.prefab
  artifactKey: Guid(814c35fddcf7f284e88928218b5f7186) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point01.prefab using Guid(814c35fddcf7f284e88928218b5f7186) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41bbe5499d4a54b4feadf6d03c4f558b') in 0.017737 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_shandong01_pingtai.prefab
  artifactKey: Guid(4ba59b53b2af20c49926d923b8926060) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_shandong01_pingtai.prefab using Guid(4ba59b53b2af20c49926d923b8926060) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ed512a8c585443cf829dc5b1302274c5') in 0.023783 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi01_02.prefab
  artifactKey: Guid(a4b429612228de74faf436676e0b8b83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi01_02.prefab using Guid(a4b429612228de74faf436676e0b8b83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f1007b48caa822e28606313dabbe4d9f') in 0.023311 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_attach.prefab
  artifactKey: Guid(cf7fff47b3cf0454d9bb7cd847b2e10b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_attach.prefab using Guid(cf7fff47b3cf0454d9bb7cd847b2e10b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fc7f9f93604895bb857e9221f5e7b93c') in 0.016849 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000277 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi02_02.prefab
  artifactKey: Guid(ca788d87d6a35da4a8a32832b58c3246) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi02_02.prefab using Guid(ca788d87d6a35da4a8a32832b58c3246) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a3cfad3d3b44e149be1204e80449da18') in 0.023648 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot01.prefab
  artifactKey: Guid(fcb35a3523ecb21449065817df4c4237) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_spot01.prefab using Guid(fcb35a3523ecb21449065817df4c4237) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2c55f9de3ff5880d6db8b4d14292b97d') in 0.007373 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop2.prefab
  artifactKey: Guid(a03ea8337c110664a84c855cd01c46ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_airdrop2.prefab using Guid(a03ea8337c110664a84c855cd01c46ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41dae7a2ebb869603e28b12df4c17ce0') in 0.131734 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_01.prefab
  artifactKey: Guid(f5ed01e6041f91346ad817df652a20ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_01.prefab using Guid(f5ed01e6041f91346ad817df652a20ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '83a4284e2cd3dabb46ba6163a547fc3e') in 0.017543 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_02.prefab
  artifactKey: Guid(ef23d377b9eca9e4985b96feb224d0ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_baoxiangui_02.prefab using Guid(ef23d377b9eca9e4985b96feb224d0ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '285fd332be9f4f5c6543b2d212deefb0') in 0.028497 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point02.prefab
  artifactKey: Guid(d487e670e7646c34a809721d0201ed06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_light_point02.prefab using Guid(d487e670e7646c34a809721d0201ed06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '338bfb8d6c83d6d8d3d421c38e14da9f') in 0.017396 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.630080 seconds.
  path: Assets/AssetBundle/Model/BombPreview.prefab
  artifactKey: Guid(9195436572928234e88dc4dce886c7d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/BombPreview.prefab using Guid(9195436572928234e88dc4dce886c7d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4ab752d4c682248895b2e8768f5a9e0') in 0.023853 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/Model/nullWeapon.prefab
  artifactKey: Guid(4a3e8fa61c5241d4e8edac91cf8b749f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/nullWeapon.prefab using Guid(4a3e8fa61c5241d4e8edac91cf8b749f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b10c946af1e8cc4a58a5c112ff80c7c5') in 0.005216 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/Model/Sergeant@tps_01_skin_show.prefab
  artifactKey: Guid(0a04873150ac3ab40aa50644e2ac741f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/Sergeant@tps_01_skin_show.prefab using Guid(0a04873150ac3ab40aa50644e2ac741f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2c675264f99aaf9e1dca16153a7cfc89') in 0.273943 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/AssetBundle/Model/FPSControllerRoot.prefab
  artifactKey: Guid(d65732d7e7f4c1e4aacaefe413554637) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/FPSControllerRoot.prefab using Guid(d65732d7e7f4c1e4aacaefe413554637) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9a11ee7acae5e661ec8b246d89ccc9cf') in 0.033680 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/Model/Sergeant@tps_01_skin_front_2.prefab
  artifactKey: Guid(11252fb08108c8e419a120318d429770) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/Sergeant@tps_01_skin_front_2.prefab using Guid(11252fb08108c8e419a120318d429770) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '183a78204c8796481631b556d242badd') in 1.600664 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016673 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.12 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.202 seconds
Domain Reload Profiling:
	ReloadAssembly (3203ms)
		BeginReloadAssembly (180ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2913ms)
			LoadAssemblies (131ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (506ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (114ms)
			SetupLoadedEditorAssemblies (2049ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (102ms)
				ProcessInitializeOnLoadAttributes (1658ms)
				ProcessInitializeOnLoadMethodAttributes (265ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 34.90 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11105.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 48.726100 ms (FindLiveObjects: 1.497400 ms CreateObjectMapping: 1.594700 ms MarkObjects: 44.906100 ms  DeleteObjects: 0.726500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.022075 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.63 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.694 seconds
Domain Reload Profiling:
	ReloadAssembly (2695ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2427ms)
			LoadAssemblies (115ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (492ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1609ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1255ms)
				ProcessInitializeOnLoadMethodAttributes (233ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 34.89 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11122.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.233100 ms (FindLiveObjects: 1.209500 ms CreateObjectMapping: 1.137300 ms MarkObjects: 32.943300 ms  DeleteObjects: 0.941400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 65.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11122.
Memory consumption went from 305.4 MB to 303.4 MB.
Total: 38.088500 ms (FindLiveObjects: 1.260100 ms CreateObjectMapping: 2.346500 ms MarkObjects: 33.698000 ms  DeleteObjects: 0.782800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018895 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.900 seconds
Domain Reload Profiling:
	ReloadAssembly (2901ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2626ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (472ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1815ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1430ms)
				ProcessInitializeOnLoadMethodAttributes (259ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 42.70 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11139.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 41.420300 ms (FindLiveObjects: 1.822600 ms CreateObjectMapping: 1.771800 ms MarkObjects: 37.006800 ms  DeleteObjects: 0.817500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 55.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11139.
Memory consumption went from 305.4 MB to 303.4 MB.
Total: 34.027500 ms (FindLiveObjects: 1.145800 ms CreateObjectMapping: 1.228300 ms MarkObjects: 30.213200 ms  DeleteObjects: 1.438900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 738.890491 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '099b48c7c2d2e6e44fba210fba4ef385') in 0.797501 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png
  artifactKey: Guid(31656d2137049864e8f71920f0239d5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_材料不足.png using Guid(31656d2137049864e8f71920f0239d5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c8a9676f5fc39ab8a692842a135987fb') in 0.150769 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png
  artifactKey: Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造保级.png using Guid(9acd7ffebbd3d8641b484aca9867d6f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2674c8862cb1b40f2f69276dcce631b') in 0.089302 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png
  artifactKey: Guid(3ebd166554281e54fb2f27d020b35595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造已达上限.png using Guid(3ebd166554281e54fb2f27d020b35595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ead86fe9611391eee4d57f65524dd3cd') in 0.113071 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png
  artifactKey: Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造继承_可继承.png using Guid(f78d052d90a6a2145a8c095fc0d38a0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '962e40b3634b329743d07bea974622dc') in 0.111990 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png
  artifactKey: Guid(520d10641f7222e478c3139b4cfcd20f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/锻造失败_无词条.png using Guid(520d10641f7222e478c3139b4cfcd20f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6f7bafca6766186d0547e7b90998855d') in 0.093932 seconds 
========================================================================
Received Import Request.
  Time since last request: 19.475875 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '706c7f4a342506057a746e27666eaa33') in 0.018817 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.900509 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture
  artifactKey: Guid(90a999b4de64ed847a2bb34b53ac6bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture using Guid(90a999b4de64ed847a2bb34b53ac6bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6489a6716c88223f84d0b71a1f79f9fb') in 0.002801 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.082079 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/c_prop_wuqichechuangi01_lv1.png
  artifactKey: Guid(946174148e3dbcf4086f8790193f1b0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/c_prop_wuqichechuangi01_lv1.png using Guid(946174148e3dbcf4086f8790193f1b0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '133bfd7536114c75c05bb8e1a8accbc1') in 0.030416 seconds 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 56.24 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 11143.
Memory consumption went from 305.8 MB to 303.8 MB.
Total: 35.380300 ms (FindLiveObjects: 1.158700 ms CreateObjectMapping: 1.353200 ms MarkObjects: 32.035500 ms  DeleteObjects: 0.831700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 21.229249 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/c_prop_wuqichechuangi01_lv1.png
  artifactKey: Guid(946174148e3dbcf4086f8790193f1b0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/c_prop_wuqichechuangi01_lv1.png using Guid(946174148e3dbcf4086f8790193f1b0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '359ca6df4c14a42d317509f3436b8538') in 0.087108 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.643350 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_选中页签.png
  artifactKey: Guid(c66ed3529dc2f3649a2e22b7e18607e5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_选中页签.png using Guid(c66ed3529dc2f3649a2e22b7e18607e5) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '76b3f2ffe12ccae1a9675404ee102cc6') in 0.061908 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.439868 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_未选中页签.png
  artifactKey: Guid(873d749b49e194b4c8c50036ddfbcd38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_未选中页签.png using Guid(873d749b49e194b4c8c50036ddfbcd38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ed11b87116b97b6d8082ec373fe0cc3f') in 0.016152 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.541190 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png
  artifactKey: Guid(f10b434dc0d88e843a234a889491c87e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png using Guid(f10b434dc0d88e843a234a889491c87e) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '384543dd89e85a02a9eb37c0c3f05fb4') in 0.012393 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.468626 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png
  artifactKey: Guid(f10b434dc0d88e843a234a889491c87e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png using Guid(f10b434dc0d88e843a234a889491c87e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b976e027608eb691d01aef373f5c858b') in 0.014658 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.980908 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png
  artifactKey: Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png using Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3de2d06cbdd49efe44538cdb67b29d63') in 0.012150 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.441254 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png
  artifactKey: Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png using Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a9543c14cec854e9b9f96bdab749068b') in 0.012755 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.614845 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png
  artifactKey: Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png using Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'd70a112fa14f2caddb047704edca1777') in 0.012897 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.380601 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png
  artifactKey: Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png using Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '68dce9887319a3f709bd62c57491ab36') in 0.011224 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.533852 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png
  artifactKey: Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png using Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7715ae91495708f58b9a4a3b21ab5f5d') in 0.031479 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.443005 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png
  artifactKey: Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png using Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e95b6c1580ebbb4035e7f00dee601763') in 0.031876 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.342491 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png
  artifactKey: Guid(e3de519afda6aed40a772fc631ac9683) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png using Guid(e3de519afda6aed40a772fc631ac9683) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '5e803f9f1b8b7b60876c9a1469b098f3') in 0.024340 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.468810 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png
  artifactKey: Guid(e3de519afda6aed40a772fc631ac9683) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png using Guid(e3de519afda6aed40a772fc631ac9683) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2bd889481db2d086821c2e36d7ce9789') in 0.015470 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.221503 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png
  artifactKey: Guid(67ece416bc775484194197f6cf09b57c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png using Guid(67ece416bc775484194197f6cf09b57c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'c63c3d47ebfdfc1c11ffe089ca451290') in 0.018439 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.439813 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png
  artifactKey: Guid(67ece416bc775484194197f6cf09b57c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png using Guid(67ece416bc775484194197f6cf09b57c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2a9d5ba99b5efc57f5d3156399fe042f') in 0.017873 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.025184 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png
  artifactKey: Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png using Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '4c6f940f0f8d62782cba9ee5b8f8ce33') in 0.014969 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.454615 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png
  artifactKey: Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png using Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b4547ac9b19a4c23b4713c7f34cfd1e5') in 0.014894 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.911193 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png
  artifactKey: Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png using Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'aa54ab1e7722258ef603b72739bdb915') in 0.013721 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.441828 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png
  artifactKey: Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png using Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f6c9dff7d0ed9a6737cf16a09bdb00c7') in 0.013682 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.234650 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png
  artifactKey: Guid(68be822fae8a1f941a913ff6850fc44c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png using Guid(68be822fae8a1f941a913ff6850fc44c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '8de1961c53558ada94546ee2b7402b88') in 0.094915 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.446706 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png
  artifactKey: Guid(68be822fae8a1f941a913ff6850fc44c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png using Guid(68be822fae8a1f941a913ff6850fc44c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7aea20821ce05f2fa5f7fc57741937b9') in 0.090592 seconds 
========================================================================
Received Import Request.
  Time since last request: 31.089093 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png
  artifactKey: Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png using Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '6370852dc22593915bd081fe31bf4ca1') in 0.013280 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.447290 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png
  artifactKey: Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png using Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '101c47528d2a8136166996d96f4cd63e') in 0.012726 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.692688 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png
  artifactKey: Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png using Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '937d666163b5f89d4659e069d5a5d307') in 0.012624 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.580729 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png
  artifactKey: Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png using Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6db19e2e9978ce104b4aec11eaba1c06') in 0.012673 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.311074 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png
  artifactKey: Guid(e594102f3469a8046aac2d2944dd464f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png using Guid(e594102f3469a8046aac2d2944dd464f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '22ae4f654e820f9ecc5aaf0c6278eb07') in 0.012986 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.439677 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png
  artifactKey: Guid(e594102f3469a8046aac2d2944dd464f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png using Guid(e594102f3469a8046aac2d2944dd464f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c48f494ab60d1c3d907d4848347ddb94') in 0.014235 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.921375 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png
  artifactKey: Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png using Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'c59e8c1b965c15037dcd6cfce4b7f490') in 0.014938 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.446535 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png
  artifactKey: Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png using Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7e5b6f5eb076f2e111a3dc97d75a0df2') in 0.016268 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.656413 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png
  artifactKey: Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png using Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '8e42b014cb6097826f7f398eace5b96a') in 0.029621 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.441222 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png
  artifactKey: Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png using Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ef3e19ded0157c56768c952b2536a11') in 0.026760 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.901366 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png
  artifactKey: Guid(592f4718c50690442a714f1d3dcec602) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png using Guid(592f4718c50690442a714f1d3dcec602) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '65d632c0c56b202e846b02b44baac5aa') in 0.014207 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.441429 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png
  artifactKey: Guid(592f4718c50690442a714f1d3dcec602) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png using Guid(592f4718c50690442a714f1d3dcec602) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e1bddfbd1cc51d0a3f03be07e39c506d') in 0.010936 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.394875 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png
  artifactKey: Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png using Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '1a0475858227b8d2233c728aa926be74') in 0.035248 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.443878 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png
  artifactKey: Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png using Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c8e252cb84b360b6de1ed9986a5c91f4') in 0.028409 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.396151 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png
  artifactKey: Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png using Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '09d24911615c72c2f8633112c741b708') in 0.014659 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.439540 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png
  artifactKey: Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png using Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '75be6011188c578dd1ed042e516a865c') in 0.012493 seconds 
========================================================================
Received Import Request.
  Time since last request: 3.515628 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png
  artifactKey: Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png using Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '594f6e8cc35bfa109c3d05901706ba2e') in 0.011560 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.438632 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png
  artifactKey: Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png using Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '86d07d89451d1dea89ae932a22d1527f') in 0.011540 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.925169 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png
  artifactKey: Guid(c540e2ac3590a3a4584d88b915263444) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png using Guid(c540e2ac3590a3a4584d88b915263444) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'e485e8f33e745ef3150138891b4029b5') in 0.014309 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.442220 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png
  artifactKey: Guid(c540e2ac3590a3a4584d88b915263444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png using Guid(c540e2ac3590a3a4584d88b915263444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '08e637ac502a1137fb7ca85a9039576f') in 0.012069 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.196411 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png
  artifactKey: Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png using Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '1e90467f35121e1137ea44e91fe85134') in 0.013569 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.445953 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png
  artifactKey: Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png using Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6a5cae19f97bf4d3d0b575563e2af974') in 0.013133 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.023170 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png
  artifactKey: Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png using Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ee20867a28d02d4d5f5f504820e77fae') in 0.013012 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.437169 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png
  artifactKey: Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png using Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4622949f27c3198b0f1e042dedfbff27') in 0.014198 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.121916 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png
  artifactKey: Guid(78865da1613145643b25155112f71383) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png using Guid(78865da1613145643b25155112f71383) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '168091eb2b6a5086fd5a8259dbb270d1') in 0.027832 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.449602 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png
  artifactKey: Guid(78865da1613145643b25155112f71383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png using Guid(78865da1613145643b25155112f71383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2280de59e27abaeab4cb15392792bce3') in 0.022851 seconds 
========================================================================
Received Import Request.
  Time since last request: 9.042051 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png
  artifactKey: Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png using Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9531b5671a82f498751d58d9c4921f02') in 0.012689 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.438861 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png
  artifactKey: Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png using Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e98dc46e9748413e137715e300b7ce3f') in 0.015454 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.318923 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png
  artifactKey: Guid(119e90e156b51e949b2819759fe32677) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png using Guid(119e90e156b51e949b2819759fe32677) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '23880ad1644d01cedc7459b76c6191fa') in 0.011969 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.447181 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png
  artifactKey: Guid(119e90e156b51e949b2819759fe32677) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png using Guid(119e90e156b51e949b2819759fe32677) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e3ca30d7569d101027bd07cd29745921') in 0.016102 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.705696 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png
  artifactKey: Guid(11f679260a52f874996c5c1048403a37) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png using Guid(11f679260a52f874996c5c1048403a37) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '192d7214cbd9a0bd2aac92abcf0e4372') in 0.011872 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.453158 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png
  artifactKey: Guid(11f679260a52f874996c5c1048403a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png using Guid(11f679260a52f874996c5c1048403a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4d9ba567072542b48666234267302350') in 0.012573 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.881960 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png
  artifactKey: Guid(74ece5bb61de54a44be3ae46229114f8) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png using Guid(74ece5bb61de54a44be3ae46229114f8) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '65c6d8ca941c8f253cb520031565142f') in 0.012212 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.547284 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png
  artifactKey: Guid(74ece5bb61de54a44be3ae46229114f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png using Guid(74ece5bb61de54a44be3ae46229114f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4dce290f429ec5fac1de7c9f4ae5ca6') in 0.011300 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.770187 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png
  artifactKey: Guid(76f8d57033545074a9d851cb490880c4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png using Guid(76f8d57033545074a9d851cb490880c4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7740f374759c7e531eb87a544f419573') in 0.012835 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.439805 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png
  artifactKey: Guid(76f8d57033545074a9d851cb490880c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png using Guid(76f8d57033545074a9d851cb490880c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f20e059206276d10c44deaf524008d2') in 0.012471 seconds 
========================================================================
Received Import Request.
  Time since last request: 290.502557 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bcae0e57a33092d854c1b2adba9c1e2e') in 0.020815 seconds 
========================================================================
Received Import Request.
  Time since last request: 394.832302 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3fec34f9c003352afc93f201b271bcdf') in 0.019813 seconds 
========================================================================
Received Import Request.
  Time since last request: 31.625078 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_green5.png
  artifactKey: Guid(e7583f87e5125574b9a7650a4ab0d59f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_green5.png using Guid(e7583f87e5125574b9a7650a4ab0d59f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '618bc18e13daac0f7f20b0faa70a5ffa') in 0.020998 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_grey2.png
  artifactKey: Guid(e858c47f4d86dc94e978025b65ea369d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_grey2.png using Guid(e858c47f4d86dc94e978025b65ea369d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '824cdaf9f91024ae07ddbdb52c51fb23') in 0.018986 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange.png
  artifactKey: Guid(4750a5f8ac56ceb45a91e353c8f5fc5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange.png using Guid(4750a5f8ac56ceb45a91e353c8f5fc5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b34a8bbc8c83ff3fa9d9e2567ad4dc37') in 0.022069 seconds 
========================================================================
Received Import Request.
  Time since last request: 483.812472 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '20a9066144cdeff1604a3f8e3a563b56') in 0.022089 seconds 
========================================================================
Received Import Request.
  Time since last request: 122.448566 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '512063aca750d7abd0a341d1dcab66be') in 0.019429 seconds 
========================================================================
Received Import Request.
  Time since last request: 19.210490 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '09b6d1e42dfbe149df65e77e5d1f906a') in 0.005807 seconds 
========================================================================
Received Import Request.
  Time since last request: 2024.303002 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f331e1ceb06fd197be90cddfc235c6ca') in 0.012567 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.791239 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '20c3237bd9a16314ea4f3779dcbd249d') in 0.026281 seconds 
========================================================================
Received Import Request.
  Time since last request: 49.266606 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '17cbd9ab769a1c9496ce83c5557ef6bb') in 0.006696 seconds 
========================================================================
Received Import Request.
  Time since last request: 211.500979 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff69f5af5677c29d27f198962f032e44') in 0.006773 seconds 
========================================================================
Received Import Request.
  Time since last request: 15.284771 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '10c1aee16cdbf5f7cde03b8f8f46bb5b') in 0.025382 seconds 
========================================================================
Received Import Request.
  Time since last request: 419.027092 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png
  artifactKey: Guid(476858e6952141a42a1147ed260cabf0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png using Guid(476858e6952141a42a1147ed260cabf0) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'd0e740b3a7e7a74107d38d7135117437') in 0.014014 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.652476 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png
  artifactKey: Guid(476858e6952141a42a1147ed260cabf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png using Guid(476858e6952141a42a1147ed260cabf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '52b670cf69b527239ba32053f5698e11') in 0.015921 seconds 
========================================================================
Received Import Request.
  Time since last request: 274.501629 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png
  artifactKey: Guid(c50865357860f2a41beb1c6710959063) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png using Guid(c50865357860f2a41beb1c6710959063) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3707207175c08a1523d212ac50de4701') in 0.013643 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.715322 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png
  artifactKey: Guid(c50865357860f2a41beb1c6710959063) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png using Guid(c50865357860f2a41beb1c6710959063) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd5441076434189177c4d6bde999861ce') in 0.014778 seconds 
========================================================================
Received Import Request.
  Time since last request: 1093.071356 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4c5e55ede050b5dc615cfd7bb5cb9085') in 0.034069 seconds 
========================================================================
Received Import Request.
  Time since last request: 329.029186 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange2.png
  artifactKey: Guid(2da33f425c181a541891190cf49d61e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange2.png using Guid(2da33f425c181a541891190cf49d61e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '60130c9e975c3bffdb21984e23232aca') in 0.029160 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange4.png
  artifactKey: Guid(0661731432531a44791cce3a78c15c35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_orange4.png using Guid(0661731432531a44791cce3a78c15c35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '52fd22bc5224407c5675eb527e7d369d') in 0.013474 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_icon_06.png
  artifactKey: Guid(90f143b8c067934418c75c8518dbbc64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_icon_06.png using Guid(90f143b8c067934418c75c8518dbbc64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7267361f8838dd5332bd0611ad6f4e6') in 0.026528 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_organge6.png
  artifactKey: Guid(7751c6ae245d3d746a8fbc2c5757f69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_organge6.png using Guid(7751c6ae245d3d746a8fbc2c5757f69d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '59323513d8d2fa13bc31b8e74b562e01') in 0.020485 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_tabside.png
  artifactKey: Guid(ff9ae831d31007140b641e7903e3300f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_tabside.png using Guid(ff9ae831d31007140b641e7903e3300f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a243048173d941b8a14a6ccf033a450') in 0.020641 seconds 
========================================================================
Received Import Request.
  Time since last request: 597.300166 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '210c3c31aefc76f9b87c01f31cac85fc') in 0.027312 seconds 
========================================================================
Received Import Request.
  Time since last request: 75.559051 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/event_return.png
  artifactKey: Guid(81b97a34d4fa0624ebefe0083a890a76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/event_return.png using Guid(81b97a34d4fa0624ebefe0083a890a76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6c2bd9ad7d8c9fdefd344dbf7b1ee15c') in 0.021282 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/GiftPack3.png
  artifactKey: Guid(da9c131e897c50a47ae24928110f36b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/GiftPack3.png using Guid(da9c131e897c50a47ae24928110f36b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '58861bf63e32634fa6c0528256782f55') in 0.013977 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/guild_bg.png
  artifactKey: Guid(78cdf208633ae5a43bfa750e2185c81c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/guild_bg.png using Guid(78cdf208633ae5a43bfa750e2185c81c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3c18d642383ac54189ae4ea2de130113') in 0.015197 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/GF-dzp_btn_02.png
  artifactKey: Guid(635745187291f1e4792691b139a7943e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/GF-dzp_btn_02.png using Guid(635745187291f1e4792691b139a7943e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'be0a4192d49642925635c49d005a8bf4') in 0.027755 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/GF-dzp_btn_02_2.png
  artifactKey: Guid(8311468a653996749b341cc2261c8ba9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/GF-dzp_btn_02_2.png using Guid(8311468a653996749b341cc2261c8ba9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '070aadc19283a1029d78b93c09e4faa5') in 0.015491 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.630521 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/guild_pageBg.png
  artifactKey: Guid(ffc4fb96519b5544da4bb1e4e2f405c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/guild_pageBg.png using Guid(ffc4fb96519b5544da4bb1e4e2f405c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'df0676d8923d96cd44d737f7c221564c') in 0.014958 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_chat.png
  artifactKey: Guid(d0c2bc3297ccf0a4193c3390e303bc5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_chat.png using Guid(d0c2bc3297ccf0a4193c3390e303bc5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd4ac9532dc6fc2be2e2334585bf38fba') in 0.014511 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic1.png
  artifactKey: Guid(0d2c573a83190d047bb979d7bd2d9220) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic1.png using Guid(0d2c573a83190d047bb979d7bd2d9220) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e37d39f93aeaaa5d15089b3356bdd246') in 0.012918 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic2.png
  artifactKey: Guid(cd7462e7771ac7e41a0d235fdb9c6c98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_data_traffic2.png using Guid(cd7462e7771ac7e41a0d235fdb9c6c98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a9f79eda22a32d8d9556aaf43cfb3d7') in 0.018724 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.519423 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_1.png
  artifactKey: Guid(070f0d4bf5a2eeb479fbf6257515c7e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_hurt_font_1.png using Guid(070f0d4bf5a2eeb479fbf6257515c7e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a0ab6860c4b67a881624648f7fcdacc5') in 0.015729 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_ charging.png
  artifactKey: Guid(3c9b5adf3d6b82e488f0438e59e0312f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_ charging.png using Guid(3c9b5adf3d6b82e488f0438e59e0312f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c77b9e9d5cde240fab4ef970e2305b9c') in 0.013328 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_signal_05.png
  artifactKey: Guid(177c8afff192dd547970e1e501eb9cb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_signal_05.png using Guid(177c8afff192dd547970e1e501eb9cb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '22e01ad87b4547161e116cdeb92e27ad') in 0.016754 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi1.png
  artifactKey: Guid(be03d17c461b68d4ab7bc220bf864708) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi1.png using Guid(be03d17c461b68d4ab7bc220bf864708) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2b6bafcbbf4393f07e9a2c5a4393581d') in 0.012767 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_block.png
  artifactKey: Guid(23f0169a71552dd49ace9ac1b51f3cdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_power_block.png using Guid(23f0169a71552dd49ace9ac1b51f3cdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b08a00e09fc6b26101974c832ecb37a3') in 0.012206 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.268246 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi2.png
  artifactKey: Guid(4efe9c8c5ba24204b90392c9bade6d77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi2.png using Guid(4efe9c8c5ba24204b90392c9bade6d77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2a26defe17c63d7084bd62a3b35e1cd9') in 0.011928 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/lockmask.png
  artifactKey: Guid(94bf1210fbc5e2a4ba022045f6586504) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/lockmask.png using Guid(94bf1210fbc5e2a4ba022045f6586504) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3399111b935a207304f752aa2f9d6bad') in 0.016051 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_03.png
  artifactKey: Guid(33c4a4391989f794c9b60da7bfc749a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_03.png using Guid(33c4a4391989f794c9b60da7bfc749a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd4cbb133317f6f9955c998f0dcdfd681') in 0.020308 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_02.png
  artifactKey: Guid(55e35ce973110eb41be3674eabc4fae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_02.png using Guid(55e35ce973110eb41be3674eabc4fae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cc99a251b4ea2d01805427d0d9b276b4') in 0.013739 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_04.png
  artifactKey: Guid(8be0e35b8b99ce440bfa99fb4c610d94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/icon_wifi_04.png using Guid(8be0e35b8b99ce440bfa99fb4c610d94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e8dfe5cd04d410e03a43e562c23564f') in 0.011464 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.333667 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/lt_img_02.png
  artifactKey: Guid(fb321398981cb9a4aafcac4f6289ffdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/lt_img_02.png using Guid(fb321398981cb9a4aafcac4f6289ffdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fde8e5f5de42c5bb1574bd7e8bb6d8c2') in 0.022156 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/pass_font_bottom.png
  artifactKey: Guid(7df81f0388ed24c4cba757382fe1228a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/pass_font_bottom.png using Guid(7df81f0388ed24c4cba757382fe1228a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '413b517d88409a10140c5211fbb9fc98') in 0.015413 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/main_redpoint.png
  artifactKey: Guid(729f89e76a53f3442b3c119ea3119daf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/main_redpoint.png using Guid(729f89e76a53f3442b3c119ea3119daf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '78e6cad26ac9427956f4bfdb196c884e') in 0.011667 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/main_tou_bg.png
  artifactKey: Guid(68c089d3b87b2f14a97c3a5dd6964858) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/main_tou_bg.png using Guid(68c089d3b87b2f14a97c3a5dd6964858) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ad1efa4a13ef505755d182eaab1d977d') in 0.014882 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/mrjh_bg_02.png
  artifactKey: Guid(e5c8a3ccba8757149b1aa990d0d2ef7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/mrjh_bg_02.png using Guid(e5c8a3ccba8757149b1aa990d0d2ef7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1e05ecc7fb169ce287105c7a7440c001') in 0.030868 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.273514 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/progress_fill_green.png
  artifactKey: Guid(1f9e47ec85b89e144a680e3f9bb52bb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/progress_fill_green.png using Guid(1f9e47ec85b89e144a680e3f9bb52bb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '801946775f4b6fc05a5f412aad024ede') in 0.015224 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/sc_sce_01.png
  artifactKey: Guid(844f8d4c3ecb1ef43a23cf4e8358a893) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/sc_sce_01.png using Guid(844f8d4c3ecb1ef43a23cf4e8358a893) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2618e2f081f6659e8cea3d8f1e38dc12') in 0.021885 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_fire_tip.png
  artifactKey: Guid(17e2c021793167347872ce7655fa8bc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_fire_tip.png using Guid(17e2c021793167347872ce7655fa8bc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3d7a5582a2f206851b01ef49fdb55cb0') in 0.015686 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/rl_bg_04.png
  artifactKey: Guid(9b86c82e91137df4fb32efdbde3dddc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/rl_bg_04.png using Guid(9b86c82e91137df4fb32efdbde3dddc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '51a53fb0bae8fcafa43e9b81ce4ef25f') in 0.016455 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_safe_tip.png
  artifactKey: Guid(4aca230e4f4a77d4cb672b22a4829c3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/pvp_safe_tip.png using Guid(4aca230e4f4a77d4cb672b22a4829c3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd9aa3b595d878447591848c8f92232d2') in 0.012194 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.243391 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/shop_top_bottom.png
  artifactKey: Guid(9d13dfd731cd4264799aeafe9b62e509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/shop_top_bottom.png using Guid(9d13dfd731cd4264799aeafe9b62e509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fba89b87b8c3a6f281eae625dab4e1a1') in 0.017722 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/tip_red.png
  artifactKey: Guid(5da40305b1e36cc4a893dd558db6f486) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/tip_red.png using Guid(5da40305b1e36cc4a893dd558db6f486) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6b4d3e107c1ae40dc8531cfc5c3b483') in 0.016450 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/Task_Lock.png
  artifactKey: Guid(0f98484095391d449b462f8b2302cedb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/Task_Lock.png using Guid(0f98484095391d449b462f8b2302cedb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '92ec2bbf00b845cb4e9c18a4b32d019e') in 0.019019 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/tab_nochoose.png
  artifactKey: Guid(96fff3e7a828cbd4385db170fdabf0eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/tab_nochoose.png using Guid(96fff3e7a828cbd4385db170fdabf0eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '88f3da861c5464978d43e6fb4f834395') in 0.013783 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/tab_choose.png
  artifactKey: Guid(2169925dace292e458a644d0ceb9db29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/tab_choose.png using Guid(2169925dace292e458a644d0ceb9db29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '619a240636e84032ed681f5ea634a64f') in 0.014366 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.665561 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/upgrade_base.png
  artifactKey: Guid(b24bdb7083ce4d940a90c9c5031508c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/upgrade_base.png using Guid(b24bdb7083ce4d940a90c9c5031508c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f98c55f4505ef9e35112253fb7e18c0') in 0.024958 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/Common/zzjh_img_03.png
  artifactKey: Guid(04fa1b08387e5df458ac63b1a4de8495) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/Common/zzjh_img_03.png using Guid(04fa1b08387e5df458ac63b1a4de8495) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2210f402b0a37cc5ea5bb8f3b6dbd509') in 0.020327 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016328 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 13.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.325 seconds
Domain Reload Profiling:
	ReloadAssembly (3326ms)
		BeginReloadAssembly (375ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (18ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (215ms)
		EndReloadAssembly (2833ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (585ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (110ms)
			SetupLoadedEditorAssemblies (1849ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (14ms)
				BeforeProcessingInitializeOnLoad (107ms)
				ProcessInitializeOnLoadAttributes (1415ms)
				ProcessInitializeOnLoadMethodAttributes (296ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (35ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.75 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 11303.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 35.636400 ms (FindLiveObjects: 1.561900 ms CreateObjectMapping: 1.377500 ms MarkObjects: 31.887100 ms  DeleteObjects: 0.808500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 63.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11303.
Memory consumption went from 305.7 MB to 303.7 MB.
Total: 39.831000 ms (FindLiveObjects: 3.644600 ms CreateObjectMapping: 1.906500 ms MarkObjects: 32.958700 ms  DeleteObjects: 1.319800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019908 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.51 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.017 seconds
Domain Reload Profiling:
	ReloadAssembly (3018ms)
		BeginReloadAssembly (181ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (80ms)
		EndReloadAssembly (2731ms)
			LoadAssemblies (115ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (500ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (111ms)
			SetupLoadedEditorAssemblies (1876ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1487ms)
				ProcessInitializeOnLoadMethodAttributes (272ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 30.87 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11320.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 33.621400 ms (FindLiveObjects: 1.238000 ms CreateObjectMapping: 1.428800 ms MarkObjects: 29.735000 ms  DeleteObjects: 1.217100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016400 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.06 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.837 seconds
Domain Reload Profiling:
	ReloadAssembly (2838ms)
		BeginReloadAssembly (161ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2568ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (489ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (107ms)
			SetupLoadedEditorAssemblies (1719ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1356ms)
				ProcessInitializeOnLoadMethodAttributes (248ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (37ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 36.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11337.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.834600 ms (FindLiveObjects: 1.146300 ms CreateObjectMapping: 1.496500 ms MarkObjects: 33.294300 ms  DeleteObjects: 0.896300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.026830 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.49 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.718 seconds
Domain Reload Profiling:
	ReloadAssembly (2718ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2450ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (481ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1638ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1273ms)
				ProcessInitializeOnLoadMethodAttributes (247ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 43.78 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11354.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 40.910100 ms (FindLiveObjects: 1.477500 ms CreateObjectMapping: 1.383200 ms MarkObjects: 37.285600 ms  DeleteObjects: 0.762400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1210.509790 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a7157adc366f5814799e5fc5014936ad') in 0.076724 seconds 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 54.65 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 11354.
Memory consumption went from 306.4 MB to 304.4 MB.
Total: 37.565700 ms (FindLiveObjects: 1.348000 ms CreateObjectMapping: 1.322100 ms MarkObjects: 34.115600 ms  DeleteObjects: 0.778700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 18.161125 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '76def83b52f429f1235a5f6ea8575e81') in 0.035117 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cc1ac034818bfa26753d7d6abcec548f') in 0.023168 seconds 
========================================================================
Received Import Request.
  Time since last request: 6853.310567 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png
  artifactKey: Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_材料底.png using Guid(8cf88284c48368c49893a10dcf43f7c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e57db3286a2b00ef180c776739b0ea89') in 0.088923 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png
  artifactKey: Guid(78865da1613145643b25155112f71383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造成功底.png using Guid(78865da1613145643b25155112f71383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3629ac3b24ca502cd62f0622683a8e44') in 0.033835 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png
  artifactKey: Guid(68be822fae8a1f941a913ff6850fc44c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床bg.png using Guid(68be822fae8a1f941a913ff6850fc44c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '83c247a81903962a4fc362a8713ea5d4') in 0.183828 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png
  artifactKey: Guid(11f679260a52f874996c5c1048403a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换底.png using Guid(11f679260a52f874996c5c1048403a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b7fa0a3659001c79fd8d71a66bf14dcb') in 0.012509 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png
  artifactKey: Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造失败底.png using Guid(cd220fe3a12c3f54fb9aef816b05bae9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03d64a4e8e0e2ec5c10479efe8452269') in 0.031336 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png
  artifactKey: Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/阴影.png using Guid(f8ea33bf920ce484d842fe0d5a3ac6a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '734dfdfc42b913dc01e3c53ba566b4d0') in 0.024977 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png
  artifactKey: Guid(c540e2ac3590a3a4584d88b915263444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_灰.png using Guid(c540e2ac3590a3a4584d88b915263444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8719b369911e46a40e43931b61a0ccd6') in 0.017972 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.966825 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png
  artifactKey: Guid(e594102f3469a8046aac2d2944dd464f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/已达上限底.png using Guid(e594102f3469a8046aac2d2944dd464f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a89ac96288db1e4d5554df6e7954660') in 0.019187 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png
  artifactKey: Guid(592f4718c50690442a714f1d3dcec602) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_红.png using Guid(592f4718c50690442a714f1d3dcec602) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6ac01918608df53b5606120e918f0508') in 0.017218 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png
  artifactKey: Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_短.png using Guid(7f360839eb06b0d4795b6de765ce34f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e3225d42ff554e85a538138533ae7e85') in 0.016516 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备剪影_甲.png
  artifactKey: Guid(7a1526ebe4724b9488a65a9b524af352) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备剪影_甲.png using Guid(7a1526ebe4724b9488a65a9b524af352) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4fb79757ead5ffaa747dcdd917923a4d') in 0.027022 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png
  artifactKey: Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_长.png using Guid(e4cb561ccbcf9de44850b94e3db8ff37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '811b5e0296eecd48c2bb97c703000ddd') in 0.018292 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.242389 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png
  artifactKey: Guid(476858e6952141a42a1147ed260cabf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/bb_icon_共鸣.png using Guid(476858e6952141a42a1147ed260cabf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '15f72d8805c23b587351177f61554c46') in 0.014610 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png
  artifactKey: Guid(76f8d57033545074a9d851cb490880c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/对号_大.png using Guid(76f8d57033545074a9d851cb490880c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dd20c66489374b9e275e498757c881fc') in 0.013807 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png
  artifactKey: Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/右侧属性信息底.png using Guid(e13acbdfd047b644b8c74bf9441f5284) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5fa4a16d2ff01f1436dbc9b9bdf2cabb') in 0.043272 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0