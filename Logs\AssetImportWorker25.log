Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker25
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker25.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [51832] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 432579214 [EditorId] 432579214 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [51832] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 432579214 [EditorId] 432579214 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 924.52 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56156
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001944 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 835.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.456 seconds
Domain Reload Profiling:
	ReloadAssembly (1457ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1253ms)
			LoadAssemblies (73ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (118ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (1044ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (836ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (128ms)
				ProcessInitializeOnLoadMethodAttributes (73ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016807 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 852.02 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.15 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.974 seconds
Domain Reload Profiling:
	ReloadAssembly (3975ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3774ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (514ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (192ms)
			SetupLoadedEditorAssemblies (2895ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (852ms)
				BeforeProcessingInitializeOnLoad (127ms)
				ProcessInitializeOnLoadAttributes (1499ms)
				ProcessInitializeOnLoadMethodAttributes (404ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 11.96 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10228 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10749.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 29.479100 ms (FindLiveObjects: 1.363000 ms CreateObjectMapping: 1.506400 ms MarkObjects: 25.736300 ms  DeleteObjects: 0.871900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 551531.535936 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/EntryLibrary/Item_EntryLibrary.prefab
  artifactKey: Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/EntryLibrary/Item_EntryLibrary.prefab using Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a13ebd9059329ff781fe155d1d65f3fd') in 0.085370 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.971138 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Hunting/UITexture/hunt_trap.png
  artifactKey: Guid(f0e5bebb45ff46445911b0c5b2327d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Hunting/UITexture/hunt_trap.png using Guid(f0e5bebb45ff46445911b0c5b2327d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8c32a2aab3674cbeca74e22edaee898f') in 0.293052 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_5.png
  artifactKey: Guid(a4f59e82d0e36a940a2d408a71f2ff34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_5.png using Guid(a4f59e82d0e36a940a2d408a71f2ff34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f21f0fbfd63ec9fe3465cd4a19c35042') in 0.023500 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_1.png
  artifactKey: Guid(82d7f4ddd3fba044989b30247d8a9730) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_1.png using Guid(82d7f4ddd3fba044989b30247d8a9730) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f7dcf830fe0ab5ff1543d299ae89295') in 0.016954 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.637099 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/EntryLibrary/Item_EntryLibrary.prefab
  artifactKey: Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/EntryLibrary/Item_EntryLibrary.prefab using Guid(25aa69271a7d5d64f97521fd1b15787e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f8f407907469859c365d8e36b470b75e') in 0.003538 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016790 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.79 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.092 seconds
Domain Reload Profiling:
	ReloadAssembly (3093ms)
		BeginReloadAssembly (178ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (78ms)
		EndReloadAssembly (2804ms)
			LoadAssemblies (125ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (570ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1916ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1530ms)
				ProcessInitializeOnLoadMethodAttributes (263ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 46.86 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9739 Unused Serialized files (Serialized files now loaded: 0)
Unloading 173 unused Assets / (2.0 MB). Loaded Objects now: 10770.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 87.094700 ms (FindLiveObjects: 2.639900 ms CreateObjectMapping: 1.415300 ms MarkObjects: 82.336800 ms  DeleteObjects: 0.701400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016789 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 14.13 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.733 seconds
Domain Reload Profiling:
	ReloadAssembly (2733ms)
		BeginReloadAssembly (156ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2476ms)
			LoadAssemblies (128ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (476ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1674ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (14ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1304ms)
				ProcessInitializeOnLoadMethodAttributes (237ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 23.93 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9739 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10787.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 26.898800 ms (FindLiveObjects: 0.860500 ms CreateObjectMapping: 0.943900 ms MarkObjects: 24.478300 ms  DeleteObjects: 0.614400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018595 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.90 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.124 seconds
Domain Reload Profiling:
	ReloadAssembly (3125ms)
		BeginReloadAssembly (157ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2858ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (562ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (116ms)
			SetupLoadedEditorAssemblies (1947ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (108ms)
				ProcessInitializeOnLoadAttributes (1545ms)
				ProcessInitializeOnLoadMethodAttributes (267ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 45.85 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9738 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.0 MB). Loaded Objects now: 10803.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.031500 ms (FindLiveObjects: 1.084100 ms CreateObjectMapping: 1.121700 ms MarkObjects: 33.021100 ms  DeleteObjects: 0.802500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016964 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.47 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.895 seconds
Domain Reload Profiling:
	ReloadAssembly (2896ms)
		BeginReloadAssembly (162ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2633ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (518ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1796ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1419ms)
				ProcessInitializeOnLoadMethodAttributes (259ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (29ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.53 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9738 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10820.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.034200 ms (FindLiveObjects: 1.220700 ms CreateObjectMapping: 1.178800 ms MarkObjects: 32.842100 ms  DeleteObjects: 0.791000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016684 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.01 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.671 seconds
Domain Reload Profiling:
	ReloadAssembly (2672ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2393ms)
			LoadAssemblies (125ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (487ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1593ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1249ms)
				ProcessInitializeOnLoadMethodAttributes (228ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 30.82 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9738 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10837.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 32.739800 ms (FindLiveObjects: 1.032600 ms CreateObjectMapping: 1.086600 ms MarkObjects: 29.890600 ms  DeleteObjects: 0.728200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3320.910211 seconds.
  path: Assets/AssetBundle/UI2/Forms/Technology/UITexture/book_item_bg.png
  artifactKey: Guid(5e02ce1983160784a8241cb2d028c0dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Technology/UITexture/book_item_bg.png using Guid(5e02ce1983160784a8241cb2d028c0dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7b9f710d53a2d51436d6078c30d9d1d') in 0.260943 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_lock.png
  artifactKey: Guid(a836a73eb24410c449e23c1b3cf15e75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_lock.png using Guid(a836a73eb24410c449e23c1b3cf15e75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0efee75fe9beddd1ba345c3ed67d6a8d') in 0.029710 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/checkitem_bg2.png
  artifactKey: Guid(90d42bb1c0d17c44098e3a78822b02e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/checkitem_bg2.png using Guid(90d42bb1c0d17c44098e3a78822b02e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '26eb301d27291d61a833fd6b6d2bedd9') in 0.029534 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_unlock.png
  artifactKey: Guid(e9538706f6df59745915d5ef66898f72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_unlock.png using Guid(e9538706f6df59745915d5ef66898f72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '37893222e2e6c07e97e183650bfea8bd') in 0.031217 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_tip_b.png
  artifactKey: Guid(cd869b9177860d647a83f3f82b8def7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_tip_b.png using Guid(cd869b9177860d647a83f3f82b8def7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a7567fee4e5a94becd9eb438c485300e') in 0.019204 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0