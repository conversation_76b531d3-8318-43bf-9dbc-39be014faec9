﻿using System;
using Cysharp.Threading.Tasks;
using SKY;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static Animancer.Easing;
using static Animancer.Editor.AnimationBindings;

namespace UltimateSurvival.Building
{
    public class BlueprintBuilder : MonoBehaviour
    {
        private static string LogTag = "BlueprintBuilder";
        public bool IsDestroy { get; set; } = false;
        private bool _inLoadingBuilding;
        private bool _inBuilding;
        private bool _buildCreated;
        private BTPKG_GET_BLUEPRINT_BUILDING_ACK _buildingInfo;
        private BTDT_BUILDING BuildingData;
        private Dictionary<uint, Vector3> _BuildingPieceInitPos = new Dictionary<uint, Vector3>();
        private Vector3 _startPos;
        private const float CheckObstacleInterval = 0.5f;
        private float _checkObstacleTimer;
        public BuildingHolder BlueprintBuildingHolder = new BuildingHolder();
        public bool NoLandcabDestroyMsg;

        public bool HasBuildingData
        {
            get { return BuildingData != null; }
        }

        public bool InBuillding
        {
            get { return _inBuilding; }
        }

        public bool HasErrorPiece
        {
            get { return m_PiecesState.Any(kv => kv.Value == false); }
        }

        public static BlueprintBuilder Instance;
        public Vector3 TargetPos { get; private set; }
        private Vector3 _lastTargetPos;

        public Dictionary<uint, CTblSkinShow.CItem> AscriptionToSkin = new();
        private Dictionary<uint, BuildingPiece> m_Pieces = new();
        private Dictionary<uint, bool> m_PiecesState = new();

        public void SetBuildingState(bool isInBuilding)
        {
            _inBuilding = isInBuilding;
            if (_inBuilding)
            {
                IsDestroy = false;
            }
            else
            {
                _lastTargetPos = Vector3.zero;
            }
        }

        public void SetBuildingInfo(BTPKG_GET_BLUEPRINT_BUILDING_ACK info)
        {
            if (info == null)
            {
                _buildingInfo = null;
                BuildingData = null;
                BlueprintBuildingHolder.BuildingData = null;
                _startPos = Vector3.zero;
            }
            else
            {
                _buildingInfo = info;
                BuildingData = info.m_stBuilding;
                BlueprintBuildingHolder.BuildingData = BuildingData;
                _startPos = BattleUtils.BTDTV3_TO_V3(BuildingData.m_oListPiece.Find(p => p.m_dwPieceID == 1).m_stPos);
            }
        }

        private void Awake()
        {
            Instance = this;
        }

        private void Update()
        {
            if (_inBuilding && !_inLoadingBuilding)
            {
                Ray ray = Camera.main.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
                RaycastHit hit;
                if (Physics.Raycast(ray, out hit, 1000f, BitTool.LayerMerge(Define.layer地面)))
                {
                    TargetPos = hit.point;
                    if (!_buildCreated)
                    {
                        _buildCreated = true;
                        _inLoadingBuilding = true;
                        CreateBuilding(() =>
                        {
                            _inLoadingBuilding = false;
                            var fight = CommonFunctions.GetUIWindowFight();
                            if (fight != null)
                            {
                                fight.SetBlueprinitButtonShow(true);
                            }
                        });
                    }
                    else
                    {
                        RePlacePieces();

                        _checkObstacleTimer += Time.deltaTime;
                        if (_checkObstacleTimer >= CheckObstacleInterval)
                        {
                            _checkObstacleTimer = 0;
                            CheckObstacle();
                        }
                    }
                }
                else
                {
                    TargetPos = Vector3.zero;
                }

                if (InputSystemManager.Instance.GetButtonUp(InputKeyDefine.Fire1) && _buildCreated)
                {
                    CheckObstacle();
                    _checkObstacleTimer = 0;
                    if (!HasErrorPiece)
                    {
                        var pos = BattleMgr.I().WorldMover.ConvertToRealPosition(TargetPos);
                        HandlerManager.getInstance().battleHandler.ReqBuildBlueprintBuilding(BattleUtils.V3_TO_BTDTV3(pos));
                    }
                }
            }
        }

        private void RePlacePieces()
        {
            if (_lastTargetPos == TargetPos)
            {
                return;
            }
            _lastTargetPos = TargetPos;

            foreach (var kv in m_Pieces)
            {
                var posOffset = BattleMgr.I().WorldMover.ConvertToRealPosition(TargetPos) - _startPos;
                kv.Value.transform.localPosition = _BuildingPieceInitPos[kv.Value.BuildingPieceId] + posOffset;
                kv.Value.UpdateGpuInstancePos();
            }
        }

        public void CheckObstacle()
        {
            foreach (var kv in m_Pieces)
            {
                var piece = kv.Value;
                m_PiecesState[piece.BuildingPieceId] = true;
                if (piece.IsFoundation())
                {
                    if (piece.IsBuildAreaLimit())
                    {
                        m_PiecesState[piece.BuildingPieceId] = false;
                    }
                    else if (piece.IsTopCrossTerrain())
                    {
                        m_PiecesState[piece.BuildingPieceId] = false;
                    }
                    else
                    {
                        piece.IsBottomCorrect(out int checkResult);
                        if (checkResult == 1 || checkResult == 2)
                        {
                            m_PiecesState[piece.BuildingPieceId] = false;
                        }
                        else if (piece.CheckCollisionRoad())
                        {
                            m_PiecesState[piece.BuildingPieceId] = false;
                        }
                    }
                }
                if(m_PiecesState[piece.BuildingPieceId] && piece.BlueprintCheckCollisionOther())
                {
                    m_PiecesState[piece.BuildingPieceId] = false;
                }

                if (m_PiecesState[piece.BuildingPieceId])
                {
                    piece.ChangePieceBlueprintColor(BuildingNet.BlueprintMaterialType.Correct);
                }
                else
                {
                    piece.ChangePieceBlueprintColor(BuildingNet.BlueprintMaterialType.Error);
                }
            }
        }

        public void DestroyAllPiece()
        {
            IsDestroy = true;
            _buildCreated = false;
            _inLoadingBuilding = false;
            // 清除旧的建筑块
            foreach (var iter in m_Pieces)
            {
                iter.Value.SetState(PieceState.Destroy);;
            }

            m_Pieces.Clear();
            m_PiecesState.Clear();
        }

        private int loadBuildingPieceCount;
        private void CreateBuilding(Action createFinish)
        {
            loadBuildingPieceCount = 0;
            for (int loadIndex = 0; loadIndex < BuildingData.m_oListPiece.Count; loadIndex++)
            {
                if (IsDestroy) break;
                BTDT_BUILDING_PIECE dataPiece = BuildingData.m_oListPiece[loadIndex];
                loadBuildingPieceCount++;
                BuildingPiece tryBuildingPiece = PoolAssetTryGetBuildingPiece(dataPiece, out bool isFreeList);
                if (tryBuildingPiece != null)
                {
                    loadBuildingPieceCount--;
                    if (loadBuildingPieceCount <= 0)
                    {
                        createFinish?.Invoke();
                    }
                }
                else
                {
                    LoadBuildPiece(dataPiece, piece =>
                    {
                        loadBuildingPieceCount--;
                        if (loadBuildingPieceCount <= 0)
                        {
                            createFinish?.Invoke();
                        }
                    });
                }
            }
        }

        private void LoadBuildPiece(BTDT_BUILDING_PIECE dataPiece, Action<BuildingPiece> createFinishIn)
        {
            string baseBuildPath = BattleMgr.I().BuildManager.GetBasePathByBuildId(dataPiece.m_wBaseID);
            if (string.IsNullOrEmpty(baseBuildPath))
            {
                createFinishIn?.Invoke(null);
                return;
            }

            BattleMgr.I().BuildManager.PoolAssetLoad(baseBuildPath, BattleMgr.I().BuildManager.BuildRoot, (baseAsset) =>
                {
                    bool needChange = !GetBuildPathByBuildId(dataPiece.m_wBaseID, out string skinPath);

                    if (IsDestroy || (needChange && string.IsNullOrEmpty(skinPath)))
                    {
                        BattleMgr.I().BuildManager.PoolAssetDestroy(baseAsset);
                        createFinishIn?.Invoke(null);
                        return;
                    }

                    if (needChange)
                    {
                        BattleMgr.I().BuildManager.PoolAssetLoad(skinPath, BattleMgr.I().BuildManager.BuildRoot,
                            (skinAsset) =>
                            {
                                if (IsDestroy)
                                {
                                    BattleMgr.I().BuildManager.PoolAssetDestroy(baseAsset);
                                    BattleMgr.I().BuildManager.PoolAssetDestroy(skinAsset);
                                    createFinishIn?.Invoke(null);
                                    return;
                                }

                                BuildingPiece buildPiece = baseAsset.GetComponent<BuildingPiece>();
                                buildPiece.Init(dataPiece.m_wBaseID);
                                if (skinAsset != null) buildPiece.SetSkinObjectAndFlush(skinAsset);
                                SetBuildPiece(buildPiece, dataPiece);
                                createFinishIn?.Invoke(buildPiece);
                            }
                            , () =>
                            {
                                BattleMgr.I().BuildManager.PoolAssetDestroy(baseAsset);
                                createFinishIn?.Invoke(null);
                            });
                    }
                    else
                    {
                        BuildingPiece buildPiece = baseAsset.GetComponent<BuildingPiece>();
                        buildPiece.Init(dataPiece.m_wBaseID);
                        SetBuildPiece(buildPiece, dataPiece);
                        createFinishIn?.Invoke(buildPiece);
                    }
                }
                , () => { createFinishIn?.Invoke(null); });
        }

        private BuildingPiece PoolAssetTryGetBuildingPiece(BTDT_BUILDING_PIECE dataPiece, out bool isFreeList)
        {
            isFreeList = false;
            string baseBuildPath = BattleMgr.I().BuildManager.GetBasePathByBuildId(dataPiece.m_wBaseID);
            if (string.IsNullOrEmpty(baseBuildPath)) return null;
            GameObject baseAsset = BattleMgr.I().BuildManager
                .PoolAssetTryGet(baseBuildPath, BattleMgr.I().BuildManager.BuildRoot, out isFreeList);
            if (baseAsset == null) return null;

            GameObject skinAsset = null;
            if (!GetBuildPathByBuildId(dataPiece.m_wBaseID, out string skinPath))
            {
                if (string.IsNullOrEmpty(skinPath)) return null;
                skinAsset = BattleMgr.I().BuildManager
                    .PoolAssetTryGet(skinPath, null, out bool skinFree);
                if (skinAsset == null) return null;
            }

            BuildingPiece buildPiece = baseAsset.GetComponent<BuildingPiece>();
            buildPiece.Init(dataPiece.m_wBaseID);
            if (skinAsset != null) buildPiece.SetSkinObjectAndFlush(skinAsset);
            SetBuildPiece(buildPiece, dataPiece);
            return buildPiece;
        }

        public bool GetBuildPathByBuildId(ushort tblBuildId, out string skinPath)
        {
            skinPath = string.Empty;
            bool isBasePath = true;
            var buildCurItem = CTblAll.g_oTblBuilding.Get(tblBuildId);
            if (buildCurItem == null)
            {
                Util.LogError("[{0}]GetBuildPathByBuildId找不到皮肤数据，BuildId{1}", LogTag, tblBuildId);
                return false;
            }
            if (AscriptionToSkin.TryGetValue(buildCurItem.m_dwAscription, out var skinShow))
            {
                // 有皮肤
                skinPath = skinShow.m_strModelPath1;
                isBasePath = false;
            }
            else
            {
                // 无皮肤检查是不是升级的建筑
                isBasePath = BattleMgr.I().BuildManager.IsBaseBuildId(tblBuildId);
                skinPath = BattleMgr.I().BuildManager.GetBuildPathByItem(buildCurItem);
            }
            return isBasePath;
        }

        private void SetBuildPiece(BuildingPiece piece, BTDT_BUILDING_PIECE dataPiece)
        {
            var posOffset = BattleMgr.I().WorldMover.ConvertToRealPosition(TargetPos) - _startPos;
            Vector3 pos = BattleUtils.BTDTV3_TO_V3(dataPiece.m_stPos);
            Vector3 euler = BattleUtils.BTDTV3_TO_V3(dataPiece.m_stEuler);
            _BuildingPieceInitPos[dataPiece.m_dwPieceID] = pos;
            var trans = piece.transform;
            trans.eulerAngles = euler;
            trans.localPosition = pos + posOffset;
            piece.BuildingPieceId = dataPiece.m_dwPieceID;
            piece.Building = BlueprintBuildingHolder;
            piece.SetState(PieceState.Preview);
            piece.ChangePieceBlueprintColor(BuildingNet.BlueprintMaterialType.None);
            piece.Hp = dataPiece.m_nHP;

            if (piece.TblBuildingItem != null)
            {
                if (AscriptionToSkin.TryGetValue(piece.TblBuildingItem.m_dwAscription, out var skinShow))
                {
                    piece.SkinId = skinShow.m_dwSkinID;
                }
                else
                {
                    piece.SkinId = 0;
                }
            }

            AddPiece(piece);

            //注册门
            //if (piece.gameObject.TryGetComponent<SceneDoor>(out SceneDoor door))
            //{
            //    door.RegisterPlayerDoor();
            //    for (int i = 0; i < BuildingData.m_oListDoor.Count; i++)
            //    {
            //        BTDT_BUILDING_DOOR info = BuildingData.m_oListDoor[i];
            //        if (info.m_dwPieceID == piece.BuildingPieceId)
            //        {
            //            door.SetBuildingDoorData(info);
            //            door.Init();
            //            break;
            //        }
            //    }
            //}

            //创建Sockets
            //piece.SetOccupy(dataPiece.m_oListSocket);
        }

        public void AddPiece(BuildingPiece piece)
        {
            if (!m_Pieces.ContainsKey(piece.BuildingPieceId))
            {
                m_Pieces.Add(piece.BuildingPieceId, piece);
                Vector3 piecePos = piece.transform.localPosition;
                //if (m_Pieces.Count == 1) 
                //{
                //    BuildingBounds = new Bounds(piecePos, new Vector3(4, 4, 4));
                //}
                //else
                //{
                //    if (!BuildingBounds.Contains(piecePos))
                //    {
                //        Vector3 minPos = BuildingBounds.min;
                //        Vector3 maxPos = BuildingBounds.max;
                //        Vector3 tmpMax = Vector3.Max(piecePos, maxPos);
                //        Vector3 tmpMin = Vector3.Min(piecePos, minPos);
                //        BuildingBounds = new Bounds((tmpMax - tmpMin) / 2 + tmpMin, tmpMax - tmpMin);
                //    }
                //}
            }
        }
    }
}
