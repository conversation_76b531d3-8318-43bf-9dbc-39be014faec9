<?xml version="1.0" encoding="utf-8"?>
<FacilityTemplate type="DeepCore.GameData.Zone.FacilityTemplate">
  <Btns element_type="DeepCore.GameData.Zone.FacilityTemplate+FacilityButton">
    <element>
      <BtnID>1</BtnID>
      <BtnName>附身</BtnName>
      <EnergyID element_type="DeepCore.GameData.Zone.FacilityTemplate+ButtonEnergyID" />
      <MasterCanUse>False</MasterCanUse>
      <UseUnitCamp>3</UseUnitCamp>
    </element>
  </Btns>
  <Coms element_type="DeepCore.GameData.Zone.FacilityCom" />
  <FileName>sgcmod_wylh/prefab/object_prefab/wylh_prefab/bags_on_pallet_v1_1.assetbundles</FileName>
  <ID>300002</ID>
  <Name>白色物料堆（可附身）</Name>
  <ParamID>10002</ParamID>
  <Type>Attach</Type>
  <UserDefineVars element_type="DeepCore.GameData.Zone.FacilityTemplate+ZoneVar">
    <element>
      <Key>attachActor</Key>
      <Value type="System.Int64">0</Value>
    </element>
  </UserDefineVars>
  <property.EditorPath>设施/白色物料堆（可附身）(300002)</property.EditorPath>
</FacilityTemplate>