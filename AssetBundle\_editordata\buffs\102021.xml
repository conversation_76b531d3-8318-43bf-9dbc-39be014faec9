<?xml version="1.0" encoding="utf-8"?>
<BuffTemplate type="DeepCore.GameData.Zone.BuffTemplate">
  <AppearanceId>0</AppearanceId>
  <BanAttack>False</BanAttack>
  <BanInteract>False</BanInteract>
  <BanMove>False</BanMove>
  <BanUseItem>False</BanUseItem>
  <BindingEffect>
    <BindBody>True</BindBody>
    <BindBodyDirection>True</BindBodyDirection>
    <BindInjuredIocation>False</BindInjuredIocation>
    <BindPartName>Bip001 Spine1</BindPartName>
    <BlurBeginTime>0</BlurBeginTime>
    <BlurEndTime>0</BlurEndTime>
    <BlurStrength>0.00</BlurStrength>
    <BlurWaitTime>0</BlurWaitTime>
    <CameraBeginTime>0</CameraBeginTime>
    <CameraDistance>0.00</CameraDistance>
    <CameraEndTime>0</CameraEndTime>
    <CameraWaitTime>0</CameraWaitTime>
    <EarthQuakeMS>0</EarthQuakeMS>
    <EarthQuakeXYZ>0.00</EarthQuakeXYZ>
    <EffectTimeMS>0</EffectTimeMS>
    <IsLoop>True</IsLoop>
    <Name>Effect3D/Prefab/Fx_Common_Hit_blood_01.prefab</Name>
    <ScaleToBodySize>0.00</ScaleToBodySize>
    <WarnDegree>80.00</WarnDegree>
    <WarnScaleX>1.00</WarnScaleX>
    <WarnScaleZ>1.00</WarnScaleZ>
    <WarnSpeed>1.00</WarnSpeed>
    <WarnType>WARNING_TYPE_NONE</WarnType>
    <property.SerialNumber>6017441</property.SerialNumber>
  </BindingEffect>
  <BindingEffectList element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <BodyHitSize>0.00</BodyHitSize>
  <BodyScale>1.00</BodyScale>
  <BodySize>0.00</BodySize>
  <ClientVisible>True</ClientVisible>
  <ExclusiveCatgory>0</ExclusiveCatgory>
  <ExclusivePriority>0</ExclusivePriority>
  <FirstTimeEnable>True</FirstTimeEnable>
  <HitIntervalMS>1000</HitIntervalMS>
  <HitKeyFrame>
    <Actions element_type="DeepCore.GameData.Zone.BuffKeyFrameAction">
      <element type="DeepCore.GameData.Zone.AttackProp">
        <Attack>21</Attack>
        <CrushPercent>0.00</CrushPercent>
        <HitActions element_type="DeepCore.GameData.Zone.HitAction" />
        <HitMoveBodyAttackSize>1.00</HitMoveBodyAttackSize>
        <HitMoveMType>BySenderPosition</HitMoveMType>
        <IsDamageProtect>False</IsDamageProtect>
        <KnockOutTimeMS>0</KnockOutTimeMS>
        <MaskDamage>False</MaskDamage>
        <MaskHitFly>False</MaskHitFly>
        <MaskKnockDown>False</MaskKnockDown>
        <MaskMustCritical>False</MaskMustCritical>
        <MaskMustHit>False</MaskMustHit>
        <Properties type="BWBattle.Common.Plugins.BWAttackProperties">
          <ATK_RATIO>0</ATK_RATIO>
          <BUILD_ATK>0</BUILD_ATK>
          <DamageRate>0.00</DamageRate>
          <LIFE_ATK>30</LIFE_ATK>
          <MINE_ATK>0</MINE_ATK>
          <STONE_ATK>0</STONE_ATK>
          <SULFUR_ATK>0</SULFUR_ATK>
          <TREE_ATK>0</TREE_ATK>
        </Properties>
        <StopFrameMS>0</StopFrameMS>
        <Weight>1</Weight>
        <property.SerialNumber>6017383</property.SerialNumber>
      </element>
    </Actions>
    <FrameMS>0</FrameMS>
  </HitKeyFrame>
  <IconName>400002</IconName>
  <ID>102021</ID>
  <IsCancelBySelf>False</IsCancelBySelf>
  <IsClientManagedMove>False</IsClientManagedMove>
  <IsDuplicating>False</IsDuplicating>
  <IsHarmful>False</IsHarmful>
  <IsInvincible>False</IsInvincible>
  <IsInvisible>False</IsInvisible>
  <IsNoneBlock>False</IsNoneBlock>
  <IsOverlay>False</IsOverlay>
  <IsPassive>False</IsPassive>
  <IsRemoveOnSenderRemoved>False</IsRemoveOnSenderRemoved>
  <IsSilent>False</IsSilent>
  <KeyFrames element_type="DeepCore.GameData.Zone.BuffTemplate+KeyFrame" />
  <LifeTimeMS>5000</LifeTimeMS>
  <MakeAvatar>False</MakeAvatar>
  <MakeStun>False</MakeStun>
  <MaxOverlay>1</MaxOverlay>
  <Name>蛊毒飞刀-持续掉血</Name>
  <OverlayBindingEffect element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <Properties type="BWBattle.Common.Plugins.BWBuffProperties">
    <BuffData type="DeepCore.ArrayList`1[[BWBattle.Common.Plugins.BWBuffData, BeeWorld.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]" element_type="BWBattle.Common.Plugins.BWBuffData">
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>DEF</ChangeType>
        <Value>-40</Value>
        <ValueType>Percent</ValueType>
      </element>
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>SPEED</ChangeType>
        <Value>-9000</Value>
        <ValueType>Value</ValueType>
      </element>
    </BuffData>
    <CanBePurged>True</CanBePurged>
    <SaveOnChangeScene>True</SaveOnChangeScene>
  </Properties>
  <UnitChangeSkills>True</UnitChangeSkills>
  <UnitKeepSkillsID element_type="System.Int32" />
  <UnitSkills element_type="DeepCore.GameData.Zone.LaunchSkill" />
  <property.EditorPath>BUFF/蛊毒飞刀-持续掉血(102021)</property.EditorPath>
</BuffTemplate>