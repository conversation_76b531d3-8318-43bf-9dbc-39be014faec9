<?xml version="1.0" encoding="utf-8"?>
<BuffTemplate type="DeepCore.GameData.Zone.BuffTemplate">
  <AppearanceId>0</AppearanceId>
  <BanAttack>False</BanAttack>
  <BanInteract>False</BanInteract>
  <BanMove>False</BanMove>
  <BanUseItem>False</BanUseItem>
  <BindingEffect>
    <BindBody>True</BindBody>
    <BindBodyDirection>True</BindBodyDirection>
    <BindInjuredIocation>False</BindInjuredIocation>
    <BindPartName>Bip001 Spine1</BindPartName>
    <BlurBeginTime>0</BlurBeginTime>
    <BlurEndTime>0</BlurEndTime>
    <BlurStrength>0.00</BlurStrength>
    <BlurWaitTime>0</BlurWaitTime>
    <CameraBeginTime>0</CameraBeginTime>
    <CameraDistance>0.00</CameraDistance>
    <CameraEndTime>0</CameraEndTime>
    <CameraWaitTime>0</CameraWaitTime>
    <EarthQuakeMS>0</EarthQuakeMS>
    <EarthQuakeXYZ>0.00</EarthQuakeXYZ>
    <EffectTimeMS>0</EffectTimeMS>
    <IsLoop>True</IsLoop>
    <Name>Effect3D/Prefab/Fx_Common_Hit_blood_01.prefab</Name>
    <ScaleToBodySize>0.00</ScaleToBodySize>
    <WarnDegree>80.00</WarnDegree>
    <WarnScaleX>1.00</WarnScaleX>
    <WarnScaleZ>1.00</WarnScaleZ>
    <WarnSpeed>1.00</WarnSpeed>
    <WarnType>WARNING_TYPE_NONE</WarnType>
    <property.SerialNumber>6017450</property.SerialNumber>
  </BindingEffect>
  <BindingEffectList element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <BodyHitSize>0.00</BodyHitSize>
  <BodyScale>1.00</BodyScale>
  <BodySize>0.00</BodySize>
  <ClientVisible>True</ClientVisible>
  <ExclusiveCatgory>0</ExclusiveCatgory>
  <ExclusivePriority>0</ExclusivePriority>
  <FirstTimeEnable>True</FirstTimeEnable>
  <HitIntervalMS>1000</HitIntervalMS>
  <IconName>400002</IconName>
  <ID>103021</ID>
  <IsCancelBySelf>False</IsCancelBySelf>
  <IsClientManagedMove>False</IsClientManagedMove>
  <IsDuplicating>False</IsDuplicating>
  <IsHarmful>False</IsHarmful>
  <IsInvincible>False</IsInvincible>
  <IsInvisible>False</IsInvisible>
  <IsNoneBlock>False</IsNoneBlock>
  <IsOverlay>False</IsOverlay>
  <IsPassive>False</IsPassive>
  <IsRemoveOnSenderRemoved>False</IsRemoveOnSenderRemoved>
  <IsSilent>False</IsSilent>
  <KeyFrames element_type="DeepCore.GameData.Zone.BuffTemplate+KeyFrame" />
  <LifeTimeMS>10000</LifeTimeMS>
  <MakeAvatar>True</MakeAvatar>
  <MakeStun>False</MakeStun>
  <MaxOverlay>1</MaxOverlay>
  <Name>逃脱喷雾变身buff</Name>
  <OverlayBindingEffect element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <Properties type="BWBattle.Common.Plugins.BWBuffProperties">
    <BuffData type="DeepCore.ArrayList`1[[BWBattle.Common.Plugins.BWBuffData, BeeWorld.Data, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]" element_type="BWBattle.Common.Plugins.BWBuffData">
      <element type="BWBattle.Common.Plugins.BWBuffData_ChangeProp">
        <ChangeType>SPEED</ChangeType>
        <Value>5000</Value>
        <ValueType>Value</ValueType>
      </element>
    </BuffData>
    <CanBePurged>True</CanBePurged>
    <SaveOnChangeScene>True</SaveOnChangeScene>
  </Properties>
  <UnitChangeSkills>True</UnitChangeSkills>
  <UnitFileName>prefab/object_prefab/prop_prefab/c_prop_xueqingxiang_test.prefab</UnitFileName>
  <UnitKeepSkillsID element_type="System.Int32" />
  <UnitSkills element_type="DeepCore.GameData.Zone.LaunchSkill" />
  <property.EditorPath>BUFF/buff第二期/逃脱喷雾变身buff(103021)</property.EditorPath>
</BuffTemplate>