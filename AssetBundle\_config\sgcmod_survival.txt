{"ModName": "sgcmod_survival", "OutputPath": "", "NoDepsPath": "", "MeshRW": true, "Platform": 1, "SceneData": {"Type": 0, "Name": "Scene", "InputPath": "", "InputFiles": "Assets/Scene/Island.unity|Assets/Scene/WorldBase.unity|Assets/Scene/xunlianchang01.unity|Assets/Scene/empty.unity|Assets/Scene.unity|Assets/Scene2.unity", "Suffix": ".unity", "InputRoot": "", "GenPath": "scene", "NeedDeps": true}, "PrefabData": {"Type": 1, "Name": "Prefab", "InputPath": "Assets/AssetBundle/prefab|Assets/AssetBundle/Model|Assets/AssetBundle/Effect|Assets/AssetBundle/Effect3D", "InputFiles": "", "Suffix": ".prefab", "InputRoot": "Assets/AssetBundle", "GenPath": "", "NeedDeps": true}, "ShaderData": {"Type": 2, "Name": "ShaderVariants", "InputPath": "Assets/AssetBundle/Shaders|Assets/AssetBundle/Effect3D/Shader", "InputFiles": "", "Suffix": ".shader|.shade<PERSON><PERSON><PERSON>", "InputRoot": "", "GenPath": "", "NeedDeps": true}, "CustomBuild": [{"Type": 3, "Name": "uires", "InputPath": "Assets/AssetBundle/UI2|Assets/AssetBundle/UI|~Assets/AssetBundle/UI2/atlases|Assets/AssetBundle/TerrainMesh|Assets/AssetBundle/Font|Assets/AssetBundle/SGC/UI", "InputFiles": "", "Suffix": "", "InputRoot": "Assets/AssetBundle", "GenPath": "uires", "NeedDeps": true}, {"Type": 3, "Name": "commonRes", "InputPath": "Assets/AssetBundle|~Assets/AssetBundle/Shaders|~Assets/AssetBundle/prefab|~Assets/AssetBundle/Model|~Assets/AssetBundle/Effect|~Assets/AssetBundle/Effect3D|~Assets/AssetBundle/UI2|~Assets/AssetBundle/UI|~Assets/AssetBundle/TerrainMesh|~Assets/AssetBundle/Font|~Assets/AssetBundle/SGC/UI", "InputFiles": "", "Suffix": "", "InputRoot": "Assets/AssetBundle", "GenPath": "", "NeedDeps": false}, {"Type": 3, "Name": "lua", "InputPath": "Assets/SGC/Unity/Lua", "InputFiles": "", "Suffix": ".lua.txt", "InputRoot": "", "GenPath": "", "NeedDeps": false}]}