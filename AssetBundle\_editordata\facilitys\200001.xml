<?xml version="1.0" encoding="utf-8"?>
<FacilityTemplate type="DeepCore.GameData.Zone.FacilityTemplate">
  <Btns element_type="DeepCore.GameData.Zone.FacilityTemplate+FacilityButton">
    <element>
      <BtnID>1</BtnID>
      <BtnName>破坏能量源</BtnName>
      <EnergyID element_type="DeepCore.GameData.Zone.FacilityTemplate+ButtonEnergyID">
        <element>
          <EnergyID>1</EnergyID>
        </element>
        <element>
          <EnergyID>2</EnergyID>
        </element>
        <element>
          <EnergyID>3</EnergyID>
        </element>
      </EnergyID>
      <MasterCanUse>False</MasterCanUse>
      <UseUnitCamp>3</UseUnitCamp>
    </element>
  </Btns>
  <Coms element_type="DeepCore.GameData.Zone.FacilityCom">
    <element type="DeepCore.GameData.Zone.FacilityProgressCom">
      <DegreeOfCompletion>1000</DegreeOfCompletion>
      <ID>1</ID>
      <PosAnchor>Center</PosAnchor>
      <PosX>-157</PosX>
      <PosY>-229</PosY>
      <ProgressBarStyle>CipherMachine</ProgressBarStyle>
      <UseBtnName>破坏能量源</UseBtnName>
    </element>
    <element type="DeepCore.GameData.Zone.FacilityQteCom">
      <ID>2</ID>
      <PosAnchor>Center</PosAnchor>
      <PosX>0</PosX>
      <PosY>0</PosY>
      <QteCompletionTime>5.00</QteCompletionTime>
      <QteRodoomMaxTime>3.00</QteRodoomMaxTime>
      <QteRodoomMinTime>1.50</QteRodoomMinTime>
      <QteStyle>CipherMachine</QteStyle>
    </element>
    <element type="DeepCore.GameData.Zone.FacilityOperationCom">
      <ID>3</ID>
      <UseAction>Build.Build</UseAction>
    </element>
  </Coms>
  <FileName>sgcmod_survival/prefab/object_prefab/prop_prefab/c_prop_wuqigui.assetbundles</FileName>
  <ID>200001</ID>
  <Name>密码机</Name>
  <ParamID>10002</ParamID>
  <Type>CipherMachine</Type>
  <UserDefineVars element_type="DeepCore.GameData.Zone.FacilityTemplate+ZoneVar">
    <element>
      <Key>DegreePerSecond</Key>
      <Value type="System.Int64">10</Value>
    </element>
    <element>
      <Key>GeneralIncreaseCompletion</Key>
      <Value type="System.Int64">20</Value>
    </element>
    <element>
      <Key>MultipleRepairBonus</Key>
      <Value type="System.Int64">50</Value>
    </element>
    <element>
      <Key>PerfectIncreaseCompletion</Key>
      <Value type="System.Int64">60</Value>
    </element>
  </UserDefineVars>
  <property.EditorPath>设施/密码机(200001)</property.EditorPath>
</FacilityTemplate>