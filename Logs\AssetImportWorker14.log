Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker14
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker14.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [38404] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 612145059 [EditorId] 612145059 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [38404] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 612145059 [EditorId] 612145059 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 974.32 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56932
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008379 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 838.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.590 seconds
Domain Reload Profiling:
	ReloadAssembly (1591ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1230ms)
			LoadAssemblies (248ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (110ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (1026ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (838ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (124ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016708 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 892.49 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.33 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.776 seconds
Domain Reload Profiling:
	ReloadAssembly (4777ms)
		BeginReloadAssembly (116ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (4503ms)
			LoadAssemblies (585ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (499ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (230ms)
			SetupLoadedEditorAssemblies (3134ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (893ms)
				BeforeProcessingInitializeOnLoad (133ms)
				ProcessInitializeOnLoadAttributes (1443ms)
				ProcessInitializeOnLoadMethodAttributes (653ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 13.85 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.53 GB to 0.53 GB.
Total: 28.207400 ms (FindLiveObjects: 0.899900 ms CreateObjectMapping: 1.140900 ms MarkObjects: 25.523800 ms  DeleteObjects: 0.641300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016344 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.55 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.833 seconds
Domain Reload Profiling:
	ReloadAssembly (2834ms)
		BeginReloadAssembly (156ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2572ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (504ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (103ms)
			SetupLoadedEditorAssemblies (1750ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1333ms)
				ProcessInitializeOnLoadMethodAttributes (294ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 42.71 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10759.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.663300 ms (FindLiveObjects: 2.587800 ms CreateObjectMapping: 1.915300 ms MarkObjects: 31.436600 ms  DeleteObjects: 0.722100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016581 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.34 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.069 seconds
Domain Reload Profiling:
	ReloadAssembly (3070ms)
		BeginReloadAssembly (188ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (85ms)
		EndReloadAssembly (2776ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (532ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (106ms)
			SetupLoadedEditorAssemblies (1898ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1533ms)
				ProcessInitializeOnLoadMethodAttributes (246ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.09 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10776.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 39.732600 ms (FindLiveObjects: 1.445000 ms CreateObjectMapping: 2.240700 ms MarkObjects: 35.144300 ms  DeleteObjects: 0.900400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 58.93 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10776.
Memory consumption went from 296.1 MB to 294.0 MB.
Total: 40.996900 ms (FindLiveObjects: 1.675000 ms CreateObjectMapping: 1.607700 ms MarkObjects: 36.987500 ms  DeleteObjects: 0.725000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015632 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.45 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.831 seconds
Domain Reload Profiling:
	ReloadAssembly (2832ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2572ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (491ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1777ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1397ms)
				ProcessInitializeOnLoadMethodAttributes (255ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (25ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 42.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10793.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 38.658800 ms (FindLiveObjects: 1.255300 ms CreateObjectMapping: 1.563500 ms MarkObjects: 34.795400 ms  DeleteObjects: 1.043500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 48.58 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10793.
Memory consumption went from 296.1 MB to 294.0 MB.
Total: 34.562800 ms (FindLiveObjects: 1.023500 ms CreateObjectMapping: 1.101000 ms MarkObjects: 31.602000 ms  DeleteObjects: 0.835200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016494 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.11 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.878 seconds
Domain Reload Profiling:
	ReloadAssembly (2879ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2607ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (510ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1780ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1435ms)
				ProcessInitializeOnLoadMethodAttributes (219ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 34.81 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10810.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 40.816600 ms (FindLiveObjects: 1.334800 ms CreateObjectMapping: 1.762300 ms MarkObjects: 36.737500 ms  DeleteObjects: 0.980700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 379619.086036 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/bag_time.png
  artifactKey: Guid(1fc227063986b3a4bb549e74fc44daa7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/bag_time.png using Guid(1fc227063986b3a4bb549e74fc44daa7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9ad68d752881ba0bcdaa6efd9eabc0db') in 0.174979 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_nolearn.png
  artifactKey: Guid(81f09e1b23c86af4d8a4fca54e4bcdad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_nolearn.png using Guid(81f09e1b23c86af4d8a4fca54e4bcdad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c0f84098774936327157dcd4c8b61424') in 0.023524 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/icon_cant_do.png
  artifactKey: Guid(34cf567171cba3141bd6841451f90752) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/icon_cant_do.png using Guid(34cf567171cba3141bd6841451f90752) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ee05ed212f02b83e5b7a4f4e4396ac8') in 0.011349 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_gou.png
  artifactKey: Guid(50e75ff5f5f687b40ad31d6fcc1ba15f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_gou.png using Guid(50e75ff5f5f687b40ad31d6fcc1ba15f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '792d89d6c626b56867eaeaec8c07ef14') in 0.016716 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_icon_23_02.png
  artifactKey: Guid(5aeef72341a1e4e49b690a424d716289) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_icon_23_02.png using Guid(5aeef72341a1e4e49b690a424d716289) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '898c0391639dbdc53ad89a42ca405e06') in 0.021347 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/btn_rename.png
  artifactKey: Guid(3be1d617fce659f469b3d52fb74a7b90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/btn_rename.png using Guid(3be1d617fce659f469b3d52fb74a7b90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'deeb1228a7cbb3720d87439ded21804a') in 0.018629 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k5.png
  artifactKey: Guid(9774a31b9ccab6441a67fe6328f2086d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k5.png using Guid(9774a31b9ccab6441a67fe6328f2086d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c609f2d4dff85e5d7735c471ac814c10') in 0.014456 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k1.png
  artifactKey: Guid(b62bd592095aa4b4884d9f2e4a564967) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k1.png using Guid(b62bd592095aa4b4884d9f2e4a564967) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd87afea2eb7862344a55bf195c9905e3') in 0.011253 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/comm_icon_time.png
  artifactKey: Guid(33d72071b16ba5f4ea88c3480d131a68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/comm_icon_time.png using Guid(33d72071b16ba5f4ea88c3480d131a68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e4f178836874833ebe018ce682a960b1') in 0.013863 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/comm_icon_wear.png
  artifactKey: Guid(c3b86b07a3ddf4841bed42cdcda07418) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/comm_icon_wear.png using Guid(c3b86b07a3ddf4841bed42cdcda07418) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff686934816eba54617ad7ec3a7f94f7') in 0.016072 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_pz1.png
  artifactKey: Guid(b8a2a8459d010ff489f1e0580ed9da58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_pz1.png using Guid(b8a2a8459d010ff489f1e0580ed9da58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b50f78e63bee8265b239b082a64db853') in 0.016232 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k3.png
  artifactKey: Guid(e032fde0ff581f94f94bdb13fab75f6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_k3.png using Guid(e032fde0ff581f94f94bdb13fab75f6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '40796b7cd19135e6b79ad47800a05f0c') in 0.012844 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_itemicon_mask.png
  artifactKey: Guid(21da1248d835ed240b5cba767fd37c09) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_itemicon_mask.png using Guid(21da1248d835ed240b5cba767fd37c09) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '46b9490a2b83b3b2af79a2e62477b844') in 0.010590 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_pz6.png
  artifactKey: Guid(9a85ad2400bcf554eaebd35011cabada) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/com_item_pz6.png using Guid(9a85ad2400bcf554eaebd35011cabada) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '88017e0a7ce19b92b442cb63909dc930') in 0.011114 seconds 
========================================================================
Received Import Request.
  Time since last request: 22.928701 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/selectkuang.png
  artifactKey: Guid(0538d948fa08482478e9476ed51df58b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/selectkuang.png using Guid(0538d948fa08482478e9476ed51df58b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a44a8e98610a74a8c146328c1242dfa2') in 0.017108 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/timeicon.png
  artifactKey: Guid(70cc6810db1a8c643be0dd20ff7160ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/timeicon.png using Guid(70cc6810db1a8c643be0dd20ff7160ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '30223dd48530081d653996316193651c') in 0.020480 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommItem/video_icon.png
  artifactKey: Guid(e6e7dc4712336914ba0515bca665b4af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommItem/video_icon.png using Guid(e6e7dc4712336914ba0515bca665b4af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4bb1508f74f960d2a873b8e2f0fce111') in 0.023398 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0