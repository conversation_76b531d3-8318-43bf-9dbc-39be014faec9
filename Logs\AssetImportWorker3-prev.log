Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker3.log
-srvPort
2710
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23812] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2267427663 [EditorId] 2267427663 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [23812] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2267427663 [EditorId] 2267427663 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 900.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56332
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008238 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 831.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.566 seconds
Domain Reload Profiling:
	ReloadAssembly (1567ms)
		BeginReloadAssembly (234ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1216ms)
			LoadAssemblies (231ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (110ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (1016ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (831ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (132ms)
				ProcessInitializeOnLoadMethodAttributes (44ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.019118 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 831.70 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.195 seconds
Domain Reload Profiling:
	ReloadAssembly (4196ms)
		BeginReloadAssembly (102ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3890ms)
			LoadAssemblies (542ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (485ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (190ms)
			SetupLoadedEditorAssemblies (2610ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (832ms)
				BeforeProcessingInitializeOnLoad (125ms)
				ProcessInitializeOnLoadAttributes (1308ms)
				ProcessInitializeOnLoadMethodAttributes (334ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 7.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10227 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10748.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 20.164200 ms (FindLiveObjects: 0.810200 ms CreateObjectMapping: 0.853600 ms MarkObjects: 17.958600 ms  DeleteObjects: 0.540700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015837 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.62 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.745 seconds
Domain Reload Profiling:
	ReloadAssembly (2746ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2467ms)
			LoadAssemblies (125ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (506ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1670ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1306ms)
				ProcessInitializeOnLoadMethodAttributes (249ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 29.64 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10761.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 32.072000 ms (FindLiveObjects: 1.143000 ms CreateObjectMapping: 1.202800 ms MarkObjects: 28.945300 ms  DeleteObjects: 0.779700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016452 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.78 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.650 seconds
Domain Reload Profiling:
	ReloadAssembly (2651ms)
		BeginReloadAssembly (150ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (2399ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1620ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1287ms)
				ProcessInitializeOnLoadMethodAttributes (213ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 31.61 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10778.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 32.827300 ms (FindLiveObjects: 1.192000 ms CreateObjectMapping: 1.405700 ms MarkObjects: 29.350500 ms  DeleteObjects: 0.877800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017258 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.03 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.705 seconds
Domain Reload Profiling:
	ReloadAssembly (2706ms)
		BeginReloadAssembly (157ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2445ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (516ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (113ms)
			SetupLoadedEditorAssemblies (1598ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1267ms)
				ProcessInitializeOnLoadMethodAttributes (214ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 25.55 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10795.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 35.309500 ms (FindLiveObjects: 1.270300 ms CreateObjectMapping: 2.139700 ms MarkObjects: 31.127900 ms  DeleteObjects: 0.770400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017608 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.574 seconds
Domain Reload Profiling:
	ReloadAssembly (2575ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2314ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (486ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (106ms)
			SetupLoadedEditorAssemblies (1503ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1177ms)
				ProcessInitializeOnLoadMethodAttributes (211ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 28.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10812.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 26.846200 ms (FindLiveObjects: 1.037900 ms CreateObjectMapping: 1.276900 ms MarkObjects: 23.871400 ms  DeleteObjects: 0.658700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020275 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 11.17 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.742 seconds
Domain Reload Profiling:
	ReloadAssembly (2742ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2455ms)
			LoadAssemblies (141ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (515ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1623ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1280ms)
				ProcessInitializeOnLoadMethodAttributes (219ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.34 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10829.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 31.007200 ms (FindLiveObjects: 1.068900 ms CreateObjectMapping: 1.225200 ms MarkObjects: 27.900300 ms  DeleteObjects: 0.811500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016272 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.686 seconds
Domain Reload Profiling:
	ReloadAssembly (2686ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2428ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (478ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1647ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1299ms)
				ProcessInitializeOnLoadMethodAttributes (225ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.32 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10846.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 34.123400 ms (FindLiveObjects: 1.668700 ms CreateObjectMapping: 2.038300 ms MarkObjects: 29.565300 ms  DeleteObjects: 0.849900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 6982.768681 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01_lod.FBX
  artifactKey: Guid(f75b10db08ca69d499864e215912b2b7) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01_lod.FBX using Guid(f75b10db08ca69d499864e215912b2b7) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
[16:05:45.9500]:OnPostprocessModel=bake_pubudong01_lod
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'cc2033960ac93ab875aa0c2dafab41cb') in 0.199851 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017708 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.35 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.653 seconds
Domain Reload Profiling:
	ReloadAssembly (2653ms)
		BeginReloadAssembly (165ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (2376ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (475ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1569ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1227ms)
				ProcessInitializeOnLoadMethodAttributes (230ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.19 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9748 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10864.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 20.147700 ms (FindLiveObjects: 0.905600 ms CreateObjectMapping: 1.028900 ms MarkObjects: 17.660600 ms  DeleteObjects: 0.551700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019387 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.46 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.729 seconds
Domain Reload Profiling:
	ReloadAssembly (2730ms)
		BeginReloadAssembly (188ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (77ms)
		EndReloadAssembly (2442ms)
			LoadAssemblies (131ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (500ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1620ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1283ms)
				ProcessInitializeOnLoadMethodAttributes (224ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (25ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 28.56 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9748 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10881.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 30.951000 ms (FindLiveObjects: 1.106400 ms CreateObjectMapping: 1.181600 ms MarkObjects: 28.005100 ms  DeleteObjects: 0.656100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017657 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.41 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.636 seconds
Domain Reload Profiling:
	ReloadAssembly (2637ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2373ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (477ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1588ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1256ms)
				ProcessInitializeOnLoadMethodAttributes (220ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.11 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9748 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10898.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 34.788100 ms (FindLiveObjects: 1.275800 ms CreateObjectMapping: 2.012100 ms MarkObjects: 30.748700 ms  DeleteObjects: 0.749400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1436.790713 seconds.
  path: Assets/RawData/object/models/Volumes/SM_Half_UnitSphere.fbx
  artifactKey: Guid(d3fa727303e9b4131b64322e6cce5b33) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/Volumes/SM_Half_UnitSphere.fbx using Guid(d3fa727303e9b4131b64322e6cce5b33) Importer(-1,00000000000000000000000000000000) [16:29:42.8895]:OnPostprocessModel=SM_Half_UnitSphere
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'e5d871320ce1159729efe6306954b882') in 0.107065 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016064 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.812 seconds
Domain Reload Profiling:
	ReloadAssembly (2812ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2548ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (477ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1740ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1345ms)
				ProcessInitializeOnLoadMethodAttributes (280ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 45.91 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 10916.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 27.704600 ms (FindLiveObjects: 1.252700 ms CreateObjectMapping: 1.315300 ms MarkObjects: 24.467200 ms  DeleteObjects: 0.668400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 719.719316 seconds.
  path: Assets/AssetBundle/UI2/Forms/NoviceGiftPack/UI/xslb_wdt_01.png
  artifactKey: Guid(4b166cd8f40eebe47b06ea847497e529) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/NoviceGiftPack/UI/xslb_wdt_01.png using Guid(4b166cd8f40eebe47b06ea847497e529) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0ddb50bc7b11131f7716c4aee7f70535') in 4.068685 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_03.png
  artifactKey: Guid(a1511026988d69948984448047d5c6fc) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_03.png using Guid(a1511026988d69948984448047d5c6fc) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5dcee8576e650a00cf39ff8e1bcff15f') in 0.035913 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/yellowlight.png
  artifactKey: Guid(d8daf3b176457214ab2513acc0e22904) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/yellowlight.png using Guid(d8daf3b176457214ab2513acc0e22904) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c89a4367667851e4e317bc4e3fe48a40') in 0.022628 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Building/zhalan01_Wood.png
  artifactKey: Guid(a246642c629fcf6469d423a27d8da0c4) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Building/zhalan01_Wood.png using Guid(a246642c629fcf6469d423a27d8da0c4) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd675eec87104fe8af501fd740cb111c7') in 0.023352 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow2.png
  artifactKey: Guid(8512196b05b5c2345a40a259797290ae) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow2.png using Guid(8512196b05b5c2345a40a259797290ae) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0e843de71efd5875b1940ec6d08fad38') in 0.020561 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_02.png
  artifactKey: Guid(8e5b82a691ee1fa408e425e7acac4a84) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_02.png using Guid(8e5b82a691ee1fa408e425e7acac4a84) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa8b065911b73f364b6e712c00d488aa') in 0.014192 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new/yellowlittle.png
  artifactKey: Guid(39caa76bc35b17f4e86bab3cc83ec929) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new/yellowlittle.png using Guid(39caa76bc35b17f4e86bab3cc83ec929) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7468c8eca0345ffb4092fa78970f150d') in 0.021826 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Fight/UITexture/yellowfight.png
  artifactKey: Guid(18f3b87ad60a8674aabd0f92c8a71cfd) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Fight/UITexture/yellowfight.png using Guid(18f3b87ad60a8674aabd0f92c8a71cfd) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2ab27122fa82819da90606685eb5e37e') in 0.016610 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new/yellowBig.png
  artifactKey: Guid(27507179cfdbdc945b47d7d7913e1e6c) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new/yellowBig.png using Guid(27507179cfdbdc945b47d7d7913e1e6c) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61e2d1402562bfbf421a94c20b3fff40') in 0.022805 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow1.png
  artifactKey: Guid(cf49ee0dbdf3f86419030c033461af96) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow1.png using Guid(cf49ee0dbdf3f86419030c033461af96) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b22117e77e5ce329fd366c64d6ce1d3f') in 0.018414 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017132 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.776 seconds
Domain Reload Profiling:
	ReloadAssembly (2777ms)
		BeginReloadAssembly (212ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (105ms)
		EndReloadAssembly (2466ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (479ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1683ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1329ms)
				ProcessInitializeOnLoadMethodAttributes (241ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (25ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 34.56 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 10960.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 49.356900 ms (FindLiveObjects: 2.970700 ms CreateObjectMapping: 2.237400 ms MarkObjects: 43.038800 ms  DeleteObjects: 1.108100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 53.55 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 10960.
Memory consumption went from 299.6 MB to 297.6 MB.
Total: 35.659200 ms (FindLiveObjects: 1.864500 ms CreateObjectMapping: 1.282500 ms MarkObjects: 31.655900 ms  DeleteObjects: 0.854400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 2635.260645 seconds.
  path: Assets/Enviro - Dynamic Enviroment/Weather/Materials/DisplayHeight.mat
  artifactKey: Guid(525717ed74c4cd742a2777393f55afeb) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Enviro - Dynamic Enviroment/Weather/Materials/DisplayHeight.mat using Guid(525717ed74c4cd742a2777393f55afeb) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '22e2806315f7a721bb3dc35d7853058f') in 0.360519 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Raptors_MainCityDisplay.prefab
  artifactKey: Guid(ff54daeb8b0b6264aa2fef3d4f4d908d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Raptors_MainCityDisplay.prefab using Guid(ff54daeb8b0b6264aa2fef3d4f4d908d) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ec3753c0875d0ab6a9f3df529836001f') in 0.120740 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Parasaurolophus_MainCityDisplay.prefab
  artifactKey: Guid(52778519107a21749891e22d5953fa7a) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Parasaurolophus_MainCityDisplay.prefab using Guid(52778519107a21749891e22d5953fa7a) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '14342b0057e38ac23e58359c6c403823') in 0.094459 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016447 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.36 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.480 seconds
Domain Reload Profiling:
	ReloadAssembly (3481ms)
		BeginReloadAssembly (204ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (99ms)
		EndReloadAssembly (3176ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (538ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (2058ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (105ms)
				ProcessInitializeOnLoadAttributes (1662ms)
				ProcessInitializeOnLoadMethodAttributes (267ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (198ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 29.81 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11063.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 26.750900 ms (FindLiveObjects: 1.623200 ms CreateObjectMapping: 1.198900 ms MarkObjects: 23.237000 ms  DeleteObjects: 0.690700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016880 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.38 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.683 seconds
Domain Reload Profiling:
	ReloadAssembly (2684ms)
		BeginReloadAssembly (156ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (2432ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (464ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1640ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1296ms)
				ProcessInitializeOnLoadMethodAttributes (229ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.69 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11080.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 32.415200 ms (FindLiveObjects: 1.340800 ms CreateObjectMapping: 1.279100 ms MarkObjects: 28.860600 ms  DeleteObjects: 0.933100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 69.98 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11080.
Memory consumption went from 302.9 MB to 300.8 MB.
Total: 39.279700 ms (FindLiveObjects: 1.928200 ms CreateObjectMapping: 1.463300 ms MarkObjects: 35.056300 ms  DeleteObjects: 0.830600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018188 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.18 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.023 seconds
Domain Reload Profiling:
	ReloadAssembly (3024ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2752ms)
			LoadAssemblies (131ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (515ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1894ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1529ms)
				ProcessInitializeOnLoadMethodAttributes (250ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 31.11 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11097.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 31.433000 ms (FindLiveObjects: 1.233000 ms CreateObjectMapping: 1.277700 ms MarkObjects: 28.106800 ms  DeleteObjects: 0.814100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020931 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 7.88 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.656 seconds
Domain Reload Profiling:
	ReloadAssembly (2657ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2401ms)
			LoadAssemblies (97ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (498ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (105ms)
			SetupLoadedEditorAssemblies (1573ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (106ms)
				ProcessInitializeOnLoadAttributes (1217ms)
				ProcessInitializeOnLoadMethodAttributes (225ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (35ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.29 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11114.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 33.856600 ms (FindLiveObjects: 1.290600 ms CreateObjectMapping: 1.054700 ms MarkObjects: 30.704100 ms  DeleteObjects: 0.805600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 59.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11114.
Memory consumption went from 302.9 MB to 300.9 MB.
Total: 39.095700 ms (FindLiveObjects: 1.185600 ms CreateObjectMapping: 1.504400 ms MarkObjects: 35.282300 ms  DeleteObjects: 1.122300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016515 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.832 seconds
Domain Reload Profiling:
	ReloadAssembly (2833ms)
		BeginReloadAssembly (164ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2571ms)
			LoadAssemblies (104ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (473ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1759ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1398ms)
				ProcessInitializeOnLoadMethodAttributes (245ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 35.12 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11131.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.023800 ms (FindLiveObjects: 1.143100 ms CreateObjectMapping: 1.175200 ms MarkObjects: 32.860700 ms  DeleteObjects: 0.843000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018357 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.90 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.629 seconds
Domain Reload Profiling:
	ReloadAssembly (2630ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2371ms)
			LoadAssemblies (102ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (475ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1580ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1251ms)
				ProcessInitializeOnLoadMethodAttributes (217ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.67 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11148.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.466900 ms (FindLiveObjects: 1.272000 ms CreateObjectMapping: 1.153300 ms MarkObjects: 33.187800 ms  DeleteObjects: 0.852100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 59.58 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11148.
Memory consumption went from 303.0 MB to 300.9 MB.
Total: 37.265600 ms (FindLiveObjects: 1.101900 ms CreateObjectMapping: 1.443500 ms MarkObjects: 33.645000 ms  DeleteObjects: 1.073700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016791 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.96 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.637 seconds
Domain Reload Profiling:
	ReloadAssembly (2638ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2371ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (450ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1599ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1268ms)
				ProcessInitializeOnLoadMethodAttributes (218ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 38.39 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11165.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 49.660900 ms (FindLiveObjects: 1.805600 ms CreateObjectMapping: 7.199200 ms MarkObjects: 39.734700 ms  DeleteObjects: 0.919800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 61.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11165.
Memory consumption went from 303.0 MB to 300.9 MB.
Total: 33.832000 ms (FindLiveObjects: 1.252700 ms CreateObjectMapping: 1.425100 ms MarkObjects: 30.281900 ms  DeleteObjects: 0.870800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020639 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.39 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.802 seconds
Domain Reload Profiling:
	ReloadAssembly (2803ms)
		BeginReloadAssembly (160ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2543ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1722ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1362ms)
				ProcessInitializeOnLoadMethodAttributes (239ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 31.95 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11182.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 22.406900 ms (FindLiveObjects: 0.921700 ms CreateObjectMapping: 0.896500 ms MarkObjects: 19.709600 ms  DeleteObjects: 0.878000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016092 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.72 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.612 seconds
Domain Reload Profiling:
	ReloadAssembly (2613ms)
		BeginReloadAssembly (154ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2358ms)
			LoadAssemblies (107ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (470ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1564ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1230ms)
				ProcessInitializeOnLoadMethodAttributes (217ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11199.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.707200 ms (FindLiveObjects: 1.151200 ms CreateObjectMapping: 1.725600 ms MarkObjects: 31.940200 ms  DeleteObjects: 1.889000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017316 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.06 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.817 seconds
Domain Reload Profiling:
	ReloadAssembly (2818ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2565ms)
			LoadAssemblies (113ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (477ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1752ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1377ms)
				ProcessInitializeOnLoadMethodAttributes (259ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (37ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 32.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11216.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 34.854600 ms (FindLiveObjects: 2.107400 ms CreateObjectMapping: 1.509000 ms MarkObjects: 30.221800 ms  DeleteObjects: 1.014600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016672 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.30 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.607 seconds
Domain Reload Profiling:
	ReloadAssembly (2608ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2340ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (468ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1544ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1208ms)
				ProcessInitializeOnLoadMethodAttributes (219ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 40.11 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11233.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.967900 ms (FindLiveObjects: 1.207100 ms CreateObjectMapping: 1.898200 ms MarkObjects: 32.312500 ms  DeleteObjects: 1.548300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 61.78 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11233.
Memory consumption went from 303.1 MB to 301.0 MB.
Total: 37.636600 ms (FindLiveObjects: 1.228700 ms CreateObjectMapping: 1.392500 ms MarkObjects: 33.806500 ms  DeleteObjects: 1.207600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016374 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.53 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.946 seconds
Domain Reload Profiling:
	ReloadAssembly (2946ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (2697ms)
			LoadAssemblies (399ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (463ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1610ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (13ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1255ms)
				ProcessInitializeOnLoadMethodAttributes (232ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 36.40 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11250.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 35.989300 ms (FindLiveObjects: 1.298800 ms CreateObjectMapping: 1.336400 ms MarkObjects: 32.388000 ms  DeleteObjects: 0.964500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018664 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.608 seconds
Domain Reload Profiling:
	ReloadAssembly (2609ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2350ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (468ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1540ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1204ms)
				ProcessInitializeOnLoadMethodAttributes (215ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.70 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11267.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 40.320900 ms (FindLiveObjects: 1.358100 ms CreateObjectMapping: 1.257300 ms MarkObjects: 36.389700 ms  DeleteObjects: 1.314300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 61.62 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11267.
Memory consumption went from 303.1 MB to 301.1 MB.
Total: 38.797300 ms (FindLiveObjects: 1.403600 ms CreateObjectMapping: 1.400200 ms MarkObjects: 34.928000 ms  DeleteObjects: 1.063900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016501 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.767 seconds
Domain Reload Profiling:
	ReloadAssembly (2767ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2501ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (479ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1301ms)
				ProcessInitializeOnLoadMethodAttributes (265ms)
				AfterProcessingInitializeOnLoad (6ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 32.03 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11284.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 32.695100 ms (FindLiveObjects: 1.151400 ms CreateObjectMapping: 1.061800 ms MarkObjects: 29.558300 ms  DeleteObjects: 0.921700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 40.40 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 169 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11284.
Memory consumption went from 303.2 MB to 301.1 MB.
Total: 20.676100 ms (FindLiveObjects: 0.867500 ms CreateObjectMapping: 0.936300 ms MarkObjects: 18.258400 ms  DeleteObjects: 0.612700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 57910.532748 seconds.
  path: Assets/RawData/object/testtree/c_rock_huoshanshi01d.png
  artifactKey: Guid(56b49caccba1e9d4ebb00ceb893591f5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/testtree/c_rock_huoshanshi01d.png using Guid(56b49caccba1e9d4ebb00ceb893591f5) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0c4d2b7fbd686d23243fc38e13eab01d') in 0.342634 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.629539 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Flying_Turn90R.fbx
  artifactKey: Guid(90f7126f8198844418ef6c30a088613b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Flying_Turn90R.fbx using Guid(90f7126f8198844418ef6c30a088613b) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 7.883869%;
[09:30:52.9326]:OnPostprocessModel=Anim_SciFi_Hydra@Flying_Turn90R
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '3640b593c4cc1ab72f9e58656828b4f4') in 20.054616 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_B.fbx
  artifactKey: Guid(8de263cded5d8da4489a537327250673) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_B.fbx using Guid(8de263cded5d8da4489a537327250673) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 13.955079%;
[09:31:12.3408]:OnPostprocessModel=Anim_SciFi_Hydra@GetHit_B
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'acede42d87b221b7006f41f856e97343') in 19.624157 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(77e58b0269b5d8348a794be4061db277) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(77e58b0269b5d8348a794be4061db277) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 18.213678%;
[09:31:31.9565]:OnPostprocessModel=Anim_SciFi_Hydra@KnockDeath
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '1732665a80755e5184d8f9a5ee27ffea') in 24.701552 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(ff908bac85deadf40b11c3edea388eba) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(ff908bac85deadf40b11c3edea388eba) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.176016%;
[09:31:56.8104]:OnPostprocessModel=Anim_SciFi_Hydra@TransitionIdle2ToIdle3
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'a0b764976787f660d4c69c216d1f5201') in 24.268308 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_F.fbx
  artifactKey: Guid(48998a5e53247294aa61108d906f7714) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_F.fbx using Guid(48998a5e53247294aa61108d906f7714) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 10.836529%;
[09:32:21.0099]:OnPostprocessModel=Anim_SciFi_Hydra@GetHit_F
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '555280070fd09af73f1a47c6d1c5cfdf') in 23.494688 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Attack12_Flying.fbx
  artifactKey: Guid(f2bdbae143ff806409cebcd70ca71339) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Attack12_Flying.fbx using Guid(f2bdbae143ff806409cebcd70ca71339) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 9.041017%;
[09:32:44.6558]:OnPostprocessModel=Anim_SciFi_Hydra@Attack12_Flying
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '5baf978327885191208b15cf21b66864') in 23.038292 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(a4f50b509668bd24d9dd7d34b0410fd4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(a4f50b509668bd24d9dd7d34b0410fd4) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 14.988855%;
[09:33:07.6263]:OnPostprocessModel=Anim_SciFi_Hydra@Attack1
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'e833c89703afd16c12c0eddd85869756') in 27.911981 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(a2a1703a92106054c82ed1ba1195b17f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(a2a1703a92106054c82ed1ba1195b17f) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.552934%;
[09:33:35.5494]:OnPostprocessModel=Anim_SciFi_Hydra@Attack2
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '7d346ba789740ff2c80cc4e4e9a2aa5c') in 28.360422 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(07c3f76da619f884c8821769f6c0017a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(07c3f76da619f884c8821769f6c0017a) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 14.349169%;
[09:34:03.9492]:OnPostprocessModel=Anim_SciFi_Hydra@TransitionIdle3ToIdle2
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '29560dc5812d6dae2cc08265bfe7ef2b') in 25.468113 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_R2.fbx
  artifactKey: Guid(2ec9466f9da1b4a449d6695417b6bdc3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_R2.fbx using Guid(2ec9466f9da1b4a449d6695417b6bdc3) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 10.757558%;
[09:34:29.3589]:OnPostprocessModel=Anim_SciFi_Hydra@GetHit_R2
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'a88574326d56458fd0b66328774cc152') in 21.466485 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(2d021318fa736374fbb8d4ced2cb9612) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(2d021318fa736374fbb8d4ced2cb9612) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 17.401022%;
[09:34:50.8617]:OnPostprocessModel=Anim_SciFi_Hydra@FallDeath
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '389f0a44d640b98bec9ba2727782a767') in 20.388878 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Fly_F.fbx
  artifactKey: Guid(98f3b162611833a4c8681dbe33e25b40) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Fly_F.fbx using Guid(98f3b162611833a4c8681dbe33e25b40) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 6.772201%;
[09:35:11.4882]:OnPostprocessModel=Anim_SciFi_Hydra@Fly_F
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'c2774f0f10561542d8ca29cc23175bf0') in 20.252263 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(9eb4e5a6d02c94349923156e4e510889) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(9eb4e5a6d02c94349923156e4e510889) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 9.673539%;
[09:35:31.7742]:OnPostprocessModel=Anim_SciFi_Hydra@Idle3
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '9b2fd27712f99b22361b0f51e72788a9') in 27.114950 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Flying_GetHit_L.fbx
  artifactKey: Guid(bbc8f3bb9b4916d40b8061ba5f862b7e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Flying_GetHit_L.fbx using Guid(bbc8f3bb9b4916d40b8061ba5f862b7e) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 13.969670%;
[09:35:58.8064]:OnPostprocessModel=Anim_SciFi_Hydra@Flying_GetHit_L
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '5e56bb365a669a69d9aa7820cde2f402') in 21.177346 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Attack13_Flying.fbx
  artifactKey: Guid(20df4c9a952177a4a84b716c2740996e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Attack13_Flying.fbx using Guid(20df4c9a952177a4a84b716c2740996e) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 8.033716%;
[09:36:20.0676]:OnPostprocessModel=Anim_SciFi_Hydra@Attack13_Flying
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '27373d18cb5c0020d0f0ea208659ff54') in 26.777985 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_F2.fbx
  artifactKey: Guid(40f6add5fc28d3248b9add6f90343252) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@GetHit_F2.fbx using Guid(40f6add5fc28d3248b9add6f90343252) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 9.818097%;
[09:36:46.7574]:OnPostprocessModel=Anim_SciFi_Hydra@GetHit_F2
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'c4ad7e01798fbde5a347b9c12e6d757a') in 22.843301 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(6b68d89a280966e46bf1a8802e28d791) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(6b68d89a280966e46bf1a8802e28d791) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.018360%;
[09:37:09.5907]:OnPostprocessModel=Anim_SciFi_Hydra@Turn90R
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '8b24851566368abbdeb8fea6fecf22f0') in 24.322311 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Fly_B.fbx
  artifactKey: Guid(166b4edc74c0d414ca09135ed6d1bf38) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Anim_SciFi_Hydra@Fly_B.fbx using Guid(166b4edc74c0d414ca09135ed6d1bf38) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 6.330390%;
[09:37:34.1701]:OnPostprocessModel=Anim_SciFi_Hydra@Fly_B
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'c153b031882ce69340dbfa9c177e1e6a') in 22.741014 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Hydra@Boss_Stronghold.fbx
  artifactKey: Guid(c6e830d212008294989a45f2b67d1ded) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Hydra@Boss_Stronghold.fbx using Guid(c6e830d212008294989a45f2b67d1ded) Importer(-1,00000000000000000000000000000000) [09:37:56.6274]:OnPostprocessModel=Hydra@Boss_Stronghold
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

ImportFBX Warnings:
Mesh 'Hydra@Boss_Stronghold_Wings_01' has 16 (out of 1832) vertices with no weight and bone assigned (they will be assigned to bone #0 with weight 1). The list of vertices: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 and so on...
 -> (artifact id: '1a7c02609dae764b2ade6f6a65b1ba98') in 0.241047 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Hydra@Boss_Stronghold.fbx' had errors: ImportFBX Warnings:
Mesh 'Hydra@Boss_Stronghold_Wings_01' has 16 (out of 1832) vertices with no weight and bone assigned (they will be assigned to bone #0 with weight 1). The list of vertices: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 and so on...

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL>
  artifactKey: Guid(fb8ad49c42ddea044bc3e4452d6822b9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/<EMAIL> using Guid(fb8ad49c42ddea044bc3e4452d6822b9) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 16.219566%;
[09:37:57.2717]:OnPostprocessModel=Anim_SciFi_Hydra@KnockOutStart
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'c47414381a86453b8a310adcfa6453a1') in 27.683209 seconds 
========================================================================
Received Import Request.
  Time since last request: 5.782071 seconds.
  path: Assets/RawData/object/models/outdoors/uv_yanjiang01_01.FBX
  artifactKey: Guid(eb95a1b78adf9e14ca6cc2d6de796944) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/uv_yanjiang01_01.FBX using Guid(eb95a1b78adf9e14ca6cc2d6de796944) Importer(-1,00000000000000000000000000000000) [09:38:30.1938]:OnPostprocessModel=uv_yanjiang01_01
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '92591fa4a7b40ff30c3e8add768336c9') in 0.022206 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0