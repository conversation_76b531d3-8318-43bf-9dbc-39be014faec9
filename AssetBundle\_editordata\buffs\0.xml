<?xml version="1.0" encoding="utf-8"?>
<BuffTemplate type="DeepCore.GameData.Zone.BuffTemplate">
  <AppearanceId>0</AppearanceId>
  <BanAttack>False</BanAttack>
  <BanInteract>False</BanInteract>
  <BanMove>False</BanMove>
  <BanUseItem>False</BanUseItem>
  <BindingEffect>
    <BindBody>False</BindBody>
    <BindBodyDirection>True</BindBodyDirection>
    <BindInjuredIocation>False</BindInjuredIocation>
    <BlurBeginTime>0</BlurBeginTime>
    <BlurEndTime>0</BlurEndTime>
    <BlurStrength>0.00</BlurStrength>
    <BlurWaitTime>0</BlurWaitTime>
    <CameraBeginTime>0</CameraBeginTime>
    <CameraDistance>0.00</CameraDistance>
    <CameraEndTime>0</CameraEndTime>
    <CameraWaitTime>0</CameraWaitTime>
    <EarthQuakeMS>0</EarthQuakeMS>
    <EarthQuakeXYZ>0.00</EarthQuakeXYZ>
    <EffectTimeMS>0</EffectTimeMS>
    <IsLoop>False</IsLoop>
    <ScaleToBodySize>0.00</ScaleToBodySize>
    <WarnDegree>80.00</WarnDegree>
    <WarnScaleX>1.00</WarnScaleX>
    <WarnScaleZ>1.00</WarnScaleZ>
    <WarnSpeed>1.00</WarnSpeed>
    <WarnType>WARNING_TYPE_NONE</WarnType>
    <property.SerialNumber>6016933</property.SerialNumber>
  </BindingEffect>
  <BindingEffectList element_type="DeepCore.GameData.Zone.LaunchEffect">
    <element>
      <BindBody>False</BindBody>
      <BindBodyDirection>True</BindBodyDirection>
      <BindInjuredIocation>False</BindInjuredIocation>
      <BlurBeginTime>0</BlurBeginTime>
      <BlurEndTime>0</BlurEndTime>
      <BlurStrength>0.00</BlurStrength>
      <BlurWaitTime>0</BlurWaitTime>
      <CameraBeginTime>0</CameraBeginTime>
      <CameraDistance>0.00</CameraDistance>
      <CameraEndTime>0</CameraEndTime>
      <CameraWaitTime>0</CameraWaitTime>
      <EarthQuakeMS>0</EarthQuakeMS>
      <EarthQuakeXYZ>0.00</EarthQuakeXYZ>
      <EffectTimeMS>0</EffectTimeMS>
      <IsLoop>False</IsLoop>
      <ScaleToBodySize>0.00</ScaleToBodySize>
      <WarnDegree>80.00</WarnDegree>
      <WarnScaleX>1.00</WarnScaleX>
      <WarnScaleZ>1.00</WarnScaleZ>
      <WarnSpeed>1.00</WarnSpeed>
      <WarnType>WARNING_TYPE_NONE</WarnType>
      <property.SerialNumber>6016934</property.SerialNumber>
    </element>
    <element>
      <BindBody>False</BindBody>
      <BindBodyDirection>True</BindBodyDirection>
      <BindInjuredIocation>False</BindInjuredIocation>
      <BlurBeginTime>0</BlurBeginTime>
      <BlurEndTime>0</BlurEndTime>
      <BlurStrength>0.00</BlurStrength>
      <BlurWaitTime>0</BlurWaitTime>
      <CameraBeginTime>0</CameraBeginTime>
      <CameraDistance>0.00</CameraDistance>
      <CameraEndTime>0</CameraEndTime>
      <CameraWaitTime>0</CameraWaitTime>
      <EarthQuakeMS>0</EarthQuakeMS>
      <EarthQuakeXYZ>0.00</EarthQuakeXYZ>
      <EffectTimeMS>0</EffectTimeMS>
      <IsLoop>False</IsLoop>
      <ScaleToBodySize>0.00</ScaleToBodySize>
      <WarnDegree>80.00</WarnDegree>
      <WarnScaleX>1.00</WarnScaleX>
      <WarnScaleZ>1.00</WarnScaleZ>
      <WarnSpeed>1.00</WarnSpeed>
      <WarnType>WARNING_TYPE_NONE</WarnType>
      <property.SerialNumber>6016935</property.SerialNumber>
    </element>
  </BindingEffectList>
  <BodyHitSize>0.00</BodyHitSize>
  <BodyScale>1.00</BodyScale>
  <BodySize>0.00</BodySize>
  <ClientVisible>True</ClientVisible>
  <EndKeyFrame>
    <Actions element_type="DeepCore.GameData.Zone.BuffKeyFrameAction" />
    <FrameMS>0</FrameMS>
  </EndKeyFrame>
  <ExclusiveCatgory>0</ExclusiveCatgory>
  <ExclusivePriority>0</ExclusivePriority>
  <FirstTimeEnable>True</FirstTimeEnable>
  <HitIntervalMS>0</HitIntervalMS>
  <HitKeyFrame>
    <Actions element_type="DeepCore.GameData.Zone.BuffKeyFrameAction" />
    <FrameMS>0</FrameMS>
  </HitKeyFrame>
  <ID>0</ID>
  <IsCancelBySelf>False</IsCancelBySelf>
  <IsClientManagedMove>False</IsClientManagedMove>
  <IsDuplicating>False</IsDuplicating>
  <IsHarmful>False</IsHarmful>
  <IsInvincible>False</IsInvincible>
  <IsInvisible>False</IsInvisible>
  <IsNoneBlock>False</IsNoneBlock>
  <IsOverlay>False</IsOverlay>
  <IsPassive>False</IsPassive>
  <IsRemoveOnSenderRemoved>False</IsRemoveOnSenderRemoved>
  <IsSilent>False</IsSilent>
  <KeyFrames element_type="DeepCore.GameData.Zone.BuffTemplate+KeyFrame">
    <element>
      <Actions element_type="DeepCore.GameData.Zone.BuffKeyFrameAction" />
      <FrameMS>0</FrameMS>
    </element>
  </KeyFrames>
  <LifeTimeMS>0</LifeTimeMS>
  <MakeAvatar>False</MakeAvatar>
  <MakeStun>False</MakeStun>
  <MaxOverlay>1</MaxOverlay>
  <OverlayBindingEffect element_type="DeepCore.GameData.Zone.LaunchEffect" />
  <Properties type="BWBattle.Common.Plugins.BWBuffProperties">
    <CanBePurged>True</CanBePurged>
    <SaveOnChangeScene>True</SaveOnChangeScene>
  </Properties>
  <UnitBaseSkillID>
    <AutoLaunch>True</AutoLaunch>
    <Priority>0</Priority>
    <SkillID>0</SkillID>
    <property.SerialNumber>6016936</property.SerialNumber>
  </UnitBaseSkillID>
  <UnitChangeSkills>True</UnitChangeSkills>
  <UnitKeepSkillsID element_type="System.Int32">
    <element>0</element>
  </UnitKeepSkillsID>
  <UnitSkills element_type="DeepCore.GameData.Zone.LaunchSkill">
    <element>
      <AutoLaunch>True</AutoLaunch>
      <Priority>0</Priority>
      <SkillID>0</SkillID>
      <property.SerialNumber>6016896</property.SerialNumber>
    </element>
  </UnitSkills>
  <property.EditorPath>BUFF/(0)</property.EditorPath>
</BuffTemplate>