Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker8.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [30664] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1368776504 [EditorId] 1368776504 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [30664] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1368776504 [EditorId] 1368776504 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 925.71 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56092
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001927 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 884.81 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.469 seconds
Domain Reload Profiling:
	ReloadAssembly (1469ms)
		BeginReloadAssembly (64ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1311ms)
			LoadAssemblies (63ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (117ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (1083ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (885ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (137ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016349 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 866.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.

=================================================================
	Native Crash Reporting
=================================================================
Got a UNKNOWN while executing native code. This usually indicates
a fatal error in the mono runtime or one of the native libraries 
used by your application.
=================================================================

=================================================================
	Managed Stacktrace:
=================================================================
	  at <unknown> <0xffffffff>
	  at System.String:FastAllocateString <0x000c1>
	  at System.String:CreateStringFromEncoding <0x0015a>
	  at System.Text.Encoding:GetString <0x001fa>
	  at System.Text.Encoding:GetString <0x00162>
	  at System.String:Ctor <0x004b2>
	  at System.String:CreateString <0x0008a>
	  at System.String:.ctor <0x00092>
	  at Mono.RuntimeMarshal:PtrToUtf8String <0x00362>
	  at System.Reflection.AssemblyName:FillName <0x000a2>
	  at System.Reflection.Assembly:GetReferencedAssemblies <0x0036a>
	  at System.Reflection.RuntimeAssembly:GetReferencedAssemblies <0x00072>
	  at CollectGizmoDrawers:.cctor <0x004cb>
	  at System.Object:runtime_invoke_void <0x00184>
	  at <unknown> <0xffffffff>
	  at System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor <0x000c1>
	  at System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor <0x0019a>
	  at UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes <0x00652>
	  at <Module>:runtime_invoke_void_object <0x0018a>
=================================================================
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.659 seconds
Domain Reload Profiling:
	ReloadAssembly (3660ms)
		BeginReloadAssembly (106ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3461ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (467ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (204ms)
			SetupLoadedEditorAssemblies (2626ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (867ms)
				BeforeProcessingInitializeOnLoad (126ms)
				ProcessInitializeOnLoadAttributes (1345ms)
				ProcessInitializeOnLoadMethodAttributes (275ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 10.27 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 25.842900 ms (FindLiveObjects: 0.907400 ms CreateObjectMapping: 1.122200 ms MarkObjects: 23.079800 ms  DeleteObjects: 0.732200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016981 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.31 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.752 seconds
Domain Reload Profiling:
	ReloadAssembly (2752ms)
		BeginReloadAssembly (165ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2486ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (517ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1654ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1291ms)
				ProcessInitializeOnLoadMethodAttributes (252ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.96 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10759.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 31.300300 ms (FindLiveObjects: 1.063600 ms CreateObjectMapping: 1.108900 ms MarkObjects: 28.516700 ms  DeleteObjects: 0.609500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 64.87 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10759.
Memory consumption went from 296.1 MB to 294.1 MB.
Total: 39.581400 ms (FindLiveObjects: 1.248500 ms CreateObjectMapping: 1.693600 ms MarkObjects: 35.674200 ms  DeleteObjects: 0.963900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016265 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.24 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.908 seconds
Domain Reload Profiling:
	ReloadAssembly (2909ms)
		BeginReloadAssembly (165ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2644ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (499ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1819ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1432ms)
				ProcessInitializeOnLoadMethodAttributes (268ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.51 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10776.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 49.853300 ms (FindLiveObjects: 1.442100 ms CreateObjectMapping: 2.571000 ms MarkObjects: 44.809200 ms  DeleteObjects: 1.029200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016488 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.779 seconds
Domain Reload Profiling:
	ReloadAssembly (2779ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2512ms)
			LoadAssemblies (128ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (478ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1708ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (13ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1340ms)
				ProcessInitializeOnLoadMethodAttributes (247ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10793.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 38.023500 ms (FindLiveObjects: 1.236100 ms CreateObjectMapping: 1.498800 ms MarkObjects: 34.367800 ms  DeleteObjects: 0.919600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.027332 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.82 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.714 seconds
Domain Reload Profiling:
	ReloadAssembly (2715ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2440ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (491ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1650ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (13ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1289ms)
				ProcessInitializeOnLoadMethodAttributes (241ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.80 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10810.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.800900 ms (FindLiveObjects: 1.372500 ms CreateObjectMapping: 1.613700 ms MarkObjects: 32.587500 ms  DeleteObjects: 1.225700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 59.52 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10810.
Memory consumption went from 296.2 MB to 294.2 MB.
Total: 33.428300 ms (FindLiveObjects: 1.109100 ms CreateObjectMapping: 1.447300 ms MarkObjects: 30.097400 ms  DeleteObjects: 0.773200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 301991.997556 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Attr.prefab
  artifactKey: Guid(dac71075febc7a0459248cc6381ab825) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Attr.prefab using Guid(dac71075febc7a0459248cc6381ab825) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ebe44b60825363c5985f29dde9c647d') in 0.118567 seconds 
========================================================================
Received Import Request.
  Time since last request: 6853.402487 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_选中页签.png
  artifactKey: Guid(c66ed3529dc2f3649a2e22b7e18607e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_选中页签.png using Guid(c66ed3529dc2f3649a2e22b7e18607e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f470eb08c9464b58d96bd1318f66a74e') in 0.160093 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png
  artifactKey: Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_一级页签_选中状态.png using Guid(b4fd026b6bf4a9e49bf7f55fc9986d5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '40c59754fad3dc34d78df1efd6ef24e5') in 0.021762 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png
  artifactKey: Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承底.png using Guid(ecb21a806d7772d46a576d8ba30704ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9ca88a901b740377444f83164c9b5025') in 0.058991 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png
  artifactKey: Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承_箭头_绿.png using Guid(b66d9eb1aa96d9f46a80f4ec7622f39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e833469af2bcb856941201523c631b70') in 0.016949 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png
  artifactKey: Guid(f10b434dc0d88e843a234a889491c87e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/解锁条件底.png using Guid(f10b434dc0d88e843a234a889491c87e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b213d9d1ab8c7c12bcec2560031b7ac') in 0.019066 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png
  artifactKey: Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造共鸣_二级页签_选中状态.png using Guid(d8bffcb83d9d81643930f2cc55ac659c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5a96e14218cd6e1e9c139a2afed8a655') in 0.019091 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png
  artifactKey: Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/锻造继承.png using Guid(9b9da4e1031604d49a373b0bcdbadb48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4f743f9045459fe8eaf55b25eee5aa1c') in 0.012484 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png
  artifactKey: Guid(74ece5bb61de54a44be3ae46229114f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/词条替换_标题底.png using Guid(74ece5bb61de54a44be3ae46229114f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a823651db52390d45f4331b4eaeaa705') in 0.016590 seconds 
========================================================================
Received Import Request.
  Time since last request: 6.004290 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png
  artifactKey: Guid(67ece416bc775484194197f6cf09b57c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料标注线_中.png using Guid(67ece416bc775484194197f6cf09b57c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b40af4f224e915eebe31b2aa290953f') in 0.020107 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_未选中页签.png
  artifactKey: Guid(873d749b49e194b4c8c50036ddfbcd38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/装备车床_未选中页签.png using Guid(873d749b49e194b4c8c50036ddfbcd38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b9adb12edd6cc81e66a269677b78137a') in 0.016123 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png
  artifactKey: Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_01.png using Guid(0dcc69a429a7c14409e85a2d8e1c55e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c1e6abb78f1805098efce37f0542ea60') in 0.012269 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png
  artifactKey: Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/材料选择_列表_02.png using Guid(265c490d41cbfc449ad384fcce1edcfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7d5eab6f99df5f3439ab4b9169cc654e') in 0.013523 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png
  artifactKey: Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/箭头_绿.png using Guid(b671026db0f78ad48ba9d6ddbbc3c644) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ab6633b21f9d57c6190192ae921b32cf') in 0.018009 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.253643 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png
  artifactKey: Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/tips_标题装饰.png using Guid(3f68e49d4f828a24eb9969941b43dab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8fc49fecfa40145944a55a995b2b76d2') in 0.014005 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png
  artifactKey: Guid(c50865357860f2a41beb1c6710959063) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/小标题.png using Guid(c50865357860f2a41beb1c6710959063) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e470fac3ab8458043277e6395808e9c6') in 0.020426 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png
  artifactKey: Guid(e3de519afda6aed40a772fc631ac9683) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/一级页签底.png using Guid(e3de519afda6aed40a772fc631ac9683) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '400111b096aaa93a741e72f304b29bec') in 0.015498 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png
  artifactKey: Guid(119e90e156b51e949b2819759fe32677) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/UITexture/属性分割线.png using Guid(119e90e156b51e949b2819759fe32677) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '36e5b74084330de550051c3321f6ed44') in 0.011160 seconds 
========================================================================
Received Import Request.
  Time since last request: 486.259103 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Attr.prefab
  artifactKey: Guid(dac71075febc7a0459248cc6381ab825) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Attr.prefab using Guid(dac71075febc7a0459248cc6381ab825) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '473a424f194effb9ab31001bcb85ce54') in 0.008442 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.327044 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ddf138fe556097b73869087d495b80d3') in 0.039647 seconds 
========================================================================
Received Import Request.
  Time since last request: 11.330901 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4eca09058b3b657027fde529864e0dc1') in 0.022721 seconds 
========================================================================
Received Import Request.
  Time since last request: 20.232168 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5c29edacf25ff607f0b2a3f0dbe84d2f') in 0.087420 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.436585 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip
  artifactKey: Guid(0e759e8a736cdee4ea4c28f2ea1ef975) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip using Guid(0e759e8a736cdee4ea4c28f2ea1ef975) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '199e9248f578f696ab6904e102667165') in 0.001181 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.090676 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipDetail.prefab
  artifactKey: Guid(22e86007fade37a41a29188d58c53c84) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipDetail.prefab using Guid(22e86007fade37a41a29188d58c53c84) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e1c54dbec2ab7804bef8f5d82cac38eb') in 0.021440 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/SynthesisItem.prefab
  artifactKey: Guid(259a0255a6e5d644faf2c941e7078a88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/SynthesisItem.prefab using Guid(259a0255a6e5d644faf2c941e7078a88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4b8241bdd2a9700ecf99249f3eda9fc3') in 0.009648 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/WGChip.prefab
  artifactKey: Guid(9ee22effe3bbe924e861f41240e8d455) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/WGChip.prefab using Guid(9ee22effe3bbe924e861f41240e8d455) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '604583fe48f18d28e53b1f29b2d562c2') in 0.007034 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipSynthesis.prefab
  artifactKey: Guid(3d6b58ee5dd48b64c80fc0baea6bdd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipSynthesis.prefab using Guid(3d6b58ee5dd48b64c80fc0baea6bdd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '979b879fcf6986d28f809e2a41d28b7d') in 0.010314 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0