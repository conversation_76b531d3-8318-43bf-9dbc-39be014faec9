Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker27
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker27.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [2496] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3320643558 [EditorId] 3320643558 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [2496] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3320643558 [EditorId] 3320643558 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 914.51 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56696
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002067 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 830.00 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.361 seconds
Domain Reload Profiling:
	ReloadAssembly (1361ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1194ms)
			LoadAssemblies (70ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (102ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (1004ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (830ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (120ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.017564 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 878.58 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.767 seconds
Domain Reload Profiling:
	ReloadAssembly (3768ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3567ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (479ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (197ms)
			SetupLoadedEditorAssemblies (2694ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (879ms)
				BeforeProcessingInitializeOnLoad (126ms)
				ProcessInitializeOnLoadAttributes (1397ms)
				ProcessInitializeOnLoadMethodAttributes (279ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 13.34 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10226 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10747.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 24.229500 ms (FindLiveObjects: 1.080900 ms CreateObjectMapping: 1.176600 ms MarkObjects: 21.406500 ms  DeleteObjects: 0.564300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 554947.033453 seconds.
  path: Assets/RawData/object/models/build/c_build_sanjiaoqiangbi01_zhongshi.FBX
  artifactKey: Guid(f4cef59b03b0c7c48b5f03434a0fb1fa) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/build/c_build_sanjiaoqiangbi01_zhongshi.FBX using Guid(f4cef59b03b0c7c48b5f03434a0fb1fa) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
[10:44:44.3299]:OnPostprocessModel=c_build_sanjiaoqiangbi01_zhongshi
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '5876315ac07f66c070c01ec43e4883ed') in 0.275841 seconds 
========================================================================
Received Import Request.
  Time since last request: 138.388826 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/AutoRifleTurret/UITexture/line.png
  artifactKey: Guid(8aafbe9605ee1974fa44795e4c676118) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/AutoRifleTurret/UITexture/line.png using Guid(8aafbe9605ee1974fa44795e4c676118) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fdbc02abb8a33607cdfdefb9854cdba0') in 0.147838 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_bg_blue.png
  artifactKey: Guid(d2f118f20370435478c7229687f316ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_bg_blue.png using Guid(d2f118f20370435478c7229687f316ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c24ec2ad56a194a2e9a30f94baef831c') in 0.057459 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_bg_green.png
  artifactKey: Guid(3ed0ed88b22873547a6b4a70e7b38f00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_bg_green.png using Guid(3ed0ed88b22873547a6b4a70e7b38f00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd5f5284cbcb23360d8771c9ab132be45') in 0.041519 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/Guild/GuildUI/lineImage.png
  artifactKey: Guid(56a1cf2cf2230af44be102803321af51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Guild/GuildUI/lineImage.png using Guid(56a1cf2cf2230af44be102803321af51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e807cabcb59a5861a33ea9acab6ade55') in 0.022037 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/line3.png
  artifactKey: Guid(4f258fbde0b771b488f02075674d5131) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/line3.png using Guid(4f258fbde0b771b488f02075674d5131) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0554a493a6d3b659ac4f84ccca09a12d') in 0.015853 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/SelectMode/SelectModeUI/list_di.png
  artifactKey: Guid(dc432569902c34a4682aae3cbe303904) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SelectMode/SelectModeUI/list_di.png using Guid(dc432569902c34a4682aae3cbe303904) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '04017f4ff8c455ee58d750aa2cc75281') in 0.014893 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/line2.png
  artifactKey: Guid(42a93f55e74fa474d9636e125be5e447) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/line2.png using Guid(42a93f55e74fa474d9636e125be5e447) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '767b091cd4e16c85332b2b32f864f948') in 0.018074 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Closet/ClosetUI/list-empty-L.png
  artifactKey: Guid(8c15d1391c0a7614e97a6a0c6490f86f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Closet/ClosetUI/list-empty-L.png using Guid(8c15d1391c0a7614e97a6a0c6490f86f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2798808b2c05b3ef20ca9d7dcb8a375b') in 0.025393 seconds 
========================================================================
Received Import Request.
  Time since last request: 11.446913 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_title_green.png
  artifactKey: Guid(3912437975d539c439b0771d4094a38c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/UltimateTalent/UITexture/list_title_green.png using Guid(3912437975d539c439b0771d4094a38c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0144274c98df2fa234eab9d277eace05') in 0.028461 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/MonthlyCard/little_title_zhizhun.png
  artifactKey: Guid(5a3d39144761f0047b4c52f25b43d36a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/MonthlyCard/little_title_zhizhun.png using Guid(5a3d39144761f0047b4c52f25b43d36a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9a4d43b171455ffde26a794c6105e33c') in 0.027635 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018000 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.357 seconds
Domain Reload Profiling:
	ReloadAssembly (3358ms)
		BeginReloadAssembly (194ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (85ms)
		EndReloadAssembly (3030ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (629ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (125ms)
			SetupLoadedEditorAssemblies (2021ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1623ms)
				ProcessInitializeOnLoadMethodAttributes (274ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.59 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9746 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10792.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 38.963900 ms (FindLiveObjects: 4.367300 ms CreateObjectMapping: 1.560800 ms MarkObjects: 32.352600 ms  DeleteObjects: 0.681700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0