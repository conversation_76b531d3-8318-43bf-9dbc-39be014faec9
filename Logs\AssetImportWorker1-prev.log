Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker1.log
-srvPort
2710
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16312] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2429778545 [EditorId] 2429778545 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [16312] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2429778545 [EditorId] 2429778545 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 999.68 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56936
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002027 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 928.22 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.529 seconds
Domain Reload Profiling:
	ReloadAssembly (1530ms)
		BeginReloadAssembly (74ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1350ms)
			LoadAssemblies (74ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (116ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (1131ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (928ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (142ms)
				ProcessInitializeOnLoadMethodAttributes (50ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.020007 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 952.80 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.721 seconds
Domain Reload Profiling:
	ReloadAssembly (3723ms)
		BeginReloadAssembly (120ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (3493ms)
			LoadAssemblies (135ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (450ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (148ms)
			SetupLoadedEditorAssemblies (2702ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (953ms)
				BeforeProcessingInitializeOnLoad (136ms)
				ProcessInitializeOnLoadAttributes (1221ms)
				ProcessInitializeOnLoadMethodAttributes (379ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 12.68 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10226 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 20.940300 ms (FindLiveObjects: 0.916500 ms CreateObjectMapping: 0.981800 ms MarkObjects: 18.374300 ms  DeleteObjects: 0.666300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 557.454199 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_S.tga
  artifactKey: Guid(6adf7a753f6278641815ff8b491ac5e2) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_S.tga using Guid(6adf7a753f6278641815ff8b491ac5e2) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9de72724018657e6598b0c8c9c6545c8') in 0.244281 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemCrystal@Elite_Field_03_E.tga
  artifactKey: Guid(1a4c62898fe0adc4b9aee49c977572d9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemCrystal@Elite_Field_03_E.tga using Guid(1a4c62898fe0adc4b9aee49c977572d9) Importer(-1,00000000000000000000000000000000) crunched in 0.045058
 -> (artifact id: 'b7c3dcdd46930c9ad2e9b82919b49e9d') in 0.153544 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_S.tga
  artifactKey: Guid(2ef35d35acd09264d9dd07775f3f3cdc) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_S.tga using Guid(2ef35d35acd09264d9dd07775f3f3cdc) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '33d62f455326a9816941a5fc958abbfa') in 0.230945 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_S.tga
  artifactKey: Guid(62489c3e8dd8af240831aafe1c0b3466) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_S.tga using Guid(62489c3e8dd8af240831aafe1c0b3466) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'de5b8938e2c929fd8f9ec994ba2345de') in 0.129684 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_list_item_b.png
  artifactKey: Guid(62bec50727a66f54aa608bebd436fa6c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_list_item_b.png using Guid(62bec50727a66f54aa608bebd436fa6c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '33064479a0e9c6d933c11768a32b1c43') in 0.016990 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Scene/bake/bake_huoshancangku01/Lightmap-0_comp_light.exr
  artifactKey: Guid(36b6a13079813524ea964e2651bec50b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/Scene/bake/bake_huoshancangku01/Lightmap-0_comp_light.exr using Guid(36b6a13079813524ea964e2651bec50b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '5900c22863c04c6d28f8585e2ea12e8e') in 0.451413 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_S.tga
  artifactKey: Guid(1647328899e18b64b8d1ed9aa7221bed) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_S.tga using Guid(1647328899e18b64b8d1ed9aa7221bed) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b52b2747f7cabdc31c8409a89e87da18') in 0.118573 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_S.tga
  artifactKey: Guid(fbbc4091606efbb41a106eb93a60d82f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_S.tga using Guid(fbbc4091606efbb41a106eb93a60d82f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '484238284415f7b847c89e738196e2db') in 0.174881 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_N.tga
  artifactKey: Guid(c0a220700e160ba47a14b8579a9b3a88) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_N.tga using Guid(c0a220700e160ba47a14b8579a9b3a88) Importer(-1,00000000000000000000000000000000) crunched in 3.188735
 -> (artifact id: '9b4d43fe20531f0f2e91bb066a8d07ea') in 3.310882 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_tip_b.png
  artifactKey: Guid(cd869b9177860d647a83f3f82b8def7b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_tip_b.png using Guid(cd869b9177860d647a83f3f82b8def7b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ce7a205ee1d1ef2672436ab91a4e2ace') in 0.039101 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.007757 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz6.png
  artifactKey: Guid(2a688a36f268be34ab153628c65309b7) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz6.png using Guid(2a688a36f268be34ab153628c65309b7) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'e115f11528a9ee80d320f50fed23fe04') in 0.012251 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_N.tga
  artifactKey: Guid(3cbc2832820d6fc40a1fa25f9744fc7f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_N.tga using Guid(3cbc2832820d6fc40a1fa25f9744fc7f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '53a610ecc14fedea2c40e8af52874ed6') in 0.143195 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.004625 seconds.
  path: Assets/RawData/object/textures/bake_pubudong01_02_d.png
  artifactKey: Guid(b5178c05b5014a540a266d03d6d342a0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_pubudong01_02_d.png using Guid(b5178c05b5014a540a266d03d6d342a0) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'd21762854010e53cca01ba77725b8997') in 0.257711 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_bg.png
  artifactKey: Guid(7555bf111d51dbd41b8fb15add29bb7d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_bg.png using Guid(7555bf111d51dbd41b8fb15add29bb7d) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'cf6e9163ffc40268faf0edfecd5e58a1') in 0.094679 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_N.tga
  artifactKey: Guid(f2ac873f008a24c4da64a3b936e9df1b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_N.tga using Guid(f2ac873f008a24c4da64a3b936e9df1b) Importer(-1,00000000000000000000000000000000) crunched in 0.567502
 -> (artifact id: '2d14ce912ec074126af9ccc4de0bc852') in 0.696574 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png
  artifactKey: Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png using Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3369f1c7fc377d58be67ad45f2f42db8') in 0.044229 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_S.tga
  artifactKey: Guid(d329c9682abef7b41b2ce08097bce2b8) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_S.tga using Guid(d329c9682abef7b41b2ce08097bce2b8) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ded47aa0bc8e89735bd673776ebb0ad0') in 0.200512 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz5.png
  artifactKey: Guid(e4448b1f7f7ca6743bd8d1488c2dbaaa) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz5.png using Guid(e4448b1f7f7ca6743bd8d1488c2dbaaa) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7da52fae477a360c704cf0b2e6983bc4') in 0.017188 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz3.png
  artifactKey: Guid(d63f0ab3d09f5074c93bcd987334a52f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz3.png using Guid(d63f0ab3d09f5074c93bcd987334a52f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'c56c31e3852794dcf632405d1261a3c8') in 0.014914 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_D.tga
  artifactKey: Guid(668f733c46a7e2045aa9495fe07dd07c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_D.tga using Guid(668f733c46a7e2045aa9495fe07dd07c) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0d1a0f9dae8bad888bf6256b7caaf7f6') in 0.134051 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Recharge/UI/title.png
  artifactKey: Guid(3879bb71a4eb6874a8a7ecb9f0104614) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Recharge/UI/title.png using Guid(3879bb71a4eb6874a8a7ecb9f0104614) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7c3432cf0a02fe5992b8ee51305da2f1') in 0.012522 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_E.tga
  artifactKey: Guid(8c9a300eabf121748a436d2ac765d6de) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_E.tga using Guid(8c9a300eabf121748a436d2ac765d6de) Importer(-1,00000000000000000000000000000000) crunched in 0.625619
 -> (artifact id: '8a5ee8dd7ef203262ae7d658f4d79699') in 0.825728 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_N.tga
  artifactKey: Guid(607fc11f58f49284c8f6e8be68724321) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSand@Elite_Field_03_N.tga using Guid(607fc11f58f49284c8f6e8be68724321) Importer(-1,00000000000000000000000000000000) crunched in 1.006768
 -> (artifact id: 'b46c1f2923b6b86e56e9db6856ee96cb') in 1.170037 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawData/object/textures/bake_rongyandong01_01_d.png
  artifactKey: Guid(a64c8fc366d01944c901dda03d85b806) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_rongyandong01_01_d.png using Guid(a64c8fc366d01944c901dda03d85b806) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '69e3668dadb7fffbfff95ad91e2d15eb') in 1.329195 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_N.tga
  artifactKey: Guid(3d791cc68c3f4494bbd0229ed18dbeca) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_N.tga using Guid(3d791cc68c3f4494bbd0229ed18dbeca) Importer(-1,00000000000000000000000000000000) crunched in 0.470478
 -> (artifact id: '1ad2f4009f8d34a0fbd887e4b3d97737') in 0.807419 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_N.tga
  artifactKey: Guid(650e72e38fe4ac344aea43f0781a0d34) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_N.tga using Guid(650e72e38fe4ac344aea43f0781a0d34) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f6641df6f6768b83a1f3688a95f2984a') in 0.287411 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_list_item_select.png
  artifactKey: Guid(c51403a75a09a2e4a9ff1c9c234b8e3f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_list_item_select.png using Guid(c51403a75a09a2e4a9ff1c9c234b8e3f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3d9c5d49920bc99dea97e2a4096a3ecf') in 0.007749 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_AO.tga
  artifactKey: Guid(63746a03edd6a2641a0884cf715ac563) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_AO.tga using Guid(63746a03edd6a2641a0884cf715ac563) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'e90bad6c28dbb9823a796d21593a9586') in 0.068830 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_N.tga
  artifactKey: Guid(084da8f872486694c93febad21209565) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_N.tga using Guid(084da8f872486694c93febad21209565) Importer(-1,00000000000000000000000000000000) crunched in 1.126137
 -> (artifact id: '3214f169211f2c6c241631738c072ff3') in 1.233010 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_S.tga
  artifactKey: Guid(c3858b60539a0134cb24659550bebead) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_S.tga using Guid(c3858b60539a0134cb24659550bebead) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '69c406601610306b29c671730dce3dce') in 0.140169 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_D.tga
  artifactKey: Guid(dff32eb3122b92a42b5b527870dd22b4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_D.tga using Guid(dff32eb3122b92a42b5b527870dd22b4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '1f8efec45ec11f11db4b2014c2755624') in 0.467589 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_lock.png
  artifactKey: Guid(dfa09368772c7044ead85873156b859f) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_lock.png using Guid(dfa09368772c7044ead85873156b859f) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '66a8b6d0d6bb587fc84ab794218741f4') in 0.051682 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_E.tga
  artifactKey: Guid(68c2f8c726488be4d8f65c3195d7ac0d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_E.tga using Guid(68c2f8c726488be4d8f65c3195d7ac0d) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9eca8ecdfa8632e29cc72ce07c67050c') in 0.185995 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/textures/c_prop_rongdongmen01d.png
  artifactKey: Guid(2f73c924c1308dc4db2551ca63a0d9a2) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/c_prop_rongdongmen01d.png using Guid(2f73c924c1308dc4db2551ca63a0d9a2) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '65cb0cbd44eb819d9c0110721532e324') in 0.288025 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/remove_icon.png
  artifactKey: Guid(86ab0754564efe14ba41b00921ee131e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/remove_icon.png using Guid(86ab0754564efe14ba41b00921ee131e) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '2ca74f43c0e8d14dfcca3e76abfcf30e') in 0.016309 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/RawData/object/textures/o_build_huoshancangku01_06_d.png
  artifactKey: Guid(6dd93ef148be9c746a19ba028fab5b94) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/o_build_huoshancangku01_06_d.png using Guid(6dd93ef148be9c746a19ba028fab5b94) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b9a814559fac5d70897100d69ade4775') in 0.135085 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golem@Elite_Field_03_AO.tga
  artifactKey: Guid(be0e563f0e0904c4daa7ead77af65c1d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golem@Elite_Field_03_AO.tga using Guid(be0e563f0e0904c4daa7ead77af65c1d) Importer(-1,00000000000000000000000000000000) crunched in 0.062513
 -> (artifact id: 'a13eb8ab409040ff4f9e257cf8d18cb5') in 0.208689 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz1.png
  artifactKey: Guid(4736984c07a7ed94092a1d3d4fd61a9b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz1.png using Guid(4736984c07a7ed94092a1d3d4fd61a9b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ab8d73b6fbb3c2222514b941ebe08fa5') in 0.022445 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Scene/bake/bake_fangkongdong01/Lightmap-0_comp_light.exr
  artifactKey: Guid(f1b964c1a86dab043813eb7f4fddc6a4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/Scene/bake/bake_fangkongdong01/Lightmap-0_comp_light.exr using Guid(f1b964c1a86dab043813eb7f4fddc6a4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0703e9e22376a5f4148b30afb87bb8c2') in 0.486330 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(ebaabb5ab330f6141a58885ca0abd9ec) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(ebaabb5ab330f6141a58885ca0abd9ec) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 3.142598%;
File 'Anim_Warrior@kick' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Warrior@kick' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '5dd8467218a2fef898980fccd3b92d9b') in 0.385259 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Warrior@kick' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Warrior@kick' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(269788c11b8e60747b33cf2ec1e4f82e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(269788c11b8e60747b33cf2ec1e4f82e) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 4.577105%;
File 'Anim_Regular@decline' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@decline' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '86e2ac97d371bf017f6502890039a39d') in 0.213734 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@decline' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@decline' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@walk_inplace.FBX
  artifactKey: Guid(dd5ceb6c54fd78148a72f92cb5f9836a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@walk_inplace.FBX using Guid(dd5ceb6c54fd78148a72f92cb5f9836a) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.354260%;
File 'Anim_Giant@walk_inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@walk_inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '63eaec51cdb2d6b52a38946d86e5a1ac') in 0.203331 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@walk_inplace.FBX' had errors: 
 File 'Anim_Giant@walk_inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@walk_inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(300460669fc017b4582227b8c6cc4483) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(300460669fc017b4582227b8c6cc4483) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 10.637096%;
File 'Anim_Giant@idle' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@idle' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '03372504c15c860252170df9bae4830d') in 0.149720 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@idle' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@idle' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(1daaf3ba64b60314bbefb65df438121c) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(1daaf3ba64b60314bbefb65df438121c) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.844508%;
File 'Anim_Giant@run' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@run' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'f559ee5cec3cbcc0738ec6be71ec708d') in 0.131497 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@run' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@run' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(c2fb84a376f21f849b2f8ad749ee80e5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(c2fb84a376f21f849b2f8ad749ee80e5) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.354118%;
File 'Anim_Giant@attack1' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@attack1' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '4141177120a552958463c33d6f0a7a78') in 0.150210 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@attack1' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@attack1' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(c00750a0f7451f94d8c6b000060ee712) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(c00750a0f7451f94d8c6b000060ee712) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.313377%;
File 'Anim_Regular@jump-high' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@jump-high' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '0f7f4641ee5a0c6dd56389b3ffbb0a7c') in 0.312982 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@jump-high' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@jump-high' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(7c747ba0d9121a7479dad848c1778b85) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(7c747ba0d9121a7479dad848c1778b85) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 2.881432%;
File 'Anim_Regular@cast1' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@cast1' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '189107008f50fa1941252da7ffa5e2e8') in 0.374171 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@cast1' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@cast1' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(f1f284da3e4c41144974a74d1d24d41b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(f1f284da3e4c41144974a74d1d24d41b) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 29.020586%;
File 'Anim_Giant@attack2' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@attack2' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '5e62b4033e32320946d314764ed7f0fa') in 0.176347 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@attack2' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@attack2' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.151619 seconds.
  path: Assets/RawData/object/models/outdoors/o_build_huoshancangku02_box.FBX
  artifactKey: Guid(********************************) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/o_build_huoshancangku02_box.FBX using Guid(********************************) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
 -> (artifact id: '905df710c7cdf13d0316adb60e5dbff1') in 0.184163 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/outdoors/o_build_huoshancangku01_rukou.fbx
  artifactKey: Guid(0e45239eb3aaf9049a664fed0ef584a3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/o_build_huoshancangku01_rukou.fbx using Guid(0e45239eb3aaf9049a664fed0ef584a3) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9ae1ffe66b3cdf6a962c30d06bb8a11b') in 0.042034 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawData/object/models/outdoors/bake_rongyandong01.FBX
  artifactKey: Guid(f25602919f6a8d74699c8816f7267d5e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_rongyandong01.FBX using Guid(f25602919f6a8d74699c8816f7267d5e) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.08 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.08 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.07 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
 -> (artifact id: '391981f0606167740d8ae5c9af1e6cf5') in 1.386750 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016277 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 827.16 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.960 seconds
Domain Reload Profiling:
	ReloadAssembly (3961ms)
		BeginReloadAssembly (436ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (21ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (255ms)
		EndReloadAssembly (3418ms)
			LoadAssemblies (132ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (518ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (2573ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (827ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1386ms)
				ProcessInitializeOnLoadMethodAttributes (248ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (22ms)
Platform modules already initialized, skipping
Curl error 35: Cert handshake failed. verify result: UNITYTLS_X509VERIFY_FATAL_ERROR. error state: 7
Refreshing native plugins compatible for Editor in 27.54 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10779.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 21.302700 ms (FindLiveObjects: 0.940800 ms CreateObjectMapping: 0.917000 ms MarkObjects: 18.904100 ms  DeleteObjects: 0.539600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5162.853163 seconds.
  path: Assets/Enviro - Dynamic Enviroment/Weather/Materials/DisplayHeight.mat
  artifactKey: Guid(525717ed74c4cd742a2777393f55afeb) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Enviro - Dynamic Enviroment/Weather/Materials/DisplayHeight.mat using Guid(525717ed74c4cd742a2777393f55afeb) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a86e81e7a09a9cfc09b2f77d9c886e37') in 2.180648 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Standard Assets/Effects/TessellationShaders/Materials/TesselatedBumpSpecularDisplacement.mat
  artifactKey: Guid(26d8bdbc8646bde48b05fbaacaaa6577) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Standard Assets/Effects/TessellationShaders/Materials/TesselatedBumpSpecularDisplacement.mat using Guid(26d8bdbc8646bde48b05fbaacaaa6577) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '014b6b027aaea2dd4bdada2ec2f3bfee') in 0.028705 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay/DisplayRawImage/DisplayRawImage.prefab
  artifactKey: Guid(69908354653fb9a4a89ef2dd227c5b69) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay/DisplayRawImage/DisplayRawImage.prefab using Guid(69908354653fb9a4a89ef2dd227c5b69) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a1c7d0f132ab2abc084d502c38121096') in 0.026927 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay/PlayerDisplay.prefab
  artifactKey: Guid(041f5cfd4b9ecce4aaf5c4ccfd234c97) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay/PlayerDisplay.prefab using Guid(041f5cfd4b9ecce4aaf5c4ccfd234c97) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '26112d2fde8e11697a0d4a468583f633') in 0.010369 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015932 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 14.21 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.000 seconds
Domain Reload Profiling:
	ReloadAssembly (3001ms)
		BeginReloadAssembly (269ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (14ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (147ms)
		EndReloadAssembly (2618ms)
			LoadAssemblies (142ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (549ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (115ms)
			SetupLoadedEditorAssemblies (1698ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (14ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1331ms)
				ProcessInitializeOnLoadMethodAttributes (240ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (29ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10884.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.809500 ms (FindLiveObjects: 0.804200 ms CreateObjectMapping: 0.787100 ms MarkObjects: 19.682600 ms  DeleteObjects: 0.534500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018459 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.20 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.713 seconds
Domain Reload Profiling:
	ReloadAssembly (2713ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (2453ms)
			LoadAssemblies (110ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (482ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1652ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1316ms)
				ProcessInitializeOnLoadMethodAttributes (221ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10901.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 20.026600 ms (FindLiveObjects: 0.832800 ms CreateObjectMapping: 0.815900 ms MarkObjects: 17.832800 ms  DeleteObjects: 0.543700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016365 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.75 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.749 seconds
Domain Reload Profiling:
	ReloadAssembly (2750ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2478ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (505ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1644ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1289ms)
				ProcessInitializeOnLoadMethodAttributes (233ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.86 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10918.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 22.378000 ms (FindLiveObjects: 1.107900 ms CreateObjectMapping: 1.202200 ms MarkObjects: 19.411900 ms  DeleteObjects: 0.654500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020521 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.663 seconds
Domain Reload Profiling:
	ReloadAssembly (2663ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2398ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (501ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (108ms)
			SetupLoadedEditorAssemblies (1542ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1187ms)
				ProcessInitializeOnLoadMethodAttributes (230ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 22.72 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10935.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 22.936500 ms (FindLiveObjects: 0.891200 ms CreateObjectMapping: 0.970000 ms MarkObjects: 20.529300 ms  DeleteObjects: 0.544400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017969 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.64 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.829 seconds
Domain Reload Profiling:
	ReloadAssembly (2830ms)
		BeginReloadAssembly (223ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (132ms)
		EndReloadAssembly (2495ms)
			LoadAssemblies (132ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (509ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1639ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1303ms)
				ProcessInitializeOnLoadMethodAttributes (218ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.91 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10952.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.007500 ms (FindLiveObjects: 0.927600 ms CreateObjectMapping: 1.156000 ms MarkObjects: 18.383000 ms  DeleteObjects: 0.539200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015966 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.36 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.790 seconds
Domain Reload Profiling:
	ReloadAssembly (2790ms)
		BeginReloadAssembly (151ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (2539ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (501ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (111ms)
			SetupLoadedEditorAssemblies (1694ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (102ms)
				ProcessInitializeOnLoadAttributes (1326ms)
				ProcessInitializeOnLoadMethodAttributes (244ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.02 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10969.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 25.000500 ms (FindLiveObjects: 0.870400 ms CreateObjectMapping: 0.913100 ms MarkObjects: 22.640900 ms  DeleteObjects: 0.574700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1236.463971 seconds.
  path: Assets/Scene/bake/bake_pubudong01/Lightmap-0_comp_light.exr
  artifactKey: Guid(ddf6cff23898941429697dfedabf0770) Importer(-1,00000000000000000000000000000000)
Start importing Assets/Scene/bake/bake_pubudong01/Lightmap-0_comp_light.exr using Guid(ddf6cff23898941429697dfedabf0770) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'cda86f6935bb175d71d0259c62f1d01c') in 0.416153 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.002107 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01_box.FBX
  artifactKey: Guid(********************************) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01_box.FBX using Guid(********************************) Importer(-1,00000000000000000000000000000000) [16:05:45.8691]:OnPostprocessModel=bake_pubudong01_box
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: 'f8010095f7a00358b0267c13b03a4c57') in 0.141974 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01.FBX
  artifactKey: Guid(********************************) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01.FBX using Guid(********************************) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.04 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.04 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.04 seconds
[16:05:47.1053]:OnPostprocessModel=bake_pubudong01
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '7901e9241451d83dd6a9cd0173df7fff') in 1.203780 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018151 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.45 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.252 seconds
Domain Reload Profiling:
	ReloadAssembly (3253ms)
		BeginReloadAssembly (194ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (88ms)
		EndReloadAssembly (2946ms)
			LoadAssemblies (132ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (471ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (2143ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (13ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1779ms)
				ProcessInitializeOnLoadMethodAttributes (238ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10987.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 20.207700 ms (FindLiveObjects: 0.829100 ms CreateObjectMapping: 0.825600 ms MarkObjects: 17.988500 ms  DeleteObjects: 0.563400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018387 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.79 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.764 seconds
Domain Reload Profiling:
	ReloadAssembly (2765ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2495ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (483ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1681ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1331ms)
				ProcessInitializeOnLoadMethodAttributes (238ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (34ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.91 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11004.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.525600 ms (FindLiveObjects: 0.869500 ms CreateObjectMapping: 0.896000 ms MarkObjects: 19.200400 ms  DeleteObjects: 0.558900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016413 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.18 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.693 seconds
Domain Reload Profiling:
	ReloadAssembly (2694ms)
		BeginReloadAssembly (176ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (74ms)
		EndReloadAssembly (2416ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (466ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1624ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1281ms)
				ProcessInitializeOnLoadMethodAttributes (216ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.02 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11021.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 20.518800 ms (FindLiveObjects: 0.884400 ms CreateObjectMapping: 0.888700 ms MarkObjects: 18.203400 ms  DeleteObjects: 0.541100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1435.641006 seconds.
  path: Assets/RawData/object/models/Volumes/SM_UnitSphere.fbx
  artifactKey: Guid(e5473be91bc954626932e8f4f172cb1b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/Volumes/SM_UnitSphere.fbx using Guid(e5473be91bc954626932e8f4f172cb1b) Importer(-1,00000000000000000000000000000000) [16:29:42.8875]:OnPostprocessModel=SM_UnitSphere
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
SKY.Util:UnityLog (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:89)
SKY.LoggerUnity:Log (SKY.Util/LogType,string,object[]) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:17)
SKY.Util:Log (string) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:201)
Debug:Log (object) (at Assets/HotFixSystem/Runtime/Utility/SKYLog.cs:270)
ModelImport:OptimizeModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:20)
ModelImport:OnPostprocessModel (UnityEngine.GameObject) (at Assets/Script/Editor/ModelImport.cs:14)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.AssetPostprocessingInternal:InvokeMethodIfAvailable (object,string,object[])
UnityEditor.AssetPostprocessingInternal:CallPostProcessMethods (string,object[])
UnityEditor.AssetPostprocessingInternal:PostprocessMesh (UnityEngine.GameObject)

(Filename: Assets/HotFixSystem/Runtime/Utility/SKYLog.cs Line: 89)

 -> (artifact id: '6a0e12e43583760daa32dee3468412a4') in 0.115897 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016549 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.61 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.865 seconds
Domain Reload Profiling:
	ReloadAssembly (2865ms)
		BeginReloadAssembly (180ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (79ms)
		EndReloadAssembly (2583ms)
			LoadAssemblies (121ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (478ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (103ms)
			SetupLoadedEditorAssemblies (1751ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1401ms)
				ProcessInitializeOnLoadMethodAttributes (231ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (34ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 28.01 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9750 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.1 MB). Loaded Objects now: 11039.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.886400 ms (FindLiveObjects: 0.926000 ms CreateObjectMapping: 1.132800 ms MarkObjects: 19.228800 ms  DeleteObjects: 0.597500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 719.709623 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Building/xiewudingban01_Wood.png
  artifactKey: Guid(6ab7c000d32718746b50a077f08bda75) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Building/xiewudingban01_Wood.png using Guid(6ab7c000d32718746b50a077f08bda75) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '328731b41c2a4c840464f633dcb17c66') in 4.067261 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow10.png
  artifactKey: Guid(ff0fd1e1d5cec7c45a72e2a512c7ed64) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow10.png using Guid(ff0fd1e1d5cec7c45a72e2a512c7ed64) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4558d74bf5c0ae0553723d0d06fce10') in 0.023914 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_icon_01.png
  artifactKey: Guid(740a16c3e7e69944bb9a1a3dc5409d69) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_icon_01.png using Guid(740a16c3e7e69944bb9a1a3dc5409d69) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '871d8705cb72a336f6289e13143b6ef2') in 0.018917 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_01.png
  artifactKey: Guid(90013a0496528f44da405796299c7119) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/zrw_bg_01.png using Guid(90013a0496528f44da405796299c7119) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a58a00054a002c6bf724e7ebe61bf308') in 0.054829 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow0.png
  artifactKey: Guid(06eb6a37a4037994fa31b22eb36f77cc) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow0.png using Guid(06eb6a37a4037994fa31b22eb36f77cc) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5fecd099d9002bbdbcfe58e548f44bc6') in 0.023415 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow3.png
  artifactKey: Guid(86443404627d0214b99014379e96ce4d) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellow3.png using Guid(86443404627d0214b99014379e96ce4d) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc837686f21465977f1ac70aa9d96a65') in 0.024904 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AssetBundle/UI2/Forms/Guild/GuildUI/yellowlight.png
  artifactKey: Guid(cc3a87c966afd974e879c6bfa7b99a2a) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Guild/GuildUI/yellowlight.png using Guid(cc3a87c966afd974e879c6bfa7b99a2a) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '994fa2e1c450f2c9c55351e44ccf9b1c') in 0.024510 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellowtitle.png
  artifactKey: Guid(1be94f20f970124418d5d81c76df81af) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Passport/UITexture/new2/yellowtitle.png using Guid(1be94f20f970124418d5d81c76df81af) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bd21d7ca1bd8eee18a6b6f19e6a78fcd') in 0.019749 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Fight/UITexture/yellowbg.png
  artifactKey: Guid(70764de993b707249aa25475775829f1) Importer(*********,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Fight/UITexture/yellowbg.png using Guid(70764de993b707249aa25475775829f1) Importer(*********,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '34ad108c4a1b2d002b625661c884f963') in 0.022252 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0