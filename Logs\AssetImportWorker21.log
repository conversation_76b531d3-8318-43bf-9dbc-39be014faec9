Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker21
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker21.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16676] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2149725927 [EditorId] 2149725927 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [16676] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2149725927 [EditorId] 2149725927 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 962.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56456
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002295 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 905.72 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.473 seconds
Domain Reload Profiling:
	ReloadAssembly (1473ms)
		BeginReloadAssembly (71ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1301ms)
			LoadAssemblies (70ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (110ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (1097ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (906ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (131ms)
				ProcessInitializeOnLoadMethodAttributes (51ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.017502 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 916.35 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.832 seconds
Domain Reload Profiling:
	ReloadAssembly (3833ms)
		BeginReloadAssembly (107ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (3628ms)
			LoadAssemblies (115ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (491ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (196ms)
			SetupLoadedEditorAssemblies (2761ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (916ms)
				BeforeProcessingInitializeOnLoad (133ms)
				ProcessInitializeOnLoadAttributes (1407ms)
				ProcessInitializeOnLoadMethodAttributes (291ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 12.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10224 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 35.549400 ms (FindLiveObjects: 1.205600 ms CreateObjectMapping: 1.727000 ms MarkObjects: 31.803100 ms  DeleteObjects: 0.812300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 470803.513909 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/com_icon_10.png
  artifactKey: Guid(939d1be4ba004534394575a975bcfe1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/com_icon_10.png using Guid(939d1be4ba004534394575a975bcfe1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '10018b45d15821ad539deb2be95503ab') in 0.148553 seconds 
========================================================================
Received Import Request.
  Time since last request: 19.360279 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/转换btn.png
  artifactKey: Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/转换btn.png using Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b8f8aec5d053fef2942aac15b5ab82f9') in 0.102799 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.368221 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/转换btn.png
  artifactKey: Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/转换btn.png using Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ca255f6d2c50664c8fc3913bcfd27d9a') in 0.015742 seconds 
========================================================================
Received Import Request.
  Time since last request: 11.445342 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png
  artifactKey: Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png using Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '899a06d74c1d69c2d3f47529f5ac1a26') in 0.004562 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.322735 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png
  artifactKey: Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png using Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '65edaae9af2e135a9c46b0036d0d0a7b') in 0.014118 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.023612 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.59 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.991 seconds
Domain Reload Profiling:
	ReloadAssembly (2992ms)
		BeginReloadAssembly (161ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2736ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (514ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (118ms)
			SetupLoadedEditorAssemblies (1873ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (105ms)
				ProcessInitializeOnLoadAttributes (1499ms)
				ProcessInitializeOnLoadMethodAttributes (244ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (22ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 38.07 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10783.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 29.473200 ms (FindLiveObjects: 1.453600 ms CreateObjectMapping: 1.389200 ms MarkObjects: 25.895800 ms  DeleteObjects: 0.732700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 2157.332905 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_protect.png
  artifactKey: Guid(046d24636d6c4214bb1fe40e4b2e9075) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_protect.png using Guid(046d24636d6c4214bb1fe40e4b2e9075) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '342d0b308e8753e473860e2d0833c2bd') in 0.108229 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_clothes.png
  artifactKey: Guid(583b0e9244c34a74388930ba6874522a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_clothes.png using Guid(583b0e9244c34a74388930ba6874522a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ad5a1cb56d6f2fa613c927ca79838eb') in 0.016563 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_leftup.png
  artifactKey: Guid(fb3c260234169c040bf55c35069544ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_leftup.png using Guid(fb3c260234169c040bf55c35069544ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1afddadef9f4dc016baa613630bd39fa') in 0.020841 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_lighting.png
  artifactKey: Guid(d2c1d9028493be944a5ed3aa9e138911) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_lighting.png using Guid(d2c1d9028493be944a5ed3aa9e138911) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a09099aef8b0971d6c79a49e8e569202') in 0.027019 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_bind.png
  artifactKey: Guid(48ec6ee564e0d4a4cab0acf15ec2fdb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_bind.png using Guid(48ec6ee564e0d4a4cab0acf15ec2fdb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '441a8d24f65d2de9543854a73b515abe') in 0.018313 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png
  artifactKey: Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_change.png using Guid(88ebbf02c30b89c4f89afabe539cbe6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8bab2ae1d5f233359dff5cbeb802bf5c') in 0.019176 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_key.png
  artifactKey: Guid(8ce56acaa51e391469457fafde1c797b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_key.png using Guid(8ce56acaa51e391469457fafde1c797b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa1dacf7ee02e0efaa5f60290a91167a') in 0.016145 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_cookpot.png
  artifactKey: Guid(7e0dcfde2d3b4fe488cf20500c3da265) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_cookpot.png using Guid(7e0dcfde2d3b4fe488cf20500c3da265) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '98658b7c0d41235da525a86a09662bc9') in 0.025240 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_leftdown.png
  artifactKey: Guid(2be39c66ce146db4a9dd5cc62093890f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_leftdown.png using Guid(2be39c66ce146db4a9dd5cc62093890f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e4ca4bf5486fd99c14bdb6153146096') in 0.015275 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_frame.png
  artifactKey: Guid(1ed8d70cab2e057488e0bc98f0fd2456) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_frame.png using Guid(1ed8d70cab2e057488e0bc98f0fd2456) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6fe3801d4dd8bfd2caed90dd2ceade30') in 0.021239 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_arms.png
  artifactKey: Guid(faa7c2ce3742bd64caafe53dcb031c74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/itemicon_arms.png using Guid(faa7c2ce3742bd64caafe53dcb031c74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '68172101bc7af0bb437cc33ebf44668f') in 0.015731 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/com_icon_10.png
  artifactKey: Guid(939d1be4ba004534394575a975bcfe1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconTip/com_icon_10.png using Guid(939d1be4ba004534394575a975bcfe1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e201f90d79b722c2942ed11b795aa40a') in 0.019885 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021320 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 11.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.403 seconds
Domain Reload Profiling:
	ReloadAssembly (3404ms)
		BeginReloadAssembly (206ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (95ms)
		EndReloadAssembly (3065ms)
			LoadAssemblies (144ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (609ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (129ms)
			SetupLoadedEditorAssemblies (2063ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (111ms)
				ProcessInitializeOnLoadAttributes (1626ms)
				ProcessInitializeOnLoadMethodAttributes (296ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 59.96 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10818.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 34.608300 ms (FindLiveObjects: 1.279400 ms CreateObjectMapping: 1.281200 ms MarkObjects: 31.239400 ms  DeleteObjects: 0.806600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016119 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.73 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.856 seconds
Domain Reload Profiling:
	ReloadAssembly (2856ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2595ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (498ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1771ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1408ms)
				ProcessInitializeOnLoadMethodAttributes (245ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.77 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10835.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 40.965200 ms (FindLiveObjects: 1.796900 ms CreateObjectMapping: 1.728300 ms MarkObjects: 36.624400 ms  DeleteObjects: 0.813500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016604 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.640 seconds
Domain Reload Profiling:
	ReloadAssembly (2641ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2368ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (480ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (110ms)
			SetupLoadedEditorAssemblies (1565ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1219ms)
				ProcessInitializeOnLoadMethodAttributes (229ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 35.47 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10852.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 38.059400 ms (FindLiveObjects: 1.094400 ms CreateObjectMapping: 1.060700 ms MarkObjects: 35.118900 ms  DeleteObjects: 0.784300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 57.58 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10852.
Memory consumption went from 299.9 MB to 297.9 MB.
Total: 38.544300 ms (FindLiveObjects: 1.398100 ms CreateObjectMapping: 1.526600 ms MarkObjects: 34.437200 ms  DeleteObjects: 1.181000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019181 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.724 seconds
Domain Reload Profiling:
	ReloadAssembly (3729ms)
		BeginReloadAssembly (401ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (23ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (1ms)
			CreateAndSetChildDomain (216ms)
		EndReloadAssembly (3196ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (601ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (115ms)
			SetupLoadedEditorAssemblies (2203ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (103ms)
				ProcessInitializeOnLoadAttributes (1729ms)
				ProcessInitializeOnLoadMethodAttributes (342ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (35ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 31.73 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10869.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 28.336600 ms (FindLiveObjects: 1.272900 ms CreateObjectMapping: 1.157400 ms MarkObjects: 25.244300 ms  DeleteObjects: 0.660400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018269 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.41 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.845 seconds
Domain Reload Profiling:
	ReloadAssembly (2845ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2562ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (504ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (112ms)
			SetupLoadedEditorAssemblies (1716ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1353ms)
				ProcessInitializeOnLoadMethodAttributes (242ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 34.67 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10886.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 38.682500 ms (FindLiveObjects: 2.080700 ms CreateObjectMapping: 1.501300 ms MarkObjects: 34.036900 ms  DeleteObjects: 1.061700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 58.42 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10886.
Memory consumption went from 300.0 MB to 298.0 MB.
Total: 34.173400 ms (FindLiveObjects: 1.189300 ms CreateObjectMapping: 1.532900 ms MarkObjects: 30.596800 ms  DeleteObjects: 0.853300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015951 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.88 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.708 seconds
Domain Reload Profiling:
	ReloadAssembly (2709ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2442ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (507ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1623ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1276ms)
				ProcessInitializeOnLoadMethodAttributes (227ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.90 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10903.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 39.397300 ms (FindLiveObjects: 1.672800 ms CreateObjectMapping: 1.831900 ms MarkObjects: 35.100800 ms  DeleteObjects: 0.790600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 55.04 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10903.
Memory consumption went from 300.0 MB to 298.0 MB.
Total: 34.640200 ms (FindLiveObjects: 1.145800 ms CreateObjectMapping: 1.007700 ms MarkObjects: 31.489100 ms  DeleteObjects: 0.995700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.023014 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.52 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.256 seconds
Domain Reload Profiling:
	ReloadAssembly (3257ms)
		BeginReloadAssembly (223ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (95ms)
		EndReloadAssembly (2921ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (530ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (2005ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1543ms)
				ProcessInitializeOnLoadMethodAttributes (337ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (62ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 35.39 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10920.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 32.433300 ms (FindLiveObjects: 1.377600 ms CreateObjectMapping: 1.516800 ms MarkObjects: 28.663200 ms  DeleteObjects: 0.874100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018061 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.19 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.715 seconds
Domain Reload Profiling:
	ReloadAssembly (2716ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (75ms)
		EndReloadAssembly (2445ms)
			LoadAssemblies (123ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (108ms)
			SetupLoadedEditorAssemblies (1641ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1293ms)
				ProcessInitializeOnLoadMethodAttributes (230ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 35.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10937.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 29.173500 ms (FindLiveObjects: 0.887900 ms CreateObjectMapping: 1.093800 ms MarkObjects: 26.576000 ms  DeleteObjects: 0.614700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 70.57 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10937.
Memory consumption went from 299.6 MB to 297.6 MB.
Total: 40.476500 ms (FindLiveObjects: 1.353000 ms CreateObjectMapping: 1.668100 ms MarkObjects: 36.567500 ms  DeleteObjects: 0.886700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 11848.146577 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_ammo_icon.png
  artifactKey: Guid(bf410f7be1abc4a45b856bb10d48d760) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_ammo_icon.png using Guid(bf410f7be1abc4a45b856bb10d48d760) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c78cf7e9b2127ed0eefbe9c01d2cf8b0') in 1.426280 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.127114 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_attr_bg3.png
  artifactKey: Guid(d0457eb220f2c394dae232b7adfc70d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_attr_bg3.png using Guid(d0457eb220f2c394dae232b7adfc70d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '769c8cfc5b487fd01ee0231db0f27436') in 1.042816 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_icon3.png
  artifactKey: Guid(53bab1d0ec18a1d40bd512f12e431d6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_icon3.png using Guid(53bab1d0ec18a1d40bd512f12e431d6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1222f897db4bd10604c3d66c7b2b3c0f') in 0.015220 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_24.png
  artifactKey: Guid(26b6925681370f248a3bd7120ce2ce1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_24.png using Guid(26b6925681370f248a3bd7120ce2ce1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fca3f753f26aa9ce7c66dfddec2c384f') in 0.022999 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip3.png
  artifactKey: Guid(c5a90a20e70b97842ab18d750210175e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip3.png using Guid(c5a90a20e70b97842ab18d750210175e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '789162d30d05a06138fc71caf155aa11') in 0.033106 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_01.png
  artifactKey: Guid(2dfcfe1bc75687c4d8da79d922f712eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_01.png using Guid(2dfcfe1bc75687c4d8da79d922f712eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '85e17f30db1b9fe17513d7e74aa5c495') in 0.020520 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tran.png
  artifactKey: Guid(2ff6a52ab89f58844beb658058fe8163) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tran.png using Guid(2ff6a52ab89f58844beb658058fe8163) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c8d3e3b09011d2ee7835b20e1426bc79') in 0.021825 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_text_equip.png
  artifactKey: Guid(ca2c8b78f31da9348bcaced92cae8072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_text_equip.png using Guid(ca2c8b78f31da9348bcaced92cae8072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ea9df293208045f581f81f13f4bfdf29') in 0.017762 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_1_disable.png
  artifactKey: Guid(2bb3b2922f0d73b4d8574cae6e020e32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_1_disable.png using Guid(2bb3b2922f0d73b4d8574cae6e020e32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4bba0f1729b334f72ce692a26957fd47') in 0.017074 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_02.png
  artifactKey: Guid(95bfba5527d94214e94939e8c4291390) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_02.png using Guid(95bfba5527d94214e94939e8c4291390) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '93e0c351c0a00f42ce09710a95b7990a') in 0.015430 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_2.png
  artifactKey: Guid(43a222e5bdf6f984584c0178d63e8c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_2.png using Guid(43a222e5bdf6f984584c0178d63e8c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '593b81dbb69a7aa70365a8389a4d6910') in 0.029201 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_01.png
  artifactKey: Guid(66679d2003b47f349973e653f1963c2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_01.png using Guid(66679d2003b47f349973e653f1963c2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1f49a6a875ce4bf40da84d8218a581ac') in 0.025540 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_attr_bg4.png
  artifactKey: Guid(273052ec35d1fae4d8e323a1e7eb8e72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_attr_bg4.png using Guid(273052ec35d1fae4d8e323a1e7eb8e72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7990a19f265792f094b36ffec54c675c') in 0.019522 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_03.png
  artifactKey: Guid(0ae983786a611ef4bb616353408e43d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_03.png using Guid(0ae983786a611ef4bb616353408e43d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2245867d8875bc30e3254d66b0ef5db0') in 0.015364 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_playerprop1.png
  artifactKey: Guid(0cf012285de8c374186aaba0b58c7e04) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_playerprop1.png using Guid(0cf012285de8c374186aaba0b58c7e04) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fcc8bcc75269dc978cd7ecbbfaaddf17') in 0.045610 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_22.png
  artifactKey: Guid(aa84298162d338f4ba1246490edc904c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_22.png using Guid(aa84298162d338f4ba1246490edc904c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c8e556da714d4efecdc13b0485476113') in 0.012015 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_propbg.png
  artifactKey: Guid(e1fc54bc9bbc34c4d9d54858335de219) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_propbg.png using Guid(e1fc54bc9bbc34c4d9d54858335de219) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dffa89b76be2bc45ce53ed664b085a30') in 0.027195 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_progress.png
  artifactKey: Guid(0119377ad93b78443a6c7270755c6792) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_progress.png using Guid(0119377ad93b78443a6c7270755c6792) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f7477f0c701c95d9eb2288422b16dc7f') in 0.018428 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_intercom0.png
  artifactKey: Guid(9368428079f415b4fb7ae8d9713dd9d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_intercom0.png using Guid(9368428079f415b4fb7ae8d9713dd9d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b587e79901c0a10835f12121691f74f') in 0.015564 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_tip_icon.png
  artifactKey: Guid(a36a81708fb3ea445b1a7d4f4664997d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_tip_icon.png using Guid(a36a81708fb3ea445b1a7d4f4664997d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '33cafc6c57f1bd5d0cdeb29c778d4cb9') in 0.015128 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_bg1.png
  artifactKey: Guid(9eb61afce17699d41bebd6c47a10f599) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_bg1.png using Guid(9eb61afce17699d41bebd6c47a10f599) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a684c21934bbe4504c64250e951fff38') in 0.019658 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_chip.png
  artifactKey: Guid(5750dea67fd97e941a73c97bc85aff96) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_chip.png using Guid(5750dea67fd97e941a73c97bc85aff96) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a48c1c8b58a4e8f83554b0ed8f4d5c37') in 0.013617 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/com_btn_18.png
  artifactKey: Guid(45761122daaa28041af3c6671de84476) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/com_btn_18.png using Guid(45761122daaa28041af3c6671de84476) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '31afdc552b251ba19804671ca4e7aee2') in 0.020031 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_weak.png
  artifactKey: Guid(72929239595933b48b9f2db1515dbd5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_weak.png using Guid(72929239595933b48b9f2db1515dbd5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1f3ae834f04fb81ee4df240dfe19d8c9') in 0.024116 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/btn_safebox.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/btn_safebox.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c540a0c3eabdc8a04850efc6650628b0') in 0.016972 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip8.png
  artifactKey: Guid(9a2abaa65d5444241a0e9e49e9571069) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip8.png using Guid(9a2abaa65d5444241a0e9e49e9571069) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e0e7b929cc6e15bfb30ceea814d05c2') in 0.017809 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_trash.png
  artifactKey: Guid(a1cd785eb960ace41bfefbf36f339d12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_trash.png using Guid(a1cd785eb960ace41bfefbf36f339d12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aa4f3ae2401d50f79315337f6f779846') in 0.021077 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_2_disable.png
  artifactKey: Guid(4553365a7bd6ed34893eaf55d8e3bb5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_2_disable.png using Guid(4553365a7bd6ed34893eaf55d8e3bb5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '59e25291531d83d707a19c6475589529') in 0.021263 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/safebox_bg.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/safebox_bg.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '392d43f18d2d625caf66b81890a04df8') in 0.028803 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_02.png
  artifactKey: Guid(76106d49a2c25b74b9bbb62942089a93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_02.png using Guid(76106d49a2c25b74b9bbb62942089a93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7516ccaad4a0d5fb8e8658c4fbe49e7b') in 0.015833 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_question.png
  artifactKey: Guid(f6589238990a1e4408f1e15524f8c873) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_question.png using Guid(f6589238990a1e4408f1e15524f8c873) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2a9a14ad2d71a318a22f3234d5f51b1') in 0.016215 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_line.png
  artifactKey: Guid(5fc1f726347213d44ae874ab0e2795ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_line.png using Guid(5fc1f726347213d44ae874ab0e2795ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dead92c8a46eae8a7a964dd770cf67f5') in 0.021896 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_1.png
  artifactKey: Guid(267df9eba028dda43b0f4f9c5e4f83c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_drop_1.png using Guid(267df9eba028dda43b0f4f9c5e4f83c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '49b1114015dc47db8642a791fc7942c9') in 0.020823 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_bg1.png
  artifactKey: Guid(d702788d4ff287e43ae18056f445e1e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_bg1.png using Guid(d702788d4ff287e43ae18056f445e1e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e35326398b03e90c25b09f474d0021de') in 0.033449 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_drop.png
  artifactKey: Guid(d6b11eb4b0d7e0a42802a03ac06ed72e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_drop.png using Guid(d6b11eb4b0d7e0a42802a03ac06ed72e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '569087af6c6feafde7d32f0215bf3c53') in 0.016967 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip2.png
  artifactKey: Guid(f521397e681f40145a571a9e14f2353d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip2.png using Guid(f521397e681f40145a571a9e14f2353d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0576fdf6d987fddcbe465f5864ac137e') in 0.017746 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_progress.png
  artifactKey: Guid(dcd35f8e8891c82439f12c3a09469c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_progress.png using Guid(dcd35f8e8891c82439f12c3a09469c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b1e7bdfdd786e61e8c86e7be6189004') in 0.016957 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_28.png
  artifactKey: Guid(f97f8237a8cb683428019bf7aae610d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_icon_28.png using Guid(f97f8237a8cb683428019bf7aae610d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '022171dfe9675a294f8206ecabd21e95') in 0.013575 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_question.png
  artifactKey: Guid(8e50383291d231a4e814fdb99c09f4a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_question.png using Guid(8e50383291d231a4e814fdb99c09f4a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d9e42d24355785256aeb58d4de898ec') in 0.011556 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_03.png
  artifactKey: Guid(65b5c74f313917d4d99c8ca669c5b39a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bb_bg_03.png using Guid(65b5c74f313917d4d99c8ca669c5b39a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2e1c3cdcc81de00b53cce942f7f8b054') in 0.028732 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_intercom1.png
  artifactKey: Guid(04607f7138f086d4cb553f369173e0db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_intercom1.png using Guid(04607f7138f086d4cb553f369173e0db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2347c25444343fede09787ab859bd8ea') in 0.012732 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_line1.png
  artifactKey: Guid(6af41e27a96c25142a73daed7bf9469e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_line1.png using Guid(6af41e27a96c25142a73daed7bf9469e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '949db8d9f794d445c2396eb338de9a6f') in 0.014686 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/com_btn_02.png
  artifactKey: Guid(770649d9188d6b44c8d70ca120d6a2d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/com_btn_02.png using Guid(770649d9188d6b44c8d70ca120d6a2d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd5614399d706ab1628246ac7b95b45e') in 0.018255 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_rename.png
  artifactKey: Guid(654e13e067ded5c48bcb8e52b6399867) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/img_ride_rename.png using Guid(654e13e067ded5c48bcb8e52b6399867) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '89eb79da16b013aed471ded6d797c236') in 0.018960 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip4.png
  artifactKey: Guid(bc114df5956139d4da533e1a258da0b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_equip4.png using Guid(bc114df5956139d4da533e1a258da0b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8db439fb283371dce56230f8c5f8a02f') in 0.020575 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_icon0.png
  artifactKey: Guid(41753912b09724c48b125dcc65f0e5e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_icon0.png using Guid(41753912b09724c48b125dcc65f0e5e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '29cab845a5b7575e5155e2888151c1e4') in 0.013073 seconds 
========================================================================
Received Import Request.
  Time since last request: 14.283202 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/套装效果_对号.png
  artifactKey: Guid(ff3ded036c1d5064da726025359cd330) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/UITexture/Common/套装效果_对号.png using Guid(ff3ded036c1d5064da726025359cd330) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b6656106ffa6302ff865ac9ef188e30f') in 0.077280 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019979 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.09 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.197 seconds
Domain Reload Profiling:
	ReloadAssembly (3198ms)
		BeginReloadAssembly (203ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (96ms)
		EndReloadAssembly (2884ms)
			LoadAssemblies (146ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1736ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1393ms)
				ProcessInitializeOnLoadMethodAttributes (225ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.28 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11029.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 31.325100 ms (FindLiveObjects: 0.977600 ms CreateObjectMapping: 1.126800 ms MarkObjects: 28.567000 ms  DeleteObjects: 0.652300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 95.62 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11029.
Memory consumption went from 300.2 MB to 298.2 MB.
Total: 36.282500 ms (FindLiveObjects: 1.599000 ms CreateObjectMapping: 1.559900 ms MarkObjects: 32.198600 ms  DeleteObjects: 0.923100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 47.78 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11029.
Memory consumption went from 300.2 MB to 298.2 MB.
Total: 25.715200 ms (FindLiveObjects: 1.125600 ms CreateObjectMapping: 1.167300 ms MarkObjects: 22.775800 ms  DeleteObjects: 0.644600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019868 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.16 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.107 seconds
Domain Reload Profiling:
	ReloadAssembly (3108ms)
		BeginReloadAssembly (221ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (95ms)
		EndReloadAssembly (2775ms)
			LoadAssemblies (147ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (528ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (135ms)
			SetupLoadedEditorAssemblies (1872ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1466ms)
				ProcessInitializeOnLoadMethodAttributes (287ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (32ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.43 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11046.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 37.735100 ms (FindLiveObjects: 1.136400 ms CreateObjectMapping: 1.858500 ms MarkObjects: 33.640100 ms  DeleteObjects: 1.098100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 55.81 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11046.
Memory consumption went from 300.3 MB to 298.2 MB.
Total: 43.838700 ms (FindLiveObjects: 1.196500 ms CreateObjectMapping: 1.394600 ms MarkObjects: 40.466400 ms  DeleteObjects: 0.779700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020863 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.27 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.976 seconds
Domain Reload Profiling:
	ReloadAssembly (2977ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2709ms)
			LoadAssemblies (117ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (493ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1889ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1465ms)
				ProcessInitializeOnLoadMethodAttributes (300ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 40.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11063.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 46.143000 ms (FindLiveObjects: 6.778400 ms CreateObjectMapping: 2.267600 ms MarkObjects: 36.262200 ms  DeleteObjects: 0.832400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 56.97 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11063.
Memory consumption went from 300.3 MB to 298.3 MB.
Total: 57.014800 ms (FindLiveObjects: 1.086900 ms CreateObjectMapping: 1.145500 ms MarkObjects: 53.879700 ms  DeleteObjects: 0.901200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016338 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.53 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.837 seconds
Domain Reload Profiling:
	ReloadAssembly (2838ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (75ms)
		EndReloadAssembly (2570ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (484ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (1764ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (13ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1394ms)
				ProcessInitializeOnLoadMethodAttributes (242ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.27 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11080.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 35.018100 ms (FindLiveObjects: 1.597700 ms CreateObjectMapping: 1.266700 ms MarkObjects: 31.345500 ms  DeleteObjects: 0.806800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 66.56 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11080.
Memory consumption went from 300.3 MB to 298.3 MB.
Total: 49.030500 ms (FindLiveObjects: 1.348800 ms CreateObjectMapping: 2.111400 ms MarkObjects: 44.475600 ms  DeleteObjects: 1.092800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018903 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 10.99 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.434 seconds
Domain Reload Profiling:
	ReloadAssembly (4436ms)
		BeginReloadAssembly (431ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (21ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (1ms)
			CreateAndSetChildDomain (229ms)
		EndReloadAssembly (3863ms)
			LoadAssemblies (228ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (680ms)
			ReleaseScriptCaches (6ms)
			RebuildScriptCaches (122ms)
			SetupLoadedEditorAssemblies (2730ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (111ms)
				ProcessInitializeOnLoadAttributes (2217ms)
				ProcessInitializeOnLoadMethodAttributes (371ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (34ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 31.00 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11097.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 22.687000 ms (FindLiveObjects: 0.855700 ms CreateObjectMapping: 1.190600 ms MarkObjects: 20.026100 ms  DeleteObjects: 0.613000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016277 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 12.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.876 seconds
Domain Reload Profiling:
	ReloadAssembly (2876ms)
		BeginReloadAssembly (157ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2613ms)
			LoadAssemblies (115ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (476ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (103ms)
			SetupLoadedEditorAssemblies (1821ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (12ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1429ms)
				ProcessInitializeOnLoadMethodAttributes (262ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 39.93 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11114.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 34.258800 ms (FindLiveObjects: 1.140600 ms CreateObjectMapping: 1.025200 ms MarkObjects: 31.319200 ms  DeleteObjects: 0.772400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016118 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.85 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.767 seconds
Domain Reload Profiling:
	ReloadAssembly (2768ms)
		BeginReloadAssembly (210ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (115ms)
		EndReloadAssembly (2448ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (478ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1635ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1278ms)
				ProcessInitializeOnLoadMethodAttributes (241ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 32.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11131.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 23.984600 ms (FindLiveObjects: 1.160300 ms CreateObjectMapping: 1.044400 ms MarkObjects: 21.135300 ms  DeleteObjects: 0.643100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5436.873738 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bffdc12fe02a91b304522f3f686b2356') in 0.185494 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016560 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.03 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.954 seconds
Domain Reload Profiling:
	ReloadAssembly (2955ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2670ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (470ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (106ms)
			SetupLoadedEditorAssemblies (1871ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1479ms)
				ProcessInitializeOnLoadMethodAttributes (274ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (30ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 34.65 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 11148.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 34.481300 ms (FindLiveObjects: 1.880300 ms CreateObjectMapping: 1.164100 ms MarkObjects: 30.668200 ms  DeleteObjects: 0.767100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017320 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 11.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.759 seconds
Domain Reload Profiling:
	ReloadAssembly (2760ms)
		BeginReloadAssembly (172ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2493ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (518ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (114ms)
			SetupLoadedEditorAssemblies (1635ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (12ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1269ms)
				ProcessInitializeOnLoadMethodAttributes (250ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.76 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11165.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 38.277300 ms (FindLiveObjects: 1.189300 ms CreateObjectMapping: 1.264500 ms MarkObjects: 34.921700 ms  DeleteObjects: 0.899800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 49.94 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11165.
Memory consumption went from 301.0 MB to 299.0 MB.
Total: 35.700400 ms (FindLiveObjects: 1.164800 ms CreateObjectMapping: 1.086000 ms MarkObjects: 32.586300 ms  DeleteObjects: 0.861600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015879 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 17.04 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  5.115 seconds
Domain Reload Profiling:
	ReloadAssembly (5116ms)
		BeginReloadAssembly (180ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (83ms)
		EndReloadAssembly (4822ms)
			LoadAssemblies (127ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (498ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (3536ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (17ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (3159ms)
				ProcessInitializeOnLoadMethodAttributes (241ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 25.84 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11182.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 30.961600 ms (FindLiveObjects: 1.088800 ms CreateObjectMapping: 1.007900 ms MarkObjects: 27.985400 ms  DeleteObjects: 0.877700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015848 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.42 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.739 seconds
Domain Reload Profiling:
	ReloadAssembly (2740ms)
		BeginReloadAssembly (167ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2472ms)
			LoadAssemblies (122ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (489ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1663ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1295ms)
				ProcessInitializeOnLoadMethodAttributes (247ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 38.41 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11199.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 35.781200 ms (FindLiveObjects: 1.485700 ms CreateObjectMapping: 1.192400 ms MarkObjects: 32.325300 ms  DeleteObjects: 0.773100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 58.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11199.
Memory consumption went from 300.7 MB to 298.7 MB.
Total: 37.821600 ms (FindLiveObjects: 1.517800 ms CreateObjectMapping: 1.510600 ms MarkObjects: 33.957500 ms  DeleteObjects: 0.834100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016558 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.13 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.066 seconds
Domain Reload Profiling:
	ReloadAssembly (3068ms)
		BeginReloadAssembly (194ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (87ms)
		EndReloadAssembly (2754ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (539ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (103ms)
			SetupLoadedEditorAssemblies (1865ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1473ms)
				ProcessInitializeOnLoadMethodAttributes (273ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (25ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 33.86 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11216.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 36.572600 ms (FindLiveObjects: 1.191400 ms CreateObjectMapping: 1.646600 ms MarkObjects: 32.902700 ms  DeleteObjects: 0.830400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016560 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.21 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.742 seconds
Domain Reload Profiling:
	ReloadAssembly (2743ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2478ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (493ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1681ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1310ms)
				ProcessInitializeOnLoadMethodAttributes (252ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (28ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 36.28 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11233.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 34.637600 ms (FindLiveObjects: 1.196400 ms CreateObjectMapping: 1.215800 ms MarkObjects: 31.317600 ms  DeleteObjects: 0.905900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 60.45 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11233.
Memory consumption went from 301.1 MB to 299.1 MB.
Total: 38.075600 ms (FindLiveObjects: 1.588500 ms CreateObjectMapping: 1.546100 ms MarkObjects: 33.140100 ms  DeleteObjects: 1.799300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 3071.318223 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/dailytask_iconframe.png
  artifactKey: Guid(3d7554964542258438e42ac1b9fba301) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/dailytask_iconframe.png using Guid(3d7554964542258438e42ac1b9fba301) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c2cb04676f6b9663f0b1bcad9432baed') in 0.093213 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/day_orangebg.png
  artifactKey: Guid(2a95ae3c66f1587418c920794dffd8c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/GameShop/UITexture/SeasonCard/day_orangebg.png using Guid(2a95ae3c66f1587418c920794dffd8c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b485a1084ae850a432fe81caf26d138') in 0.019118 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_03.png
  artifactKey: Guid(0ef497c3ce75cb24282402df1463a741) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_03.png using Guid(0ef497c3ce75cb24282402df1463a741) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dc342a255484e0ad6eddec15ba2cf202') in 0.239255 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_04.png
  artifactKey: Guid(0d749171020fa904988c6d5e53264318) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_04.png using Guid(0d749171020fa904988c6d5e53264318) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3be992b9a8699c6a6f891dd9289ed3e0') in 0.013341 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/DamageList/UITexture/damage_icon_1.png
  artifactKey: Guid(614c425679e21c348a06f0cf69d1ca2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/DamageList/UITexture/damage_icon_1.png using Guid(614c425679e21c348a06f0cf69d1ca2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ce82d555ce7e56f12c78f3ec993c1cd0') in 0.014858 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Task/TaskUI/dailytask_legend.png
  artifactKey: Guid(35986390ec15a03498c52ebbc7bf0f78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Task/TaskUI/dailytask_legend.png using Guid(35986390ec15a03498c52ebbc7bf0f78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'df96ac9f8263f6b13a500240791a374d') in 0.027873 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_02.png
  artifactKey: Guid(ebf4532c3a554f042bf119a1384591db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_02.png using Guid(ebf4532c3a554f042bf119a1384591db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '163f2b8099697c9c274e30a0a64eea1c') in 0.014312 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/BattleTask/UITexture/dailytask_progress2.png
  artifactKey: Guid(f8f562722fea90445b32f9e34b8ec400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/BattleTask/UITexture/dailytask_progress2.png using Guid(f8f562722fea90445b32f9e34b8ec400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '643156f9ac509b6ed204435461ad4458') in 0.018377 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/MagicaCloth/Example (Can be deleted)/UnityChan/Logo/Dark_Silhouette.png
  artifactKey: Guid(78faaf6ed3c032e45b86491a36928ea6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MagicaCloth/Example (Can be deleted)/UnityChan/Logo/Dark_Silhouette.png using Guid(78faaf6ed3c032e45b86491a36928ea6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a2e2d791eba26e9492217d30a9be6cb3') in 0.037872 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_08.png
  artifactKey: Guid(4a7e1217bb6aaac4da00b0af0bb4c3ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_08.png using Guid(4a7e1217bb6aaac4da00b0af0bb4c3ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e27d734e73d8ac2b50970737dc01c685') in 0.017717 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_01-1.png
  artifactKey: Guid(f3dcdc9b0833b7648ac7ca203ddf3689) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/ddt_icon_01-1.png using Guid(f3dcdc9b0833b7648ac7ca203ddf3689) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '53ce94ffb887292cf35839795f4e5fde') in 0.015088 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Shop/UITexture/def_title.png
  artifactKey: Guid(8fd3ce94db7b5eb4ab6f2d315b9e80b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Shop/UITexture/def_title.png using Guid(8fd3ce94db7b5eb4ab6f2d315b9e80b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8afd1dd0ef79501c6e1ca1126d51ec62') in 0.029837 seconds 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 54.78 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11252.
Memory consumption went from 301.5 MB to 299.4 MB.
Total: 39.341600 ms (FindLiveObjects: 1.302400 ms CreateObjectMapping: 2.238200 ms MarkObjects: 34.875500 ms  DeleteObjects: 0.924300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.020078 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 13.71 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.178 seconds
Domain Reload Profiling:
	ReloadAssembly (3179ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (2908ms)
			LoadAssemblies (132ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (522ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (111ms)
			SetupLoadedEditorAssemblies (2043ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (14ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1655ms)
				ProcessInitializeOnLoadMethodAttributes (259ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 44.12 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11268.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 44.946900 ms (FindLiveObjects: 1.306500 ms CreateObjectMapping: 4.765100 ms MarkObjects: 38.032400 ms  DeleteObjects: 0.841800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 66.50 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11268.
Memory consumption went from 301.2 MB to 299.2 MB.
Total: 46.935100 ms (FindLiveObjects: 1.617600 ms CreateObjectMapping: 1.690600 ms MarkObjects: 42.066700 ms  DeleteObjects: 1.559200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016926 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 11.10 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.423 seconds
Domain Reload Profiling:
	ReloadAssembly (4425ms)
		BeginReloadAssembly (1109ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (17ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (234ms)
		EndReloadAssembly (3189ms)
			LoadAssemblies (183ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (565ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (127ms)
			SetupLoadedEditorAssemblies (2214ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (11ms)
				BeforeProcessingInitializeOnLoad (115ms)
				ProcessInitializeOnLoadAttributes (1755ms)
				ProcessInitializeOnLoadMethodAttributes (315ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.15 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9735 Unused Serialized files (Serialized files now loaded: 0)
Unloading 169 unused Assets / (2.0 MB). Loaded Objects now: 11284.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 48.675900 ms (FindLiveObjects: 1.595300 ms CreateObjectMapping: 1.787200 ms MarkObjects: 44.447000 ms  DeleteObjects: 0.845000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 80.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11284.
Memory consumption went from 301.2 MB to 299.2 MB.
Total: 48.845000 ms (FindLiveObjects: 1.111200 ms CreateObjectMapping: 1.234500 ms MarkObjects: 45.627000 ms  DeleteObjects: 0.870800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016769 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.60 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.032 seconds
Domain Reload Profiling:
	ReloadAssembly (4034ms)
		BeginReloadAssembly (368ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (19ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (203ms)
		EndReloadAssembly (3551ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (515ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (2676ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (2201ms)
				ProcessInitializeOnLoadMethodAttributes (358ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.63 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9740 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 11306.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 21.791500 ms (FindLiveObjects: 0.855600 ms CreateObjectMapping: 0.871800 ms MarkObjects: 19.517200 ms  DeleteObjects: 0.545500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 57767.490959 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Panel_ModuleSelect.prefab
  artifactKey: Guid(a4268dcd9c25986488095fb53d820a54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/ModuleSelect/Panel_ModuleSelect.prefab using Guid(a4268dcd9c25986488095fb53d820a54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b15281bd222a5c84e1e06f1ac142fb8e') in 0.285864 seconds 
========================================================================
Received Import Request.
  Time since last request: 104.694836 seconds.
  path: Assets/AssetBundle/UI2/UITexture/SkillIcon/4.png
  artifactKey: Guid(810974c5fa1bcbf4f8304b0839779f2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/SkillIcon/4.png using Guid(810974c5fa1bcbf4f8304b0839779f2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f96a17ef2db63d9fcc4eafbae03e6d50') in 0.047971 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.838802 seconds.
  path: Assets/AssetBundle/UI2/UITexture/RideIcon/10002.png
  artifactKey: Guid(ef1dada6aa5e93f42a099c6286c002e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/RideIcon/10002.png using Guid(ef1dada6aa5e93f42a099c6286c002e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e19f28bc19dbc5447fa8cf8842cf2a1b') in 0.375569 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/UITexture/RideIcon/10003.png
  artifactKey: Guid(b2b0cac4332a65943ad12b2d5381341e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/RideIcon/10003.png using Guid(b2b0cac4332a65943ad12b2d5381341e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b74b88438ea04e8e3ad2aa8ec14ca0c7') in 0.337189 seconds 
========================================================================
Received Import Request.
  Time since last request: 29.228904 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215002.png
  artifactKey: Guid(66b9c942d45a7b54784f42fa0d9bfb26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215002.png using Guid(66b9c942d45a7b54784f42fa0d9bfb26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c3f240511191277e7abd850249256248') in 0.023281 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.935786 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215005.png
  artifactKey: Guid(aa50f5dd0ab819146acf493008676c4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215005.png using Guid(aa50f5dd0ab819146acf493008676c4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5039838d6353a7ecf87a3218511126da') in 2.310021 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215025.png
  artifactKey: Guid(33e9853da17fc34419eb18e9133f07aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215025.png using Guid(33e9853da17fc34419eb18e9133f07aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b04f43d2405a58014c91f3511a0f882a') in 0.040463 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215026.png
  artifactKey: Guid(9a659b97c1dcc6d4c8a80dab0f5786df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215026.png using Guid(9a659b97c1dcc6d4c8a80dab0f5786df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a2e29d8bcb3e7563824200e661c5b7ba') in 0.024085 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215014.png
  artifactKey: Guid(91f585c2e41135646b95747959f08005) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215014.png using Guid(91f585c2e41135646b95747959f08005) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4dccd13a4efdb7632ebcc870d39eb5f7') in 0.039379 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215015.png
  artifactKey: Guid(a03aba5c01b4d0d4aa2d64f5fe45dcf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215015.png using Guid(a03aba5c01b4d0d4aa2d64f5fe45dcf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '92b1c7e3847412a7d8cc44d3a85e93b9') in 0.077116 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215028.png
  artifactKey: Guid(439aa4e469b79c649ab3b6e1effc2906) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215028.png using Guid(439aa4e469b79c649ab3b6e1effc2906) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b6abeb8ca96ee5efdb218186c367d529') in 0.034327 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215030.png
  artifactKey: Guid(9cdee4ec9c9ad69409ff81a491320db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215030.png using Guid(9cdee4ec9c9ad69409ff81a491320db2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f6cfa70e6487df16de9e181011496dac') in 0.025227 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215017.png
  artifactKey: Guid(f55362aea0594e54d9ac0eea971d9e6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215017.png using Guid(f55362aea0594e54d9ac0eea971d9e6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e2bc2003eaae67ef30a82f31ff11569b') in 0.028928 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215003.png
  artifactKey: Guid(35e5232dad7f20b43a78f28d1cc69be5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215003.png using Guid(35e5232dad7f20b43a78f28d1cc69be5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3927608874e9a3462864d91c679cdb98') in 0.059407 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215031.png
  artifactKey: Guid(9b08553db6de16843aa420740245adef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215031.png using Guid(9b08553db6de16843aa420740245adef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b446d71a1e11476213241b00d0f433f') in 0.019997 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215021.png
  artifactKey: Guid(631064bab6161a54da22a8e23567abcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215021.png using Guid(631064bab6161a54da22a8e23567abcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '611bea50e3a3bc378ebecd60d8eafcde') in 0.025497 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215013.png
  artifactKey: Guid(8ce7ad1687b0476408606510d8cf1c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215013.png using Guid(8ce7ad1687b0476408606510d8cf1c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cbef0b458bcbfedca898f3cf8399b186') in 0.018679 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215019.png
  artifactKey: Guid(b74db477987740441a67c9e43c13b755) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215019.png using Guid(b74db477987740441a67c9e43c13b755) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '049f73424bca83eb77b45af2e159433a') in 0.041855 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215007.png
  artifactKey: Guid(60981210aed971d4fa604ef5b12ed0c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215007.png using Guid(60981210aed971d4fa604ef5b12ed0c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fecc9e794a635cb4fe52cbeb962aee31') in 0.022780 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215020.png
  artifactKey: Guid(7c04c19f168465441b3881863d40865a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215020.png using Guid(7c04c19f168465441b3881863d40865a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8641989bf724a738f510ba47d57f0d41') in 0.018306 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.223252 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215033.png
  artifactKey: Guid(fa871de5cbc119e4baa9a9651181bf88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215033.png using Guid(fa871de5cbc119e4baa9a9651181bf88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a5c758d095774d83bf9acfc9bfa305de') in 0.057982 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215034.png
  artifactKey: Guid(f19c1002c718e4a4db452f2753d8aa9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215034.png using Guid(f19c1002c718e4a4db452f2753d8aa9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c99f6e071a5eff79f8207448c452fe6d') in 0.018273 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215038.png
  artifactKey: Guid(f49d498fcba0363489e003252402ff18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215038.png using Guid(f49d498fcba0363489e003252402ff18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '71ad3da82e6c01763a1a7346ab1d5af5') in 0.028252 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215037.png
  artifactKey: Guid(492c7b4ac4a6b8f4ba5fec53a861ec3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215037.png using Guid(492c7b4ac4a6b8f4ba5fec53a861ec3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '476207b4a665998fd3968323a35cd35b') in 0.044017 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215035.png
  artifactKey: Guid(ae8727c08babe2f4aa02f357f6d125e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215035.png using Guid(ae8727c08babe2f4aa02f357f6d125e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '381ba329a527880d89d8e12f325a5693') in 0.039616 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215040.png
  artifactKey: Guid(953d8489dbfd3364f96071d2006b729c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215040.png using Guid(953d8489dbfd3364f96071d2006b729c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '902a6de8a5e536560c0c68ade432f850') in 0.019114 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/UITexture/ChatFrame/215043.png
  artifactKey: Guid(31ae26ba334f6244c9467a3da0b491ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/ChatFrame/215043.png using Guid(31ae26ba334f6244c9467a3da0b491ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a32909b7669ea4ab23d4c35825cf9456') in 0.017163 seconds 
========================================================================
Received Import Request.
  Time since last request: 255.606982 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/ShareUI/icon_extract.png
  artifactKey: Guid(2451a0e17773a094a950290cecfe77ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/ShareUI/icon_extract.png using Guid(2451a0e17773a094a950290cecfe77ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9c550ba8a26c683483e66a947bf5c2b3') in 0.028276 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_2.png
  artifactKey: Guid(2deebdc897733cb40828e4dba826fdad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_2.png using Guid(2deebdc897733cb40828e4dba826fdad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e4a7e992a4a51a89ef07d68c1424bc29') in 0.021353 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_3.png
  artifactKey: Guid(c364a6aed6017af4da533b75bd9bbf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_3.png using Guid(c364a6aed6017af4da533b75bd9bbf06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '765b3b3b516e54afb4c3aa0d505c8cac') in 0.170130 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_4.png
  artifactKey: Guid(8ea0984a674b39445bd5092e91e29e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_4.png using Guid(8ea0984a674b39445bd5092e91e29e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8ad7c49d3b7f4c7bf7e5d51ceca209d0') in 0.023468 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_6.png
  artifactKey: Guid(bae02240fbd278a42939274cb83d4314) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/LittleMap/LittleMapUI/icon_extract_6.png using Guid(bae02240fbd278a42939274cb83d4314) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '125d9e82736eef909885b70218aacff1') in 0.015748 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0