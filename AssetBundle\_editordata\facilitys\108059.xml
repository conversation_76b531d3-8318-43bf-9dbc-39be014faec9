<?xml version="1.0" encoding="utf-8"?>
<FacilityTemplate type="DeepCore.GameData.Zone.FacilityTemplate">
  <Btns element_type="DeepCore.GameData.Zone.FacilityTemplate+FacilityButton" />
  <Coms element_type="DeepCore.GameData.Zone.FacilityCom" />
  <FileName>sgcmod_survival/prefab/object_prefab/prop_prefab/c_prop_lua_switch01.assetbundles</FileName>
  <ID>108059</ID>
  <Name>机关开关</Name>
  <ParamID>10002</ParamID>
  <Type>CipherMachine</Type>
  <UserDefineVars element_type="DeepCore.GameData.Zone.FacilityTemplate+ZoneVar">
    <element>
      <Key>DegreePerSecond</Key>
      <Value type="System.Int64">10</Value>
    </element>
    <element>
      <Key>GeneralIncreaseCompletion</Key>
      <Value type="System.Int64">20</Value>
    </element>
    <element>
      <Key>PerfectIncreaseCompletion</Key>
      <Value type="System.Int64">60</Value>
    </element>
    <element>
      <Key>MultipleRepairBonus</Key>
      <Value type="System.Int64">50</Value>
    </element>
  </UserDefineVars>
  <property.EditorPath>设施/机关开关(108059)</property.EditorPath>
</FacilityTemplate>