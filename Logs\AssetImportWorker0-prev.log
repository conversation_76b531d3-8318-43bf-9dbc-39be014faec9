Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker0.log
-srvPort
2710
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [9236] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1371523236 [EditorId] 1371523236 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [9236] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1371523236 [EditorId] 1371523236 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 995.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56892
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001969 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 947.27 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.558 seconds
Domain Reload Profiling:
	ReloadAssembly (1559ms)
		BeginReloadAssembly (79ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1374ms)
			LoadAssemblies (78ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (115ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (1153ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (947ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (143ms)
				ProcessInitializeOnLoadMethodAttributes (53ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.018982 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 940.47 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Mono: successfully reloaded assembly
- Completed reload, in  3.726 seconds
Domain Reload Profiling:
	ReloadAssembly (3727ms)
		BeginReloadAssembly (119ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (3493ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (478ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (148ms)
			SetupLoadedEditorAssemblies (2685ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (941ms)
				BeforeProcessingInitializeOnLoad (140ms)
				ProcessInitializeOnLoadAttributes (1225ms)
				ProcessInitializeOnLoadMethodAttributes (367ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 14.42 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10226 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 19.629900 ms (FindLiveObjects: 0.853300 ms CreateObjectMapping: 0.813500 ms MarkObjects: 17.130200 ms  DeleteObjects: 0.831500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 557.454251 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz2.png
  artifactKey: Guid(3c5afdabb6c6893429f6416e1b435242) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz2.png using Guid(3c5afdabb6c6893429f6416e1b435242) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f28158b74d5d6c7e99144699b80c902c') in 0.228880 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_arrow_left.png
  artifactKey: Guid(76a61498280633e478d23583e02ac10a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_arrow_left.png using Guid(76a61498280633e478d23583e02ac10a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '170c13ebc8d3316a3fcc072bfafdd666') in 0.008051 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz4.png
  artifactKey: Guid(3b4d97ab5b1fbc74fa9e391a8da94ebf) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_pz4.png using Guid(3b4d97ab5b1fbc74fa9e391a8da94ebf) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '189d1de975bcd715ae8efe1ab12ca4cb') in 0.069320 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSimple@Elite_Field_03_E.tga
  artifactKey: Guid(b8915d496a236664a99f9bb3f5740df3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemSimple@Elite_Field_03_E.tga using Guid(b8915d496a236664a99f9bb3f5740df3) Importer(-1,00000000000000000000000000000000) crunched in 0.007140
 -> (artifact id: 'bf2a5ffe20174af7648fdddd305bdb66') in 0.055600 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_AO.tga
  artifactKey: Guid(cab4749606d342849bc831133dd37726) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_AO.tga using Guid(cab4749606d342849bc831133dd37726) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'd028256c982e1d530ef8cd0e75edfd74') in 0.338002 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/textures/bake_rongyandong01_03_d.png
  artifactKey: Guid(ffcf627272733cb46b46708c6677af44) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_rongyandong01_03_d.png using Guid(ffcf627272733cb46b46708c6677af44) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '1a7c402326b75e1a172010a75a2458ee') in 0.034515 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_S.tga
  artifactKey: Guid(d8a877fbb8195f741b91c46f7515d4d4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_S.tga using Guid(d8a877fbb8195f741b91c46f7515d4d4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'fc32df3239d61a744f9d93c94c5eb3b4') in 0.120582 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_D.tga
  artifactKey: Guid(d103ff38dc79b3a41aa9ade453e14234) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_D.tga using Guid(d103ff38dc79b3a41aa9ade453e14234) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '37c6c03893e0c4ed6b8a05e19cba9b63') in 0.129125 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_D.tga
  artifactKey: Guid(c80b89e449c7a2b4e8450ce0634d38a7) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_D.tga using Guid(c80b89e449c7a2b4e8450ce0634d38a7) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'a780a30befa5c90b65ac1085b0045197') in 0.129929 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_D.tga
  artifactKey: Guid(fc3398c3a56e351428de0a2227ca637b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemCrystal@Elite_Field_02_D.tga using Guid(fc3398c3a56e351428de0a2227ca637b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '95ea9ca6e134b17fe6f93a31e84adff5') in 0.180706 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_S.tga
  artifactKey: Guid(d11a1fc8ebe13694883ff55c3a5edbff) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_S.tga using Guid(d11a1fc8ebe13694883ff55c3a5edbff) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'f090ceadc4dee55e8253738ad0d94611') in 0.163611 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_E.tga
  artifactKey: Guid(cfcc1e6311b56af4cac7e0b6f5e5f9d9) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_E.tga using Guid(cfcc1e6311b56af4cac7e0b6f5e5f9d9) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b0d1d6ff8f813eb17ac55d023e289a8e') in 0.174603 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_N.tga
  artifactKey: Guid(f936bf0f9b4a7a04b955beabee38b756) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_N.tga using Guid(f936bf0f9b4a7a04b955beabee38b756) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '2f0ecb4bdf26e0365f103800a7cbae05') in 0.203920 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/Golem@Elite_Field_02_AO.tga
  artifactKey: Guid(7fa2cc9797f5b2d498f823fdc7e40bba) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/Golem@Elite_Field_02_AO.tga using Guid(7fa2cc9797f5b2d498f823fdc7e40bba) Importer(-1,00000000000000000000000000000000) crunched in 0.098034
 -> (artifact id: '82eac986757d7b11d32539be45c80591') in 0.247092 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_AO.tga
  artifactKey: Guid(ee9a026f1a1eb0545b4d1be5e8e6bfa8) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_AO.tga using Guid(ee9a026f1a1eb0545b4d1be5e8e6bfa8) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '10281c70009d18746fe2e89fec6eeea9') in 0.294718 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.003017 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_E.tga
  artifactKey: Guid(933fd51256f55754db8f1191d47dfb96) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_E.tga using Guid(933fd51256f55754db8f1191d47dfb96) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '54eb223e2e47e834083bd8f38f156387') in 0.145992 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_D.tga
  artifactKey: Guid(000b689d414cb6f4bb32b8876db4e307) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_D.tga using Guid(000b689d414cb6f4bb32b8876db4e307) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '2e1ebddbf07602b0418f3e39a1b899fc') in 0.197007 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Scene/bake/bake_pubudong01/Lightmap-0_comp_light.exr
  artifactKey: Guid(ddf6cff23898941429697dfedabf0770) Importer(-1,00000000000000000000000000000000)
Start importing Assets/Scene/bake/bake_pubudong01/Lightmap-0_comp_light.exr using Guid(ddf6cff23898941429697dfedabf0770) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '30606acae8be14b73224de08c7ed3c6d') in 0.714335 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_S.tga
  artifactKey: Guid(79fe42cb484797b40ae95eff8575d48d) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_S.tga using Guid(79fe42cb484797b40ae95eff8575d48d) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'a8d737d1c5b032e545a8d42c0afc98e9') in 0.147888 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_E.tga
  artifactKey: Guid(cffcb3f18866ec3488a12287160a601e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_E.tga using Guid(cffcb3f18866ec3488a12287160a601e) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'e511efc5c40e5dd7e1f75b465c82b5a5') in 0.231266 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_N.tga
  artifactKey: Guid(483ef93d0975f564bae3f38146f9e8b0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_N.tga using Guid(483ef93d0975f564bae3f38146f9e8b0) Importer(-1,00000000000000000000000000000000) crunched in 1.199106
 -> (artifact id: '0a64648db361feffeba7e50dc02e7e82') in 1.380450 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_N.tga
  artifactKey: Guid(92e8f28dabc199a4896d6dd1cd0426f5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_N.tga using Guid(92e8f28dabc199a4896d6dd1cd0426f5) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ed89b26192e9cb5341ba89248e1dd2d6') in 0.187155 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.011709 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_empty.png
  artifactKey: Guid(20a82daa4f4cc6e4f9b12744379e8c1a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/inlay_empty.png using Guid(20a82daa4f4cc6e4f9b12744379e8c1a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '30d43208cc44c0cc3236937a0f6ef464') in 0.014560 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_S.tga
  artifactKey: Guid(e3cd5845d0ab68c449dfe7dbc529cb55) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_S.tga using Guid(e3cd5845d0ab68c449dfe7dbc529cb55) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'fd2dfb312e026d155e281bbadda1dfab') in 0.252899 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_D.tga
  artifactKey: Guid(8b45e89668cdf634fb7949fe48faa958) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_D.tga using Guid(8b45e89668cdf634fb7949fe48faa958) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '1443fdacf0206ba53879021cf0669169') in 0.148001 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_N.tga
  artifactKey: Guid(46fc2b4410e25ec4ea3eec599a217223) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_N.tga using Guid(46fc2b4410e25ec4ea3eec599a217223) Importer(-1,00000000000000000000000000000000) crunched in 1.356024
 -> (artifact id: 'a3aec0dc696ecb872a7219ba294c9153') in 1.502824 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_arrow_right.png
  artifactKey: Guid(5ea417762047f1846a48579a2bcb5416) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/refine_arrow_right.png using Guid(5ea417762047f1846a48579a2bcb5416) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3c80686738a49ede28d925c54f69ed39') in 0.089937 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_S.tga
  artifactKey: Guid(a1b508a8fe4a4bb438501a5b627f14fb) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_01/Materials/GolemRock@Elite_Field_01_S.tga using Guid(a1b508a8fe4a4bb438501a5b627f14fb) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '7aa59ad729a851b9e54ba233a830f144') in 0.136144 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.006244 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_S.tga
  artifactKey: Guid(392cb9131a2680749bcd732dbff04767) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_S.tga using Guid(392cb9131a2680749bcd732dbff04767) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'df747b0c2456357f03cd7ebdd6ca119e') in 0.135772 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_lock.png
  artifactKey: Guid(a836a73eb24410c449e23c1b3cf15e75) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_lock.png using Guid(a836a73eb24410c449e23c1b3cf15e75) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0855b4e4f38cbf30c334478df7eadb46') in 0.029268 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_N.tga
  artifactKey: Guid(2bfbe4d0363f88b4f82fcd978daba1aa) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_N.tga using Guid(2bfbe4d0363f88b4f82fcd978daba1aa) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '161b761ef79bccf85c9b77bb3b043ef8') in 0.152385 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawData/object/textures/bake_rongyandong01_02_d.png
  artifactKey: Guid(63e62cbe2e76af74a812ec56e7291e63) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_rongyandong01_02_d.png using Guid(63e62cbe2e76af74a812ec56e7291e63) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '5fb137b488687a2471fb91d4b3be0a85') in 0.110073 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.010649 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_D.tga
  artifactKey: Guid(4402afad4e508034489623273f9bd78b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_D.tga using Guid(4402afad4e508034489623273f9bd78b) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'fbb0c2b3705f0341d29d42c7689acecb') in 0.166130 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_S.tga
  artifactKey: Guid(9caea01594cab5c4eac65a8118d6c9a3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemRock@Elite_Field_03_S.tga using Guid(9caea01594cab5c4eac65a8118d6c9a3) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '0c0913b526fdf722661c3936eb4cec26') in 1.686064 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSimple@Elite_Field_02_E.tga
  artifactKey: Guid(a67080d2775bd2148a5b29242da6f872) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSimple@Elite_Field_02_E.tga using Guid(a67080d2775bd2148a5b29242da6f872) Importer(-1,00000000000000000000000000000000) crunched in 0.006333
 -> (artifact id: 'd915bd0029ddbb57aa3cc9051cae4a87') in 0.346913 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_AO.tga
  artifactKey: Guid(bc87a65d50d2e564a9f5341eea4e5728) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_AO.tga using Guid(bc87a65d50d2e564a9f5341eea4e5728) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ee8701fabbc209e9212aa115033484cc') in 0.421397 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_AO.tga
  artifactKey: Guid(525eac8a44367d847bac5ad0a7a45f4a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonArmor@Boss_Stronghold_01_AO.tga using Guid(525eac8a44367d847bac5ad0a7a45f4a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '6cf217482b5bf8423e3db5f5a4cc747b') in 0.102541 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_D.tga
  artifactKey: Guid(3315f4554a37f25448777badc31a62d6) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemSand@Elite_Field_02_D.tga using Guid(3315f4554a37f25448777badc31a62d6) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b623c19c0bc560565d4091501e85844a') in 0.136996 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/RawData/object/textures/bake_rongyandong01_05_d.png
  artifactKey: Guid(784d01f845248544ba9db3e8c13fd8c3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_rongyandong01_05_d.png using Guid(784d01f845248544ba9db3e8c13fd8c3) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '86bb5a1b75060fca2106d62786216ca5') in 0.019454 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_unlock.png
  artifactKey: Guid(ff1fb8755ba31d640aa89773de671259) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_unlock.png using Guid(ff1fb8755ba31d640aa89773de671259) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '78557a73de2ee838cdd8033aabd9e53c') in 0.006186 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_N.tga
  artifactKey: Guid(4a85727a39d6d404aafdfa0c677b9831) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Body_01_N.tga using Guid(4a85727a39d6d404aafdfa0c677b9831) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '379d326155a0391ae3365842f53b86ca') in 0.199907 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_E.tga
  artifactKey: Guid(ff0700a56470e5b48b4e7af24eb3ff09) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Dragon@Boss_Stronghold/Materials/DragonBody@Boss_Stronghold_01_E.tga using Guid(ff0700a56470e5b48b4e7af24eb3ff09) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '68e806bed15cee13ae51f5ba22e3721f') in 0.160459 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Scene/bake/bake_rongyandong01/Lightmap-0_comp_light.exr
  artifactKey: Guid(e9844f626c593b44fb342d28778b6b8a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/Scene/bake/bake_rongyandong01/Lightmap-0_comp_light.exr using Guid(e9844f626c593b44fb342d28778b6b8a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '71918cb0838041f981ed2f24fac9efe7') in 0.626508 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_D.tga
  artifactKey: Guid(08403870189025f42b7b4e0fc8279eb6) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/GolemStone@Elite_Field_03_D.tga using Guid(08403870189025f42b7b4e0fc8279eb6) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '4f3a1644ef57371ddcd7c6c0ca08ccac') in 0.119924 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_unlock.png
  artifactKey: Guid(e9538706f6df59745915d5ef66898f72) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/chip/chip_item_unlock.png using Guid(e9538706f6df59745915d5ef66898f72) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'dcfe6ba902d44ed2d7bb6223dea76753') in 0.097619 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_D.tga
  artifactKey: Guid(23b1ff255e635004cb9e86d73b41c281) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_D.tga using Guid(23b1ff255e635004cb9e86d73b41c281) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '5297637209d4768f730d137a132cbd99') in 0.456374 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_chip.png
  artifactKey: Guid(5750dea67fd97e941a73c97bc85aff96) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/Forms/Bag/UITexture/bag_tab_chip.png using Guid(5750dea67fd97e941a73c97bc85aff96) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '46d229bd2a802a1f669ffaac16d0036e') in 0.020520 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_D.tga
  artifactKey: Guid(bc87d210c57ce23459246c43963e737a) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/Materials/Golemcrystal@Elite_Field_03_D.tga using Guid(bc87d210c57ce23459246c43963e737a) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '8ca7d0223e5c476e49dfb8311ec7f467') in 0.116147 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/RawData/object/textures/bake_rongyandong01_04_d.png
  artifactKey: Guid(143a5c4a2f4a54f43bbadeea22aa5b00) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/textures/bake_rongyandong01_04_d.png using Guid(143a5c4a2f4a54f43bbadeea22aa5b00) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '649799a7db10913fd8550d56d0fa3a48') in 0.169444 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_golden4.png
  artifactKey: Guid(f70aa850af47e854a97b621fe5ec27d4) Importer(-1,00000000000000000000000000000000)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBtn/com_btn_golden4.png using Guid(f70aa850af47e854a97b621fe5ec27d4) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'ba5638e632e4666a35ec6a53475b5a14') in 0.008959 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_D.tga
  artifactKey: Guid(08f7afdc6cac41a4b99ee6848bc6a5eb) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Wings_01_D.tga using Guid(08f7afdc6cac41a4b99ee6848bc6a5eb) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'edaec0547e628affd372695d962b5a9f') in 1.878068 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_D.tga
  artifactKey: Guid(c4e90cab5fa917646b012be17a9e5a43) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemStone@Elite_Field_02_D.tga using Guid(c4e90cab5fa917646b012be17a9e5a43) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '9af39631819caac5c1f4b57b5e0adfa8') in 0.183847 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_E.tga
  artifactKey: Guid(b6bbdfe4266daf54eb81266f675a6ecd) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Materials/GolemRock@Elite_Field_02_E.tga using Guid(b6bbdfe4266daf54eb81266f675a6ecd) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'b9b2998bc7abfa5facd375b3f380fd91') in 0.065842 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_N.tga
  artifactKey: Guid(ca4665108d1feab49af8cfe0dc1e320e) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Hydra@Boss_Stronghold/Materials/Hydra@Boss_Stronghold_Head_01_N.tga using Guid(ca4665108d1feab49af8cfe0dc1e320e) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'cbec81489af037a88edd370826fbb51f') in 0.146630 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.495495 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/GolemStone@Elite_Field_02.fbx
  artifactKey: Guid(e6b8e05428d816e4482fe0be008c3783) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/GolemStone@Elite_Field_02.fbx using Guid(e6b8e05428d816e4482fe0be008c3783) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '3b044cb5172aca5633ed559c4ed93daa') in 0.327571 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(c6bd8f758b5152c4aa5aea44cb5ba6a5) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(c6bd8f758b5152c4aa5aea44cb5ba6a5) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 15.053807%;
File 'Anim_Giant@hit2' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@hit2' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '95b2ed7574794002e6b5a7466bcb852e') in 0.218616 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@hit2' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@hit2' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(2a7266924347a0b4ab658714fdd8b7ef) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(2a7266924347a0b4ab658714fdd8b7ef) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.963213%;
File 'Anim_Giant@attack3' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@attack3' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'f918e62da0aae18a2fcfbbb77aad4875') in 0.209904 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@attack3' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@attack3' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@run_inplace.FBX
  artifactKey: Guid(02d6f971035d2af429c3940b2c8cb18b) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@run_inplace.FBX using Guid(02d6f971035d2af429c3940b2c8cb18b) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.863048%;
File 'Anim_Giant@run_inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@run_inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '4dd5b209f30132f7aeabb111942de6b7') in 0.150873 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/Anim_Giant@run_inplace.FBX' had errors: 
 File 'Anim_Giant@run_inplace' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@run_inplace' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_03/GolemStone@Elite_Field_03.fbx
  artifactKey: Guid(3165832d8035cce4cbd016e102fab348) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_03/GolemStone@Elite_Field_03.fbx using Guid(3165832d8035cce4cbd016e102fab348) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '4c430f9d52c852f0c382ee1f405cef9f') in 0.136751 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(ab995c3b5f1f6c344aeef7ae4b1dbe70) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(ab995c3b5f1f6c344aeef7ae4b1dbe70) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 11.296926%;
File 'Anim_Giant@idlebreak' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@idlebreak' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '0cb77445186e91301fe9d38ed3a3ed95') in 0.154142 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@idlebreak' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@idlebreak' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(8feec97d240804e4c8413ea5dd8597c6) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(8feec97d240804e4c8413ea5dd8597c6) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 2.899861%;
File 'Anim_Regular@jump' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@jump' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'd298135857e8a2f48c5abb08a1fa4c79') in 0.319077 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@jump' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@jump' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(4b0b2aed4a52874428fd7e68d5beeac0) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(4b0b2aed4a52874428fd7e68d5beeac0) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 12.320889%;
File 'Anim_Giant@walk' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Giant@walk' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'c53e77cb5bb88b95546eeb8ec5beb00e') in 0.184969 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Giant@walk' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Giant@walk' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.006977 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(e05a1b3a70199554e9c23007ca1269ec) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(e05a1b3a70199554e9c23007ca1269ec) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 3.688661%;
File 'Anim_Regular@punch' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@punch' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: '832af965c7690280b2fda6569093658a') in 0.342920 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@punch' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@punch' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>
  artifactKey: Guid(60f387a501b2d5748bb7047b0c11aece) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL> using Guid(60f387a501b2d5748bb7047b0c11aece) Importer(-1,00000000000000000000000000000000) Keyframe reduction: Ratio: 14.415682%;
File 'Anim_Regular@jump-long' has rig import warnings. See Import Messages in Rig Import Tab for more details.
File 'Anim_Regular@jump-long' has animation import warnings. See Import Messages in Animation Import Settings for more details.
 -> (artifact id: 'c1e4e70b4a2d0336253c62a808b18fb4') in 0.100206 seconds 
  ERROR: Import of 'Assets/RawData/object/models/monster/Golem@Elite_Field_02/<EMAIL>' had errors: 
 File 'Anim_Regular@jump-long' has rig import warnings. See Import Messages in Rig Import Tab for more details.

 File 'Anim_Regular@jump-long' has animation import warnings. See Import Messages in Animation Import Settings for more details.


========================================================================
Received Import Request.
  Time since last request: 0.033634 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01_box.FBX
  artifactKey: Guid(********************************) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01_box.FBX using Guid(********************************) Importer(-1,00000000000000000000000000000000)  -> (artifact id: '78c6320180a6d3a72afea59d76b0377e') in 0.053920 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01_lod.FBX
  artifactKey: Guid(f75b10db08ca69d499864e215912b2b7) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01_lod.FBX using Guid(f75b10db08ca69d499864e215912b2b7) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.05 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.05 seconds
 -> (artifact id: 'ccf9037e601976b79b0918ab3ca988aa') in 0.094534 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/RawData/object/models/outdoors/o_build_huoshancangku01_zhaozi.fbx
  artifactKey: Guid(f894ea0a7623e3942a5191cd1a2a0b10) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/o_build_huoshancangku01_zhaozi.fbx using Guid(f894ea0a7623e3942a5191cd1a2a0b10) Importer(-1,00000000000000000000000000000000)  -> (artifact id: 'eb939020bdc4890d106fd4bd0e063fa7') in 0.060895 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RawData/object/models/outdoors/bake_pubudong01.FBX
  artifactKey: Guid(132f1959143ddb94cb28349e91942de3) Importer(-1,00000000000000000000000000000000)
Start importing Assets/RawData/object/models/outdoors/bake_pubudong01.FBX using Guid(132f1959143ddb94cb28349e91942de3) Importer(-1,00000000000000000000000000000000) Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.07 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.05 seconds
Launched and connected uv unwrapper F:/Unity 2021.3.15f1/Editor/Data/Tools/UnwrapCL.exe after 0.06 seconds
 -> (artifact id: '6ad8c2e4da8cd2e7ec1832ca5dc3dc16') in 1.248510 seconds 
========================================================================
Received Import Request.
  Time since last request: 553.008789 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab
  artifactKey: Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Panel_Bag.prefab using Guid(275710024206f604fba168e20b84742b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '75547fcf3ba1fd9e2adb1e463f276489') in 0.120830 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017337 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 831.63 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.919 seconds
Domain Reload Profiling:
	ReloadAssembly (3921ms)
		BeginReloadAssembly (383ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (17ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (231ms)
		EndReloadAssembly (3431ms)
			LoadAssemblies (129ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (533ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (2576ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (832ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1381ms)
				ProcessInitializeOnLoadMethodAttributes (264ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Curl error 35: Cert handshake failed. verify result: UNITYTLS_X509VERIFY_FATAL_ERROR. error state: 7
Refreshing native plugins compatible for Editor in 30.03 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9749 Unused Serialized files (Serialized files now loaded: 0)
Unloading 175 unused Assets / (2.0 MB). Loaded Objects now: 10780.
Memory consumption went from 0.56 GB to 0.55 GB.
Total: 21.531700 ms (FindLiveObjects: 0.923100 ms CreateObjectMapping: 0.932100 ms MarkObjects: 19.132900 ms  DeleteObjects: 0.542000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3386.997183 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle
  artifactKey: Guid(181f509b2397ab240a99a085decd8ad7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle using Guid(181f509b2397ab240a99a085decd8ad7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '10384a3293ba447e29271af233702e15') in 0.063469 seconds 
========================================================================
Received Import Request.
  Time since last request: 18.762572 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Equipment
  artifactKey: Guid(feb1283da166f2843b7d6fd6bfbfb247) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Equipment using Guid(feb1283da166f2843b7d6fd6bfbfb247) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '63b0364eddcf8dddee1f230f252ba250') in 0.001172 seconds 
========================================================================
Received Import Request.
  Time since last request: 57.151439 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/BattleMail
  artifactKey: Guid(6fdb54195cead5f43bea0376a5a81c16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/BattleMail using Guid(6fdb54195cead5f43bea0376a5a81c16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '91239e293f3e6476b053eaf377f00361') in 0.001340 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.915127 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Equipment/Panel_EquipmentDomain.prefab
  artifactKey: Guid(eeea1aaf4b4dcac4ab14a5d9efa72559) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Equipment/Panel_EquipmentDomain.prefab using Guid(eeea1aaf4b4dcac4ab14a5d9efa72559) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ccfd10065a7dfdd240d820fafb2f5a4') in 0.053877 seconds 
========================================================================
Received Import Request.
  Time since last request: 117.254751 seconds.
  path: Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png
  artifactKey: Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/UITexture/Common/CommBg/comm_bg_grey2.png using Guid(cc4753bdaf9511a47b713141bf8061ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '88edb2931ed9dd1d999fa093613c6416') in 0.125383 seconds 
========================================================================
Received Import Request.
  Time since last request: 1021.170087 seconds.
  path: Assets/AssetBundle/UI2/Forms/PlayerDiaplay/Display3DRoleForTouch.prefab
  artifactKey: Guid(7feef9f6b8c13f44fb03b7e9f25c0d1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/PlayerDiaplay/Display3DRoleForTouch.prefab using Guid(7feef9f6b8c13f44fb03b7e9f25c0d1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0e9395188cda213dfad69a665ecbe8dc') in 0.015731 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Parasaurolophus_MainCityDisplay.prefab
  artifactKey: Guid(52778519107a21749891e22d5953fa7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Parasaurolophus_MainCityDisplay.prefab using Guid(52778519107a21749891e22d5953fa7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9502a7a967657a797aff5002abc8b429') in 2.031529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/RawData/object/models/monster/raptors/Raptors_MainCityDisplay.playable
  artifactKey: Guid(71b83614b2b22004e82886dc42bd76fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawData/object/models/monster/raptors/Raptors_MainCityDisplay.playable using Guid(71b83614b2b22004e82886dc42bd76fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'baabe328f9827b12a49b350b33ecd271') in 0.100698 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Raptors_MainCityDisplay.prefab
  artifactKey: Guid(ff54daeb8b0b6264aa2fef3d4f4d908d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/monster_prefab/DisplayForMainCity/Raptors_MainCityDisplay.prefab using Guid(ff54daeb8b0b6264aa2fef3d4f4d908d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32c0ad0d73c07aff008b936f1a2a0122') in 0.088832 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0