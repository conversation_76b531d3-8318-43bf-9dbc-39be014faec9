<?xml version="1.0" encoding="utf-8"?>
<FacilityTemplate type="DeepCore.GameData.Zone.FacilityTemplate">
  <Btns element_type="DeepCore.GameData.Zone.FacilityTemplate+FacilityButton">
    <element>
      <BtnID>1</BtnID>
      <BtnName>破解密码</BtnName>
      <EnergyID element_type="DeepCore.GameData.Zone.FacilityTemplate+ButtonEnergyID">
        <element>
          <EnergyID>1</EnergyID>
        </element>
        <element>
          <EnergyID>2</EnergyID>
        </element>
      </EnergyID>
      <MasterCanUse>False</MasterCanUse>
      <UseUnitCamp>3</UseUnitCamp>
    </element>
  </Btns>
  <Coms element_type="DeepCore.GameData.Zone.FacilityCom">
    <element type="DeepCore.GameData.Zone.FacilityProgressCom">
      <DegreeOfCompletion>1000</DegreeOfCompletion>
      <ID>1</ID>
      <PosAnchor>Center</PosAnchor>
      <PosX>-157</PosX>
      <PosY>-229</PosY>
      <ProgressBarStyle>CipherMachine</ProgressBarStyle>
      <UseBtnName>破解密码</UseBtnName>
    </element>
    <element type="DeepCore.GameData.Zone.FacilityOperationCom">
      <ID>2</ID>
      <UseAction>Build.Build</UseAction>
    </element>
  </Coms>
  <FileName>sgcmod_survival/prefab/object_prefab/prop_prefab/c_prop_wuqigui.assetbundles</FileName>
  <ID>310001</ID>
  <Name>逃生门</Name>
  <ParamID>10002</ParamID>
  <Type>DecodingSwitch</Type>
  <UserDefineVars element_type="DeepCore.GameData.Zone.FacilityTemplate+ZoneVar">
    <element>
      <Key>DegreePerSecond</Key>
      <Value type="System.Int64">10</Value>
    </element>
    <element>
      <Key>IsActivate</Key>
      <Value type="System.Int64">0</Value>
    </element>
    <element>
      <Key>MultipleRepairBonus</Key>
      <Value type="System.Int64">20</Value>
    </element>
  </UserDefineVars>
  <property.EditorPath>设施/逃生门(310001)</property.EditorPath>
</FacilityTemplate>