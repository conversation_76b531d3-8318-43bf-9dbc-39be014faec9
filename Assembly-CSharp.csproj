﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{A2859A56-AB4A-51F9-60BB-1B6FC96E8C70}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2021_3_15;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;USE_SEARCH_ENGINE_API;USE_SEARCH_TABLE;USE_SEARCH_MODULE;USE_PROPERTY_DATABASE;USE_SEARCH_EXTENSION_API;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;SKY_PROTOBUF;SKY_JSON_DOT_NET;SKY_DEBUG;CROSS_PLATFORM_INPUT;UNITY_POST_PROCESSING_STACK_V1;AMPLIFY_SHADER_EDITOR;ODIN_INSPECTOR;ODIN_INSPECTOR_3;TextMeshPro;ODIN_INSPECTOR_3_1;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;NO_PACKAGE;DISABLE_UWA_SDK;ACTK_WALLHACK_LINK_XML;FLORA_PRESENT;GAIA_MESH_PRESENT;UPPipeline;BCG_RCCP;BCG_NEWINPUTSYSTEM;UNITY_POST_PROCESSING_STACK_V2;NWH_DWP2;RCCP_PHOTON;THIRD_PERSON_CONTROLLER;ULTIMATE_CHARACTER_CONTROLLER_UNIVERSALRP;TEXTMESH_PRO_PRESENT;GAIA_2_PRESENT;GAIA_PRO_PRESENT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.16</UnityProjectGeneratorVersion>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2021.3.15f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Assets\SDK\LSHDotNet\MinHasher.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiGrid.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Data\ThermoSector.cs" />
    <Compile Include="Assets\SDK\Heap\BaseHeap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\AIMonsterBase.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowControlSingleton.cs" />
    <Compile Include="Assets\XLua\Src\LuaDLL.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Constructor\F3DTurretConstructor.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DRect.cs" />
    <Compile Include="Assets\Script\Effect\Trails\SmokePlume.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizationTrigger.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySignalTower.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\YilongRotate.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Closet\UIItemClosetWeapon.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYLocalizationImage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityHomeAttackedAlarm.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowWidget.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\Utility\MathHelper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Contracts\MapGuideInfo.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf8\Utf8ValueStringBuilder.CreateFormatter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BattleTask\UIWindowBattleTask.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\InitOnStart.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\BonePose.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\UIBaseItemPool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonSMBIdle.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\UILineRenderer.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BackHome\UIWindowBackHome.cs" />
    <Compile Include="Assets\Poco\Config.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Effects\DayorNight.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbLadderDown.cs" />
    <Compile Include="Assets\Script\UI2\Task\UITaskWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapSceneUI.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\ISceneObject.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityMaterialPoint.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateOffline.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Helpers\BehaviourToggle.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningPlayerMono.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterFlashing.cs" />
    <Compile Include="Assets\Script\UI2\RechargeSevenDay\UIRechargeSevenDayWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tasks\Effect\PlayParticleClipTask.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Const\Enums.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UICommonButton.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UltimateTalentPageItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Save.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Interface\ITaskCollection.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Report\UIReportWnd.cs" />
    <Compile Include="Assets\Script\Message\Handler\battleHandler.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\PrivateValueAccessor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideComputePentration.cs" />
    <Compile Include="Assets\Script\UI2\Sponsor\UIWndSponsorPreview.cs" />
    <Compile Include="Assets\Script\Data\playerdata\PlayerData.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UICombatModeHelpArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleManagerInspector.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIItemPassAward.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableUtil.cs" />
    <Compile Include="Assets\SDK\Sky\Sound\SoundPlayer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityWeaponDesk.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Action\BindObject.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\DropDownListButton.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenEvent.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBData\FZBFloat.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Gearbox.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySysVending.cs" />
    <Compile Include="Assets\Script\Message\Handler\TrapHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugNewMove.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DDummyEvent.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\SGCAssetInterface.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\SplitMesh\Triangulator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMover.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityRonglu.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Assets\Group.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySleepBag.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityEquipLathe.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetProtocol.cs" />
    <Compile Include="Assets\Script\Model\PlayerActorInfo.cs" />
    <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\Loader\CustomLoader.cs" />
    <Compile Include="Assets\Script\Thermodynamic\ThermoSlider.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillConeDamage.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\LoopScrollRect.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Exhaust.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZConst.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWGBind.cs" />
    <Compile Include="Assets\Script\Message\Handler\ReviveHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideEffectManager.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Unity\TextMeshProExtensions.SetStringBuilder.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWGItemAttr2.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BrokenFacility\UIWndBrokenFacilty.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateThirdPerson.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiButton.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\IResettableBufferWriter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIWeaponBoxList.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Nos.cs" />
    <Compile Include="Assets\Script\Message\Handler\AirDropHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIAnimationLua.cs" />
    <Compile Include="Assets\Script\Data\playerdata\HallBagData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIItemLingDiGuiBoxGroup.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildHelp.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_SpeedLimiter.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateControl.cs" />
    <Compile Include="Assets\Setting\DepthNormalsFeature.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiConsole.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\Edge.cs" />
    <Compile Include="Assets\Script\UI2\Friend\UIFriendAddItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\PSkillThrow.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\TerritoryLevel\UIWndTerritoryPutDetail.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiActionBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureHeadShow.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordKeyRead.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkillLua.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SignalTower\UIWindowSignalTowerInstruction.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\SliderHPImage.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CarController.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIItemShopSmallItem.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Action\BindActionObject.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIWindowShop.cs" />
    <Compile Include="Assets\Script\Data\playerdata\LingDiguiData.cs" />
    <Compile Include="Assets\Script\Model\Control\InputKeyDefine.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Version.cs" />
    <Compile Include="Assets\Poco\PocoManager.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\GenerateHeightMap.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CharacterCombine.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkillHitData.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DMatrix4x4.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Basic\CSHighlighterController.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\MapIconManager.cs" />
    <Compile Include="Assets\Script\Model\Component\SurvivalFootPlacement.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillPositionEffect.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Animation\AnimatorTest.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Spoiler.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBData\FZBInt.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\TouchPadEvent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIConfine\AIConfineHp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\TaskStatus.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowBoxPackage.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildMemInfo.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SettlementData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Resource.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityElectricLamp.cs" />
    <Compile Include="Assets\Script\UI2\Notice\UIWindowScrollNotice.cs" />
    <Compile Include="Assets\Script\Data\Table\TblAutoEnum.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Common\BattleParam.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterInteractive.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DTurnTable.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\GuidEx.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIManage.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Pair.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\AutoRifleTurret\UIItemAutoRifleTurretGameBagGroup.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\FormattingHelpers.cs" />
    <Compile Include="Assets\Poco\TcpClientState.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbLadderUp.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDrink.cs" />
    <Compile Include="Assets\XLua\Src\MethodWarpsCache.cs" />
    <Compile Include="Assets\Script\Message\Handler\MailHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Car\FgCar.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowAction.cs" />
    <Compile Include="Assets\Script\UI2\HuntMode\UIWndHuntMode.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComMsg.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIRechargeContrl.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiScrollView.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityChandelier.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Treasure.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\DefDataClothesOrSkin.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UWA\UWAEffectListPlay.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\FormatParser.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_Generic_List_1_System_Int32_Wrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MountHunting.cs" />
    <Compile Include="Assets\XLua\Gen\AICommonBlendTreeWrap.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetHead.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.6.- Slide\Scripts\SlidingCharacter.cs" />
    <Compile Include="Assets\Script\Model\Character.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Collection\TimelineTaskCollection.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\PerformanceTracker.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityDoorControl.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_DemoMaterials.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\SceneLoader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\RankIntegral.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Express\ExpressAnalyze.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UIRedemptionCodeWnd.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Bind\BindValue.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityRideElectricalGenerator.cs" />
    <Compile Include="Assets\Script\Text\TextWordSpacing.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\PresetTable.cs" />
    <Compile Include="Assets\Script\Model\Weapon\BulletMove\BulletMove.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordMap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityLingdigui.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\GrassInstancing.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Pool\PoolGroup.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\ModelImportInfo.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBData\FZBConvert.cs" />
    <Compile Include="Assets\RawData\Effect\Delay.cs" />
    <Compile Include="Assets\Script\Message\Handler\AutoRifleTurretHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\Tracker\Monster\MonsterTrakcer.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Assets\Track.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FreeCameraController.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Bookcase\UIBookcase.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ColliderRandPos.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\SupplyGroupObject.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\NGUI_LanguagePopup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\ScriptableObject\SPCRJointDynamicsColliderData.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\ModelTable.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetProtocolHandle.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeShop\UIExchangeWnd.cs" />
    <Compile Include="Assets\XLua\Gen\AIMonsterLuaWrap.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Telemetry.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GrassEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\UIEventTriggerListener.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\MegaSplat Integration\EnviroMegaSplatIntegration.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityElectricDoor.cs" />
    <Compile Include="Assets\Script\Model\Action\ActionSkillFire.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordRow.cs" />
    <Compile Include="Assets\Script\Message\SystemMessageName.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\SPCRJointDynamicsExport.cs" />
    <Compile Include="Assets\Script\Message\Handler\FundRebateHandler.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoResPage.cs" />
    <Compile Include="Assets\Script\UI2\HuntingQTE\UIWindowHuntingQTE.cs" />
    <Compile Include="Assets\Script\Model\ScriptableObject\PlayerConfig.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIItemBoxPackage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleModeFeature\BattleModeConfig.cs" />
    <Compile Include="Assets\Script\App\PatchVesion.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\GPUInstancingRender.cs" />
    <Compile Include="Assets\Script\UI2\DrawCard\UIWndDrawRewardPreview.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\EdgeHit.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UltimateTalentGroupItem.cs" />
    <Compile Include="Assets\Script\UI2\Team\UIWndTeamInvite.cs" />
    <Compile Include="Assets\Script\Model\TriggerObj.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\FieldTypes.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\BindPair.cs" />
    <Compile Include="Assets\Script\UI2\Task\UIDayItem.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UITurntableWnd.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoToggleItem.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB2_TextureBakeResults.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWindowPassport.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector3Wrap.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Input.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\ProtocolTemplate.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\ToggleLanguage.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Matrix2.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningShaderType.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ScrollBehaviour.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DCircle.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\TreeGizmos.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideTrigger.cs" />
    <Compile Include="Assets\Script\UI2\Common\FightComTipsWnd.cs" />
    <Compile Include="Assets\SDK\Sky\Util\BitTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AILaser.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\FPSCounter.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\JsonUtilityArrays.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\RTConvexTowerOccluder.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ViewPlayerData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\AIMonster.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Drop\TableDrop.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapDici.cs" />
    <Compile Include="Assets\Script\Message\Handler\AchieveHandler.cs" />
    <Compile Include="Assets\Script\UI2\Technology\UIWindowTechnology.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Attempt.cs" />
    <Compile Include="Assets\SDK\Sky\Net\UDP\NetUdpClient.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\Tracker\Base\TrackerBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\SinglePolygonData.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\Zlib.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIPvpIcon.cs" />
    <Compile Include="Assets\Script\Message\Handler\SleepBagHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_SetBehaviorType.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\Attr\RPGAttrField.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningBone.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DRandomize.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\RainSpwaner.cs" />
    <Compile Include="Assets\SDK\FG\net\monitorprotocal.cs" />
    <Compile Include="Assets\Script\Message\Handler\ForgeHandler.cs" />
    <Compile Include="Assets\Script\Data\playerdata\RankData.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BattleTaskData.cs" />
    <Compile Include="Assets\Script\UI2\ConsumerRank\UIWndConsumerRankPoster.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapElevator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Transform\MoveTo.cs" />
    <Compile Include="Assets\Script\Message\Handler\PassportHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\NPC\NPCTrigger.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ExceptionUtil.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroWeatherPrefab.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\RepairDesk\UIRepairDeskGroup.cs" />
    <Compile Include="Assets\Script\UI2\OutsideAchieve\UIWndAchieve.cs" />
    <Compile Include="Assets\Poco\Utils\UWASDKAgent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Player\PlayerManager.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\PersonalRating\UIWindowPersonalRatingWeekly.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUIWindowProtoCount.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIWindowSettlement.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AeroDynamics.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Extensions.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiForm.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineRecordWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ObjectNameTag.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Animation\MouseEvent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYBetterList.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordManage.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MeshBakerGrouper.cs" />
    <Compile Include="Assets\XLua\Gen\XLuaGenAutoRegister.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UICombatModeSetting.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizationManager.cs" />
    <Compile Include="Assets\Script\UI2\Team\TeammateSign.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Template\StreamExporterPath.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureType.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWndBuySafeBox.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\SurfaceIdentity.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIWndEquipLathe.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureBase.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\PSkillPosEffect.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWndRewardPreview.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DrawMergedBounds.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapMovePlatform.cs" />
    <Compile Include="Assets\Script\Message\Handler\SignInHandler.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIPlayerInfo.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.9.- Slope Speed Modifier\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BattleTask\UIWindowBattleTaskDetail.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Helpers\GameObjectToggle.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestTTKInfoManager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Damage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimatePageUnlock.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiManage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralBrokenObject.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonReader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMount.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapStageMovePlatform.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DOBB.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DRay2D.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf8\Utf8ValueStringBuilder.AppendFormat.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionSpatialHash.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\PreparedFormat.cs" />
    <Compile Include="Assets\Script\UI2\SelectMode\UIWndSelectModeExplain.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\OfflineMsg\UIItemOfflineMsgMaintainGroup.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\RectUtility.cs" />
    <Compile Include="Assets\Script\Common\CommonFunctions.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UISliderTextReader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.LandCab.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics2D.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_UVTransform.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineRecordItem.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeShop\UIExchangeShopItem.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\ConvexHullAlgorithm.Initialize.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Stronghold\StrongholdArea.cs" />
    <Compile Include="Assets\Script\UI2\Mail\UIMailNew.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordRowDB.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_AtlasPackerRenderTexture.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tasks\Camera\CameraControlClipTask.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIItemManuAttr.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Bomb\C4Object.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\ZStream.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Property.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\WaterFall.cs" />
    <Compile Include="Assets\SDK\Sky\Util\PageView.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Transform\RotateTo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\DropDownListItem.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbThrough.cs" />
    <Compile Include="Assets\Script\Message\Handler\ExchangeShopHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UINetworkButton.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\ListAgent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityBookcase.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\RainFeature.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\KeyBind\KeyBindEvent.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIBatteryInfo.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Platform\ImeTool.cs" />
    <Compile Include="Assets\Script\UI2\NewbieChallenge\UIWndNewbieChallenge.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\DecimalEx.cs" />
    <Compile Include="Assets\Script\Effect\Meteorite\MeteoriteManager.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Scripts\RuntimeMeshSimplifier.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\PersonalRating\UIWindowPersonalRating.cs" />
    <Compile Include="Assets\Script\Message\Handler\DropPackageHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\IDetector.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\AnimationPlay.cs" />
    <Compile Include="Assets\SDK\FG\service\servertimeservice.cs" />
    <Compile Include="Assets\Script\Occlusion\Occlusion.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ForgeData.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\Billboard.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\ArrayTools.cs" />
    <Compile Include="Assets\Script\Model\Control\LockSceneCamera.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\RainConeController.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoRuleItem.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateFreelook.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Debug\DebugBundleInfo.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Medicine\UIWndMedicine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Inspector_SelfRotation.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionItem.cs" />
    <Compile Include="Assets\Script\Data\playerdata\GameShopData.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DShotgun.cs" />
    <Compile Include="Assets\Script\UI2\CommunityDiversion\UIWindowCommunityDiversion.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DNebula.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonMapper.cs" />
    <Compile Include="Assets\VolumetricFog\Scripts\VolumetricFogFoW.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIWindowCart.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\ChangeAvatarWnd.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BoxLaunchBehaviour.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.NumberBuffer.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\ShowUGUIRaycastTargetRect.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Output.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureAnimator.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\NPC\UINPCDialogWnd.cs" />
    <Compile Include="Assets\Script\UI2\Friend\UIFriendItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\DataStructures\GuideOctree.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Visual_Dashboard.cs" />
    <Compile Include="Assets\Script\Data\playerdata\QuestData.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIWindowPanelSaveCollocation.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBMgr.cs" />
    <Compile Include="Assets\Script\Token\Survival\StatePlaceBomb.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\StreamLoader.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\ConvexFace.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\UIItemTreasureArea.cs" />
    <Compile Include="Assets\Script\App\Entry.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\InputUtils.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Shared Assets\Scripts\AutoSimulationEnabler.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonWriter.cs" />
    <Compile Include="Assets\Script\Message\Handler\MedicineHandler.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\VRHelper.cs" />
    <Compile Include="Assets\Script\UI2\IntenseMode\UIWndIntenseModeTeamReward.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\Occludee.cs" />
    <Compile Include="Assets\Script\Model\Control\NoiseApplier.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.Formatting.cs" />
    <Compile Include="Assets\Script\Effect\Trails\TrailRenderer_Base.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateLeanLeft.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\MonsterAimAssistParam.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AirDrop\AirDropObject.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildActiveBoxPreview.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SignalTower\UIWindowSignalTower.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CameraRenderTexture.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateOperatingFacility.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\CustomTagEditor.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Utillities\Range.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Utils\ThermoUtils.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SelectModeData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\LocalizationBundlesManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TrainingResUI.cs" />
    <Compile Include="Assets\Script\Common\CustomPoolManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BattleMail\UiWindowBattleMail.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\SceneFashion.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWndUseItem.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UIWndTimeDActAll.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Temperature\UIWndTempResistLevel.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\COMMON\VersionConfig.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureApiConst.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\Base\ForbidBuildArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\CreateWarningPositionEffect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateBrainDescription.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\DropUtils.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\InterfaceLayout\Panel_Bag_Fight_ShortcutLayout.cs" />
    <Compile Include="Assets\Script\UI2\Common\GoldNum.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\TransformUtils.cs" />
    <Compile Include="Assets\Script\Data\AbstractData.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BatteryMakerData.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\SupportClass.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Config.cs" />
    <Compile Include="Assets\Poco\Dumper\UGUINode\UnityNodeGrabber.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\SGCAssetLoader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EvacuateBox\EvacuateBoxManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugAIMonsterMove.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Game Objects\ShatterOnCollision.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonWriter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineBagGroup.cs" />
    <Compile Include="Assets\Script\UI2\DailyRecharge\UIWndDailyRecharge.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DCapsule.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_HoodCamera.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticView\StaticView.cs" />
    <Compile Include="Assets\Script\Message\Handler\ChatHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Sound\SoundEngineRandAudio.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\VirtualTerrainEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\DataStructures\GuideKDTree.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleModeFeature\BattleModeFeature.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\WeldTerrains.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\LRUStruct.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UltimateTalentBrainItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Decomposer\UIWndDecomposer.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_MeshCombinerSimple.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\Script\Model\AttrValue.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\OnScreenCustomButton.cs" />
    <Compile Include="Assets\Script\Message\Handler\TreasureAreaHandler.cs" />
    <Compile Include="Assets\Script\Model\ScriptableObject\PlayerStateConfig.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Attack.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Event\TriggerLog.cs" />
    <Compile Include="Assets\Script\Data\playerdata\RepairDeskData.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceCenter.cs" />
    <Compile Include="Assets\Script\Message\Handler\InstructionHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Forge\UIWndForge.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenFuncDefault.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildMain.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Loaders\Loader.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CombineSkinnedMgr.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\FireFly\FireFlyObject.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SleepBag\UISleepBagWnd.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizationLoadout.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIWindowHallBag.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Bind\BindValueArray.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBDataTypeUtil.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Temperature\UIWndTemperature.cs" />
    <Compile Include="Assets\Script\Model\WeaponAnimDatabase.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Debug\DebugAssetInfo.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\PersonalInfoWnd.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\ShatterTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapDoor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\NPC\NPCInfo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZDataMonoBehaviour.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Customization.cs" />
    <Compile Include="Assets\SDK\Sky\Db\Config.cs" />
    <Compile Include="Assets\Script\Message\Handler\OfflineMsgHandler.cs" />
    <Compile Include="Assets\Script\Model\CheckSqueezedTrigger.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiComponent.cs" />
    <Compile Include="Assets\XLua\Gen\AIInterfaceWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\SPCRJointDynamicsManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIObstacle\AIObstacleConfig.cs" />
    <Compile Include="Assets\XLua\Gen\DelegatesGensBridge.cs" />
    <Compile Include="Assets\Poco\Utils\LogUtil.cs" />
    <Compile Include="Assets\Script\UI2\SelectMode\UIWindowSelectMode.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Limiter.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroSurfaceAnimator.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEquipAttrItem.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Jump.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Socket.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingHolder.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Singleton.cs" />
    <Compile Include="Assets\post\FastBloom.cs" />
    <Compile Include="Assets\XLua\Src\LuaException.cs" />
    <Compile Include="Assets\Script\Message\Handler\PersonalHandler.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\CommonGMProWnd.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\BitOperations.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\UIWndQuickGM.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\8.- Custom Simulation\Scripts\SimulatablePlatform.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowControlView.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIWindowLingDiGui.cs" />
    <Compile Include="Assets\Script\Occlusion\UnmanagedArrayHelper.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\InfTree.cs" />
    <Compile Include="Assets\Script\UI2\NoviceGiftPack\UIWndNovicePackBuy.cs" />
    <Compile Include="Assets\Script\UI\UIRoot.cs" />
    <Compile Include="Assets\Script\Message\Handler\BujiBoxHandler.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\Debug.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\loadscene.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Rank\UiWindowRank.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\CollectionCircleArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\IFacilityWnd.cs" />
    <Compile Include="Assets\Script\Model\CheckDoorTrigger.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SignInData.cs" />
    <Compile Include="Assets\Script\UI2\FirstRecharge\UIWndRechargeRule.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\NPC\NPCManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\PterosaurTransit\UIWndPterosaurTransit.cs" />
    <Compile Include="Assets\Script\Message\Handler\BrokenFacilityHandler.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\MathHelper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMonsterConfig.cs" />
    <Compile Include="Assets\Script\Effect\RFX4_Turbulence.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIMaterialPointObj.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateVertigo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityParts.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation\DelaunayTrianglationInternal.cs" />
    <Compile Include="Assets\Script\UI2\Utils\Lang.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDie.cs" />
    <Compile Include="Assets\Script\UI2\SignIn\UISignInWnd.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComWating.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Supply.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiActionSize.cs" />
    <Compile Include="Assets\Script\Model\Weapon\AimAt.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\Netstandard15Polyfill.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Demo\Scripts\ApplyLandingForce.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDrop.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\Tree.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Hunting\HuntingRopePoint.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DWarpTunnel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralTreeObject.cs" />
    <Compile Include="Assets\Script\Model\AIPlayerActionManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityLearnMachine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\DeviceResObject.cs" />
    <Compile Include="Assets\FORGE3D\PoolManager\F3DPoolManager.cs" />
    <Compile Include="Assets\Script\UI2\Login\UIWindowLogin.cs" />
    <Compile Include="Assets\Script\App\HotPlatformAdapter.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Demo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\StickGroundMove.cs" />
    <Compile Include="Assets\Script\Message\Handler\RepairDeskHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Revive\DontBuildArea.cs" />
    <Compile Include="Assets\Script\Data\playerdata\PersonalInfoData.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Effects\EnviroEffects.cs" />
    <Compile Include="Assets\Script\Tools\LodTool.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIWindowBuyConfirm.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\4.- Platforms\4.3.- One-Way Platform\Scripts\OneWayPlatform.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MaterialPoint\MaterialPointManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\BezierPath.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIConfine\AIConfine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\UIPrimitiveBase.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UISettingsPrivacyWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityClearing.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentPreview.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.4.- Bouncer\Scripts\Bouncer.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoMonsterPage.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowChipRefine.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoSectorPage.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIChatIcon.cs" />
    <Compile Include="Assets\SDK\LSHDotNet\SimilarityMeasures.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityObject.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Control\SkillButton\SkillButton.cs" />
    <Compile Include="Assets\SDK\Sky\Util\GameObjectUtils.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDrive.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIDiamondChange.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Interfaces\IData.cs" />
    <Compile Include="Assets\XLua\Src\InternalGlobals.cs" />
    <Compile Include="Assets\Script\Occlusion\UnmanagedArray.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapMove.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\FashionResUI.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_GroundMaterials.cs" />
    <Compile Include="Assets\XLua\Gen\AITranDataBaseWrap.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSKinningCullingMode.cs" />
    <Compile Include="Assets\Script\UI2\Evacuate\UIWndExract.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIWndGunLineRule.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIAttackRange.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiContainer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GrassManagerCS.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Lightmap\LightmapStreamLoader.cs" />
    <Compile Include="Assets\Script\Data\Table\TblEnum.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiPage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\MonsterBornAreaConfig.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\DynamicManager.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\UGUIJoystick.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Player\F3DPlayerTurretController.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\QueryClosest.cs" />
    <Compile Include="Assets\Script\Model\State\StateDefine.cs" />
    <Compile Include="Assets\Script\Data\playerdata\FundRebateData.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\TerrainDeform.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\HallGrassHandle.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\NPC\NPCArea.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\OfflineMsg\UIItemOfflineMsgInfoGroup.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CarSelectionExample.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenTexture.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonException.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySupply.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\ITree.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Parts\UIWindowParts.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateFistRight.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\SceneDetectorBase.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\KDQueryNode.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Bomb\BombUtils.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\FloatEx.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\ContextUnity.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDying.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\HomeAttackedAlarm\UIHomeAttackedAlarm.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MedalPointRank\UIWndMedalPointRank.cs" />
    <Compile Include="Assets\XLua\Gen\SurvivalPlayerCharacterLuaWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\TransferFacility.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\Example_LocalizedString.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\FastNumberWriter.cs" />
    <Compile Include="Assets\Script\App\InputShield.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiScreen.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEquipSelectWnd.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenValueFloat.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIItemCommonTab.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\InterfaceLayout\LittleMapSmallLayout.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateHelp.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWindowBuyExp.cs" />
    <Compile Include="Assets\Script\SenceSplit\SecenSplitJSON.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\Tracker\Base\ITracker.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIWindowPropNumSelect.cs" />
    <Compile Include="Assets\Script\Model\Control\PlayerMovement.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Random.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Quest\UIFightQuestAndTeam.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateBeBumped.cs" />
    <Compile Include="Assets\Script\Tools\EditorMonoTool\DrawTargetPoints.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Bind\UIBindingInfo.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiCheckbox.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf16ValueStringBuilder.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\FacilityUpGrade\UIFacilityUpGradeInfo.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroVegetationInstance.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Interface\IAction.cs" />
    <Compile Include="Assets\Script\UI2\Task\UITaskItem.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWndPlayVideo.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Utility\EnviroSetSystemTime.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\4.- Platforms\4.1.- Scripted Platform\Scripts\KinematicMove.cs" />
    <Compile Include="Assets\XLua\Gen\EffectEntityWrap.cs" />
    <Compile Include="Assets\Script\UI\InputNavigator.cs" />
    <Compile Include="Assets\Script\Message\Handler\GuildHandler.cs" />
    <Compile Include="Assets\XLua\Gen\System_Collections_IEnumeratorBridge.cs" />
    <Compile Include="Assets\Script\Message\Handler\CookingHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Util\TextHyperlink.cs" />
    <Compile Include="Assets\Script\Tools\LookAtCamera.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.3.- PlayerInput Character Controller\Scripts\MyCharacterController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\CollectionResObject.cs" />
    <Compile Include="Assets\SDK\Sky\CodeNameRule.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\SceneRuntimeExample\MB_ExampleSkinnedMeshDescription.cs" />
    <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\ByFile\ByFile.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetMovePlatform.cs" />
    <Compile Include="Assets\Script\Effect\CamGrayEffect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Export\ExportHandle.cs" />
    <Compile Include="Assets\SDK\KDTree\FPSTreeDebug.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Drop\DropList.cs" />
    <Compile Include="Assets\XLua\Gen\AIMonsterBlendTreeWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Export\AIHiddenPoints.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\HexConverter.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\LoopScrollPrefabSource.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_OverrideVehicleExample.cs" />
    <Compile Include="Assets\Script\Thermodynamic\ThermoSign.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningAnimation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\SceneObject.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasurePoint\TreasurePointConfig.cs" />
    <Compile Include="Assets\Script\Message\Handler\InteractiveHandler.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWindowItemNumSelect.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\8.- Custom Simulation\Scripts\SimulationManager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Recorder.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateHipShot.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIUseCreditVoucherWnd.cs" />
    <Compile Include="Assets\Script\Model\RemotePlayer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasurePoint\TreasurePointEditor.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\FightWidget\UIWGSelectOneDropPackage.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Joint.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\GridBounds.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityAutoRifleTurret.cs" />
    <Compile Include="Assets\SDK\Sky\Db\SqliteDB.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenScriptFunc.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Contracts\IMapGuide.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Decorator\GuiFollow.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetObject.cs" />
    <Compile Include="Assets\Script\UI2\Mail\UIMailItem.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIGiftSelectShow.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\UIDialogAgent.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TentData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\PVPState\UIWindowPVPState.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoThreeLevelPage.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceThirdEngine.cs" />
    <Compile Include="Assets\Script\Occlusion\BoundsOctree.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonSMBAttack.cs" />
    <Compile Include="Assets\XLua\Src\Utils.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\MeshCombine\CombineChildren.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ChargingTableData.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UIFeedbackWnd.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ChargingTable\UIWndChargingTable.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroSurfaceAnimatorMultiple.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Cooking\UICookingSystemWnd.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DCurvedBeam.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\EasyObjectPool\EasyObjectPool.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\MathUtil\MathData\Triangle.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMonsterHP.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetMagicaRenderDeformer.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\AttributesImage.cs" />
    <Compile Include="Assets\XLua\Gen\AIConfineWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIPartFollowLua.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiItem.cs" />
    <Compile Include="Assets\Script\Data\playerdata\MapData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticView\StaticViewManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\StabilityPoint.cs" />
    <Compile Include="Assets\SDK\KDTree\KDBindData.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoPage.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWGItemAttr.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\SPCRJointDynamicCreateBone.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.Dragon4.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\GridViewControllerGrass.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBoom.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowDropPackage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentDescription.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIItemShopCart.cs" />
    <Compile Include="Assets\Script\UI2\Technology\UIWindowLearnMachine.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\Attr\RPGAttrParse.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySocket.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_ColorWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetClothParam.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Action\BindActionBool.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\FacilityUpGrade\UIFacilityUpGrade.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingSockets.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowResrouceInfo.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\FreeViewEvent.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf8\Utf8ValueStringBuilder.SpanFormattableAppend.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BoxOpeningBehaviour.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Loaders\TextureLoader.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_TruckTrailer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\MLockLookCamera.cs" />
    <Compile Include="Assets\SDK\Sky\Util\EventListener.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiEdit.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIItemDropPackage.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DWarpJump.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\SkillBehaviour\AIBehaviourControlTriggerEvent.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Data\ThermoRuleMono.cs" />
    <Compile Include="Assets\Script\Message\Handler\GuideTaskHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonSMBRun.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Decomposer\DecomposerItem.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\PlayerDisplayChangeSkin.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonMapper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\Utils\BoundsEx.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\NTask.cs" />
    <Compile Include="Assets\AQUAS 2020\ReflectionWater\Reflection.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterToggle.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUIItemDropItem.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Parameter.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\ZInputStream.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_SetMobileController.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComHintWnd.cs" />
    <Compile Include="Assets\Script\UI2\SelectMode\UIItemRoomItem.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateOneAction.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\IAIEntity.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GPUInstancedManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityWeaponBox.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Waypoint.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Hunting\UIWindowHunting.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\NPCDisplay.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Freeview\FreeviewEye.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenAni.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\3.- Events\3.1.- Character Events\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.2.- Target Lock\Scripts\TargetLockCharacter.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf16\Utf16ValueStringBuilder.SpanFormattableAppend.cs" />
    <Compile Include="Assets\Script\UI2\Common\NowTryFashionShow.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasureEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_MobileInputs.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HLodDataList.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Assets\Asset.cs" />
    <Compile Include="Assets\Script\Model\Weapon\MeleeWeapon.cs" />
    <Compile Include="Assets\AQUAS 2020\Scripts\WaterControl.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UITeamingHallItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityMine.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_APITest.cs" />
    <Compile Include="Assets\Script\Message\Handler\HuntHandler.cs" />
    <Compile Include="Assets\Script\RenderFeature\StylizedRenderingRenderFeature.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIDamageLIst.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.7.- Fly\Scripts\MyFirstPersonCharacter.cs" />
    <Compile Include="Assets\Script\UI2\ConsumerRank\UIWndConsumerRank.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureResult.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\BuildableGridVisualizer.cs" />
    <Compile Include="Assets\Script\Text\TextUtils.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Axle.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonData.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Base\BasePlayerDisplayController.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Interfaces\IDirector.cs" />
    <Compile Include="Assets\Script\Token\CheckSkill.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Color.cs" />
    <Compile Include="Assets\Script\UI2\WarReserve\UIWndSkinEquip.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\OrdnanceProcess\UIItemReformGameBagGroup.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBData\FZBVector3.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapSwitch.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Scripts\Simplifier.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_TextureBaker.cs" />
    <Compile Include="Assets\Script\App\UrpCameraSwitch.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Camera.cs" />
    <Compile Include="Assets\UIParticles\Scripts\SetPropertyUtility.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleHighLightManager.cs" />
    <Compile Include="Assets\Script\UI2\Maincity\UIMaincity.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\Deflate.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Quest\UIWndQuest.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbThroughEdge.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Effects\EnviroLightShafts.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\RegisterBundlesManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\OfflineMsg\UIWindowOfflineMsg.cs" />
    <Compile Include="Assets\Poco\Utils\ObjectPools\ListPool.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Data\ThermoRule.cs" />
    <Compile Include="Assets\Script\UI2\WarReserve\UIWndSkinLevleUp.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroTerrainVegetation.cs" />
    <Compile Include="Assets\Script\Data\playerdata\GuildData.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecord.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiImage.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Transformer\UIWindowTransformerDamageRank.cs" />
    <Compile Include="Assets\FORGE3D\PoolManager\F3DPoolManagerDB.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\SplitMesh\ShatterCalMesh.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BoxCameraAnimationBehaviour.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DFlameThrower.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MultiMeshBaker.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Lightmap\LightmapManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityVendingMachine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetMagicaVirtualDeformer.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\UIDepth.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Maps\BigMapGuide.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\PathCurve.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUIWindowException.cs" />
    <Compile Include="Assets\Script\App\GameSetting.cs" />
    <Compile Include="Assets\Script\Message\Handler\loginhandler.cs" />
    <Compile Include="Assets\Script\Effect\Trails\SmoothTrail.cs" />
    <Compile Include="Assets\Script\Message\Handler\MapHandler.cs" />
    <Compile Include="Assets\Script\Common\CustomObjectPool.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenOrderList.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateLeanRight.cs" />
    <Compile Include="Assets\Script\App\UniSDK\SDKManager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UIManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIDamageListArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Door\DoorResUI.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Decorator\GuiFlash.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateAim.cs" />
    <Compile Include="Assets\Script\Model\Weapon\BulletMove\BulletMovePaoWuXian.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AILaserTrack.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Upgrade.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceGMEEngine.LifeCycle.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiActionManage.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateUseBandage.cs" />
    <Compile Include="Assets\Script\SenceSplit\TerrainSplitTo4.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\2.- Animation\2.1.- Character Animator\Scripts\UnityCharacterAnimator.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Triangulator\ITriangulator.cs" />
    <Compile Include="Assets\Script\Model\DummyBox.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWindowPassPortLevelUp.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Game Objects\HierarchyHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\AirDrop\UIWndAirDrop.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\ActivityInterface.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.Grisu3.cs" />
    <Compile Include="Assets\Script\Model\ParticleLateUpdate.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenUGUIPotion.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\EditorUtil.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleDecalManager.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Mouse\MouseForce.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureEffect.cs" />
    <Compile Include="Assets\Script\UI2\Common\UITabScrollContent.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\GList.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIItemHallBagCollocation.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_ChangableWheels.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Closet\UIWindowCloset.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetMove.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Axles.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Collection\ParallelTaskCollection.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UICanvas.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUIWindowGameLogicTest.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkillStartData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\SteamLoaderTest.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector4Wrap.cs" />
    <Compile Include="Assets\Script\Message\Handler\NearbyBroadcastHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralTreeManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\UICircle.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningCycleList.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiDebug.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBApiData\FZBCharacterController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapObject.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\FacilityRename\UIFacilityRenameWnd.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UIController.cs" />
    <Compile Include="Assets\Script\UI2\InviteFriends\UIInviteFriendsWnd.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIEntryWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\Preset.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Extensions\StringExtensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AITriggerEventHitInfo.cs" />
    <Compile Include="Assets\SDK\Sky\Util\ConstDefine.cs" />
    <Compile Include="Assets\Script\UI2\Friend\UIFriendWnd.cs" />
    <Compile Include="Assets\Script\Message\Handler\TestHandler.cs" />
    <Compile Include="Assets\Script\Message\Handler\SelectModeHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\System\RunTime.cs" />
    <Compile Include="Assets\Script\Model\Controller.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectCulling\EffectPointLight.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ConsumerRankData.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Siren.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HugeObjectFlag.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Talent\UIWndTalentExpDetail.cs" />
    <Compile Include="Assets\Script\Model\Character\IPlayerLuaCall.cs" />
    <Compile Include="Assets\Script\Message\Handler\DecomposerHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MaterialPoint\UIWindowMaterialPoint.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Platform\Win32Help.cs" />
    <Compile Include="Assets\Script\Model\ActorSide.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\ActionResourceManager.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\PlayerDisplay3DController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityDecomposer.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroAudioZone.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgRideEffectManager.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\BindArray.cs" />
    <Compile Include="Assets\Script\Data\playerdata\AdGainData.cs" />
    <Compile Include="Assets\PolyFew\demo\Scripts\PolygonReduction.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenBase.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\GListEffect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\Utils\PriorityQueue.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoSubTabItem.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\ModelSprite.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\CombiningInformation.cs" />
    <Compile Include="Assets\XLua\LuaManager.Activity.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\_testing\MB2_TestShowHide.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Parts\UIItemPartEquipItem.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\SplitMesh\ShatterMeshTool.cs" />
    <Compile Include="Assets\Script\Effect\Meteorite\Meteorite.cs" />
    <Compile Include="Assets\Poco\Utils\Singleton.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_WheelManager.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiSelectionGrid.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoAnimalPage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GenerateWaterSurface.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizationApplier.cs" />
    <Compile Include="Assets\SDK\FG\net\NetDecodeManager.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\SGCAssetObject.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComBagFullMailTips.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgRide.cs" />
    <Compile Include="Assets\Script\UI2\NoviceGiftPack\UIWndNovicePackExpired.cs" />
    <Compile Include="Assets\SDK\Sky\Sound\Sound.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\LittleMapSmall.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestTTKInfo.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\QueryKNearest.cs" />
    <Compile Include="Assets\XLua\Src\GenAttributes.cs" />
    <Compile Include="Assets\Script\UI2\Technology\UIWindowTechTip.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIVehicleBag.cs" />
    <Compile Include="Assets\Script\Message\Handler\NewbieChallengeHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIShouLei.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\TryClothesOrSkinControl.cs" />
    <Compile Include="Assets\Script\Message\Handler\CountdownHandler.cs" />
    <Compile Include="Assets\Script\Message\Handler\GameShopHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DFan.cs" />
    <Compile Include="Assets\Poco\Utils\ObjectPools\ArrPool.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\IdTable.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateDrillMining.cs" />
    <Compile Include="Assets\Script\App\WordFilter.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Utillities\GizmosExtra.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityManager.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Pool\PoolManager.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningPlayerMonoManager.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIItemSubscriptionPanel.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnimManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\StreamGridViewController.cs" />
    <Compile Include="Assets\Script\Data\playerdata\PartsData.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_DashboardButton.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Runner\FlushingOperation.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_WheelCollider.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Effects\EnviroFog.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\NativeLinkedListState.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIWndTeamRecruitment.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugAiMonsterAttackRange.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralResManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIChargeTriggerEvent.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Loaders\LoaderObj.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIFacilityWindowControl.cs" />
    <Compile Include="Assets\Script\Message\Handler\HallBagHandler.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\HandleControlsUtility.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\ZStreamException.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\WorkBench\UIWorkBench.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\MLockLookCamera.Eye.cs" />
    <Compile Include="Assets\Script\UI2\Notice\UIWindowNotice.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Radiation\RadiationManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIForgeResultWnd.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\YouMeConstDefine.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Characters\AgentCharacter.cs" />
    <Compile Include="Assets\SDK\Sky\Sound\SoundEventPlayer.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Shared Assets\Scripts\SimpleCameraController.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Thunderstorm\Sources\Scripts\THOR_SheetLightning.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterBase.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Revive\UIRevive.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_TextureCombiner.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\PolyFew.cs" />
    <Compile Include="Assets\SDK\Sky\Util\GameTimer.cs" />
    <Compile Include="Assets\DynamicBone\Scripts\DynamicBoneCollider.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillFollowPart.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiFormShowAni.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\Deque.cs" />
    <Compile Include="Assets\Script\UI2\Common\UILevelUp.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiLabel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\EnergyShield.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\KillStreakLeaderboard\UIItemKillStreakLeaderboard.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Effects\Warp Tunnel\Example\ClusterEmitter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\EntityEventHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\OrdnanceProcess\UIOrdnanceProcessWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMAnimal.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DropPackage\DropPackageManager.cs" />
    <Compile Include="Assets\SDK\Sky\Util\StringTool.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Stability.cs" />
    <Compile Include="Assets\Script\Model\Weapon\BulletMove\BulletMoveLine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentInfo.cs" />
    <Compile Include="Assets\Script\UI2\SelectMode\UIWndBattleJoinInfo.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroSeasonObjectSwitcher.cs" />
    <Compile Include="Assets\SDK\FG\net\procgameserverproto.cs" />
    <Compile Include="Assets\Script\UI2\Transition\UIWindowTransition.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\CreateHiZData.cs" />
    <Compile Include="Assets\Script\UI2\Task\UILevelTaskItem.cs" />
    <Compile Include="Assets\XLua\Gen\PackUnpack.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\GridViewController.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Transformer\UIWndDamageRankRule.cs" />
    <Compile Include="Assets\Script\UI2\Common\UINetworkStateInfo.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateOperatTurret.cs" />
    <Compile Include="Assets\Script\Message\Handler\UltimateTalentHandler.cs" />
    <Compile Include="Assets\Script\Model\AnimatorKey.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\HullType.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\AudioTrack.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\NetPackage.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ConditionPackage\UIWndConditionPackage.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEquipForgeItem.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateRideFire.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\Utils\MatrixEx.cs" />
    <Compile Include="Assets\SDK\Sky\Util\FileExtension.cs" />
    <Compile Include="Assets\XLua\Src\ObjectTranslator.cs" />
    <Compile Include="Assets\Script\Data\playerdata\WaterCollectData.cs" />
    <Compile Include="Assets\Script\Message\NetworkLatencyManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIAnnularExtendDamage.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\PlayerDisplay3DChangeSkin.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityInteractive.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIWndSettleRewardInstruction.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoClientModule.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityBatteryMaker.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonReader.cs" />
    <Compile Include="Assets\Script\Message\Handler\IntenseModeHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_RepairStation.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.4.- Old Input Examples\1.4.1 First Person\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\StaticTree.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SlotMachineData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\LOD\LODCalculationJob.cs" />
    <Compile Include="Assets\Script\Tools\UI3DPlaneSwitcher.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DQuaternion2D.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeHuntShop\UIWGExchangeHuntShop.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\SceneDetailLayerGrid.cs" />
    <Compile Include="Assets\StompyRobot\SROptions\SROptions.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\TeamPoint.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIItemHistoryMilitary.cs" />
    <Compile Include="Assets\SDK\LSHDotNet\LSHSearch.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Box\UIWindowBox.cs" />
    <Compile Include="Assets\Script\Effect\EffectEntity.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateEat.cs" />
    <Compile Include="Assets\Script\Message\Handler\AimSiteHandler.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\SerializableDictionary.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ElectricData.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\LoopHorizontalScrollRect.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\GridChild.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\TerrainDataSe.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UIWndChallengeActivity.cs" />
    <Compile Include="Assets\VolumetricFog\Scripts\VolumetricFogPosT.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MeshBakerRoot.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildApplyList.cs" />
    <Compile Include="Assets\VolumetricFog\Scripts\FogVolume.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Contracts\GuideUpdateType.cs" />
    <Compile Include="Assets\Script\UI2\Utils\Colors.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\InternalSpanEx.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TransferTo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\LinearSceneOcTree.cs" />
    <Compile Include="Assets\StompyRobot\SROptions\SROptions.Test.cs" />
    <Compile Include="Assets\Script\Model\InputSystemManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapTblDataMgr.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Lib\F3DMath.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ScrollText.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\RTSimpleOccluder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tasks\Transform\MoveToClipTask.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateRide.cs" />
    <Compile Include="Assets\SDK\Sky\Util\MiniJSON.cs" />
    <Compile Include="Assets\Script\Effect\XRayComposite.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureMove.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\AnimationShader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UltimateTalentItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityAndGate.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroSkyRendering.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.4.- Old Input Examples\1.4.2 Side Scroller\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBApiData\FZBApiParamBase.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowControl.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\DynamicBoneEditor.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Debug\RemoteCommand.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Demo\Scripts\DemoController.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_CopyBoneWeights.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\Triangle.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\_testing\MB2_TestUpdate.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateMeleeRight.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\LoopVerticalScrollRect.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUI.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleCommonManager.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIItemHallBagCommon.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\LegacyHull.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoServer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Quest.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\PoseRandomBehavior.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB2_Log.cs" />
    <Compile Include="Assets\SDK\Sky\Util\EncryptValue.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DVector4.cs" />
    <Compile Include="Assets\Poco\Utils\ObjectPools\ObjectPool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetMagicaCloth.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Cleaner\UIItemCleaner.cs" />
    <Compile Include="Assets\SGC\Unity\Utils\CSCallLuaUtils.cs" />
    <Compile Include="Assets\Poco\ConcurrentDictionary.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateTransitCountDown.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Attribute\SetPropertyAttribute.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\AnimationCurveData.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordManageTemplate.cs" />
    <Compile Include="Assets\Script\Message\Handler\BookcaseHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\PathMove.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DMissileLauncher.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityFloorLamp.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Maths.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityLadder.cs" />
    <Compile Include="Assets\Script\Token\StateToken.cs" />
    <Compile Include="Assets\Script\Data\playerdata\InstructionData.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\UIWndChangeName.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\StreamLoaderCustomData.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\ProtocolHandle.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowChipSynthesis.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceOperation.cs" />
    <Compile Include="Assets\Poco\Dumper\IDumper.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Tuple.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\UIPilotInstruction.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GrassRuntimeClass.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_WheelCamera.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\PersonalRating\UIWindowPersonalRatingLevelUp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBlendTree\AIAttackAbility.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Demo\Scripts\Floater.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\Base.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineWnd.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DVector2.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\HitArrow\HitArrow.cs" />
    <Compile Include="Assets\VacuumShaders\Terrain To Mesh\Example Scenes\Scripts\RunTime_Terrain_Convertion.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ElectricGeneratorData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillCatchPlayer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMonsterTame.cs" />
    <Compile Include="Assets\Script\UI2\Utils\AdapterCanvas.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\MathUtil\MathData\Vertex.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Helpers\RootMotionController.cs" />
    <Compile Include="Assets\Script\Model\Control\LerpToRoot.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Siren.cs" />
    <Compile Include="Assets\Script\Tools\EditorMonoTool\SenceExportPicture.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYUIFont.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIChatWnd.cs" />
    <Compile Include="Assets\XLua\Src\CopyByValue.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBApiData\FZBApiCheckDefine.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\SGCAssetBundle.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Runtime\SystemServices.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZStringWriter.cs" />
    <Compile Include="Assets\Script\Message\Handler\ConsumerRankHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Car\CarUseControl.cs" />
    <Compile Include="Assets\SDK\FG\net\netdatapool.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\EditorCoroutines.cs" />
    <Compile Include="Assets\Script\UI2\Bag\DefDataAttrList.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\ZOutputStream.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\Base\FacilityBuildHelper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySwitch.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_SkidmarksManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalent.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComMoneyLackWnd.cs" />
    <Compile Include="Assets\Script\Message\Handler\BagHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugAiMonsterMoveDrawLine.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\SpriteNineSliced.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\ZombieTideMonsterSlot.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\RaycastManager.cs" />
    <Compile Include="Assets\Script\UI2\ModSelect\UIWindowModSelect.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIMapIconItem.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Components\CharacterLook.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\MagicaClothEditor.cs" />
    <Compile Include="Assets\ErosionBrush\ErosionBrush.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_SirenManager.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DDespawn.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\PSkillMissile.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetAgent.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Audio.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ReadOnlyListAdaptor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureRender.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroSky.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MeshBaker.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\BindSingle.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Debug\RemoteDebuggerInRuntime.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Matrix.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Transformer\UIWindowTransformerIntegralReward.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AITriggerEvent.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MedalPointRank\UIWndTeamPointsDetails.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordTemplate.cs" />
    <Compile Include="Assets\XLua\Gen\AIAttackAbilityWrap.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingHelpers.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Other.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWndBossTowerConfirm.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\RPGApp.cs" />
    <Compile Include="Assets\Script\Message\Handler\StealBagHandler.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\InfCodes.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Worlds\GuideEffect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\CableCurve.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizationDemo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectCulling\EffectCullingManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityCloset.cs" />
    <Compile Include="Assets\StompyRobot\SROptions\SROptions.Attributes.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIItemLingDiGuiBagGroup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HLodTreeLoader.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BoxAniBehaviourBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugLookAtCamera.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\Lexer.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\DelayActive.cs" />
    <Compile Include="Assets\Script\Data\playerdata\AchieveData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureHuman.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\MathUtil\Triangulation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityLongChair.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIItemPassTask.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIRechargePanel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetPlatform.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIWndSlotMachine.cs" />
    <Compile Include="Assets\SDK\Heap\KSmallest.cs" />
    <Compile Include="Assets\Script\UI\AniSprite.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DropPackage\DropPackageObject.cs" />
    <Compile Include="Assets\Poco\Utils\ObjectPools\DicPool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureMoveControl.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SupplySpot\UIWindowSupplySpot.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYLocalizationData.cs" />
    <Compile Include="Assets\Script\Model\Control\TransformNoise.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\LittleMapLuopan.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\Attr\RPGAttr.cs" />
    <Compile Include="Assets\Script\PostProcessing\BloomOptimized.cs" />
    <Compile Include="Assets\Script\Data\playerdata\GacheData.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\Base\IPSkill.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Box\UIItemBoxBoxBagGroup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZCullTester.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiPackage.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiLayer.cs" />
    <Compile Include="Assets\Script\Effect\Trails\SmokeTrail.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMissile.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MeteoriteEffect\MeteoriteEffectManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\InterfaceLayout\Panel_FightMoveLayout.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYByteReader.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_InputManager.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoInspectorItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AITranDataBase.cs" />
    <Compile Include="Assets\Script\Model\PlayerController.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIWndSettleRewardClaim.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation\VoronoiMesh.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\CreateUtilities.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBlendTree\AIBlendTreeAbility.cs" />
    <Compile Include="Assets\Script\UI2\ConsumerRank\UIWndCommonRewardPreview.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BlueprintBuilder.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Hunting\UIWindowRideBag.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\FadeAction.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BaseBattleModule.cs" />
    <Compile Include="Assets\Script\Message\Handler\TransformerHandler.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB_Utility.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Effect\PlayParticle.cs" />
    <Compile Include="Assets\EasyRoads3D\Scripts\runtimeScript.cs" />
    <Compile Include="Assets\SDK\Sky\Util\DoTweenManager.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\Base\PlayerSkillBase.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionBurst.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\Math2DUtils.cs" />
    <Compile Include="Assets\Script\Message\Handler\SettlementHandler.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Interfaces\IResourceManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ScreenEffect\ScreenEffect.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Bind\BindText.cs" />
    <Compile Include="Assets\Script\App\IGameSetting.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BoxBagData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Worlds\GuideSign.cs" />
    <Compile Include="Assets\Script\Effect\NetEffect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityDici.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_FixedCamera.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Reload.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\FullSerializerExtensions.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroZone.cs" />
    <Compile Include="Assets\Script\UI2\Notice\UIItemNoticeTab.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Trail.cs" />
    <Compile Include="Assets\Script\Data\playerdata\DrawingTableData.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComLockTip.cs" />
    <Compile Include="Assets\Script\Data\playerdata\PlayerLevelTaskData.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Goods3DDisplaySkin.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIMembershipSubscribePanel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Runner\RunnerProcess.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ChipData.cs" />
    <Compile Include="Assets\Script\Data\playerdata\DecomposerData.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BoxDropBehaviour.cs" />
    <Compile Include="Assets\RawData\Effect\causticsProjector.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\SignalTrack.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\SetRenderQueue.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\Lexer.cs" />
    <Compile Include="Assets\Script\UI2\Sponsor\UIWndSponsor.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TransformerRankData.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Shims.cs" />
    <Compile Include="Assets\Script\Data\playerdata\FriendData.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\VertexMappers\UvMapper.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\TextHorizontalGradient.cs" />
    <Compile Include="Assets\SDK\Sky\Util\MultiKeyDictionary.cs" />
    <Compile Include="Assets\Script\FZBPush\AtomType\IFZBAtomBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIWeaponBoxArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIEntityBase.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Util\GuiLayout.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Player\F3DPlayerController_Legacy.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateIdle2.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_OtherAddons.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\EffectTrack.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TalentData.cs" />
    <Compile Include="Assets\DynamicBone\Scripts\DynamicBonePlaneCollider.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIForgeMatSelectItem.cs" />
    <Compile Include="Assets\Script\UI2\Technology\UIWindowTechBook.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Controllers\ThirdPersonCameraController.cs" />
    <Compile Include="Assets\Script\Effect\EffectController.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Runtime\UtilityServicesRuntime.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\GPUInstancingDraw.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\GameShop\UIWndGameShop.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\TreasureAndDrop\UIWindowTreasureOpen.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\MathEx.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIBaseItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIObstacle\AIObstacleHp.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenPosition.cs" />
    <Compile Include="Assets\SDK\Sky\Util\ColorTool.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\UnLoadGameObject.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BuJiBoxData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\TouchRegionEvent.cs" />
    <Compile Include="Assets\Script\Model\InputSystem\AdaptiveKalmanFilter.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UICombatModeHelp.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenRotation.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\PathLerp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralCircleArea.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbEdge.cs" />
    <Compile Include="Assets\Poco\TcpDatagramReceivedEventArgs.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Const\Prefs.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningExecuteOncePerFrame.cs" />
    <Compile Include="Assets\FORGE3D\PoolManager\F3DPoolContainer.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceThirdAPI.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\ShowNameInEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentUpgrade.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\ScreenSpaceCanvas.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_PlayerPrefsX.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterSpectrum.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComReplaceItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Worlds\GuideWorldPoint.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UICommonProgressImage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\MonsterArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapScaleSync.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\UIPolygonDrawer.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Templates\Top Down\Scripts\Characters\MyCharacter.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\AnimationToken.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIItemPassChallenge.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Debug\DebugReport.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasureManager.cs" />
    <Compile Include="Assets\Script\Message\Handler\NoticeHandler.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\QueryInterval.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Trigger\ObservableDragTrigger.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoTabItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Closet\UIItemClosetCommon.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Helpers\CompoundObjectController.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\Inflate.cs" />
    <Compile Include="Assets\Script\Model\InputSystem\KalmanFilter.cs" />
    <Compile Include="Assets\SDK\Sky\Mission\MissionToken.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Tool.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateReload.cs" />
    <Compile Include="Assets\Script\Message\Handler\SectorInfoHandler.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UIWndRecharge.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Helpers.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIWndWeekSignIn.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Trigger\ObservableLongPointerDownTrigger.cs" />
    <Compile Include="Assets\Script\Model\Control\CameraController.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\PSkillBoom.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Extensions\GuideCenterExtension.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasureConfig.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningPlayerResources.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetReceiveList.cs" />
    <Compile Include="Assets\Script\UI2\Common\HungerAndThirstBuff.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\Lexer.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Event\EventDispatcher.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Point.cs" />
    <Compile Include="Assets\EasyRoads3D\Scripts\ERVegetationStudio.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableMonoBehaviour.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningPlayerJoint.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityElectricalGenerator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideSelector.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\ZipTool.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Assets\InspectorPreviewAsset.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\RepairDesk\UIRepairDeskWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tasks\Animation\PlayAnimationClipTask.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_TrailerAttacher.cs" />
    <Compile Include="Assets\Script\Model\Weapon\WeaponRecoilMgr.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Box\BaoXiangAnimatorControl.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\LookJoystickEventToFire.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZString.Utf8Format.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\FormatHelper.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UITeamCommunicateItem.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIWndMedalSettle.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIForgeResonanceWnd.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Cleaner\UIWindowCleaner.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\SGC\Launcher\SGCProjectSetting.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation\VoronoiEdge.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\LOD\LODManager.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ParticleAndAnimation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AirDrop\AirDropPointConfig.cs" />
    <Compile Include="Assets\SDK\Heap\MaxHeap.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ConditionPackageData.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\Attr\RPGAttrDetail.cs" />
    <Compile Include="Assets\Script\Model\AnimationKey.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation\TriangulationCell.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\EBusBattleUI.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterRevealer.cs" />
    <Compile Include="Assets\Script\UI\UIMsgBox.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingNetEvent.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Event\EDataHelper.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZString.Concat.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_SpikeStrip.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Element_DontRotate.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Transform\MoveBy.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SwitchData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Export\MonsterSlotObj.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Engine.cs" />
    <Compile Include="Assets\Script\Model\CheckTrigger.cs" />
    <Compile Include="Assets\Script\Data\playerdata\NoticeClientData.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonReader.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.13.- Planet Walk\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateSprint.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\UtilityServices.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\AlphaPulse.cs" />
    <Compile Include="Assets\Script\Data\EBusNet.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\AssetEncrypt.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenFuncObject.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIResonanceItem.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeShop\UIExchangeShopWnd.cs" />
    <Compile Include="Assets\SDK\LSHDotNet\Result.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiTime.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Trigger\ObservablePointerMoveTrigger.cs" />
    <Compile Include="Assets\Script\Message\Handler\WorkbenchHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CrashShredder.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeHuntShop\UIWGExchangeHuntTask.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonAIBehaviour.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\LampControl.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ShopData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\GameShop\MeritoriousIntegral.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Move\TrapLinearMoveParam.cs" />
    <Compile Include="Assets\Script\UI2\Notice\UIWindowSysNotice.cs" />
    <Compile Include="Assets\Script\UI2\Friend\UIFriendAddWnd.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf8ValueStringBuilder.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionGrid.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\HitArrow\HitArrowData.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Bind\UIAutoBindHelper.cs" />
    <Compile Include="Assets\Script\App\LightingManager.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\SceneDynamicAddDelete\MB_DynamicAddDeleteExample.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MaterialPoint\MaterialPointMonsterSlot.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\LODLevelState.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\CallbackNotification.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.NumberToFloatingPointBits.cs" />
    <Compile Include="Assets\Script\Model\Move\MoveMonster.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Hunting\RideUseControl.cs" />
    <Compile Include="Assets\Script\Data\playerdata\CookingSystemData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIObstacle\AIObstacle.cs" />
    <Compile Include="Assets\Script\Message\Handler\InterfaceLayoutHandler.cs" />
    <Compile Include="Assets\Script\Message\MessageFactory.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetBezierParam.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\GameObject\LoadPrefab.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\UnityTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillTitleToCircle.cs" />
    <Compile Include="Assets\SDK\KDTree\KDQuery\QueryRadius.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapRotation.cs" />
    <Compile Include="Assets\Script\Message\Handler\BoxSupplyHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureTrigger.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ExchangeShopData.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\VertexMappers\ColorMapper.cs" />
    <Compile Include="Assets\Script\Model\Weapon\Weapon.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_MobileDrag.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\SolidColorMapper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleManager.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoDetailItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SysVending\UISysVendingItem.cs" />
    <Compile Include="Assets\XLua\Gen\ActivityInterfaceWrap.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\ProtocolBase.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComGetWayWnd.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\LineMove.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\Utility\MeshUtils.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Tree.cs" />
    <Compile Include="Assets\Script\VoiceEngine\VoiceGMEEngine.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWndBuyItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapLaser.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\OnScreenCustomPad.cs" />
    <Compile Include="Assets\Script\Data\playerdata\StealBagData.cs" />
    <Compile Include="Assets\XLua\Tutorial\LuaCallCSharp\LuaCallCs.cs" />
    <Compile Include="Assets\Script\UI2\Common\ItemIcon.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIFriendListItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowFight.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiLayerModal.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\WaterSurface.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ElectricSystem\UIWndElectroCircuit.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\AnimationSprite.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetSystemMessage.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\CommonGMWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\ZombieTideTargetConfig.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\TextColor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZDrawer.cs" />
    <Compile Include="Assets\Script\Data\playerdata\OfflineMsgData.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Noise.cs" />
    <Compile Include="Assets\Script\Model\Character\SurvivalPlayerCharacter.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoDetailToggleItem.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\DataModel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Door\DoorManager.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIBigMapHunt.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYLocalizationText.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Revive\ReviveArea.cs" />
    <Compile Include="Assets\Script\App\Scene.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.10.- Fall Damage\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\Data\playerdata\WarReserveData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Facility.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Utillities\CircularBuffer.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiDesktop.cs" />
    <Compile Include="Assets\Script\App\GameObj.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\FireFly\FireFlyManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIForgeMatSelectWnd.cs" />
    <Compile Include="Assets\Script\Model\Component\SurvivalBodyLean.cs" />
    <Compile Include="Assets\SDK\Sky\Db\MySqlDB.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBlendTree\AICommonBlendTree.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\RectTransformExt.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingHitStub.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CameraCarSelection.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CameraGray.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HLodLoader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\ScriptableObject\SPCRJointDynamicsPointGrabberData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityOrGate.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Teleporter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Evacuate\ExtractEffectMgr.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoSignItem.cs" />
    <Compile Include="Assets\Poco\TcpClientConnectedEventArgs.cs" />
    <Compile Include="Assets\RawData\Effect\fajittu\shui\shuijian\Scripts\StylizedWaterInspector.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\SpriteTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Area\AreaManager.cs" />
    <Compile Include="Assets\Script\Message\Handler\RankHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Mission\TableMission.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\SceneRuntimeExample\MB_Example.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\Vector2Ext.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoSocketPool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\SupplyObject.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Drag.cs" />
    <Compile Include="Assets\Script\Model\Action\ActionWeaponPlayer.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.8.- Swim\Scripts\MyFirstPersonCharacter.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\ValueStringBuilder.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoClientSingle.cs" />
    <Compile Include="Assets\XLua\Src\LuaFunction.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\MapPoint.cs" />
    <Compile Include="Assets\SDK\FG\service\netrequest.cs" />
    <Compile Include="Assets\Script\Data\Table\TblLoadUtil.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleUtils.cs" />
    <Compile Include="Assets\RawData\Effect\fajittu\shui\shuijian\Scripts\EnableDepthBuffer.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\StealBag\UIWindowStealBag.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Logger.cs" />
    <Compile Include="Assets\Script\Data\playerdata\WorkbenchData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BattleTask\UIWindowShowADReward.cs" />
    <Compile Include="Assets\Script\Model\ScriptableObject\PlayerHitBox.cs" />
    <Compile Include="Assets\Script\Model\Weapon\BulletPhalanx.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroMeshSeasons.cs" />
    <Compile Include="Assets\XLua\Gen\Tutorial_CSCallLua_ItfDBridge.cs" />
    <Compile Include="Assets\SDK\Sky\Mission\MissionPlayer.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UISettingsWnd.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB2_UpdateSkinnedMeshBoundsFromBounds.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\TextGradient.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIDamageListDetail.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Components\PhysicsVolume.cs" />
    <Compile Include="Assets\XLua\Src\ObjectCasters.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiProgress.cs" />
    <Compile Include="Assets\Script\Message\Handler\BoxHandler.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\PlayerDisplayMsgData.cs" />
    <Compile Include="Assets\Script\Message\Handler\SignalTowerHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Platform\Win32Api.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\8.- Custom Simulation\Scripts\ISimulatable.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningClip.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\GameShop\UIWndShopSingleItemBuy.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GrassInstances.cs" />
    <Compile Include="Assets\Script\Data\playerdata\CleanerData.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Items\ThermoTextItem.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Paint.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\AppendDeltaPositionTrigger.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Closet\UIWindowClosetMain.cs" />
    <Compile Include="Assets\XLua\LuaManager.Buff.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYLocalization.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.3.- Twin-Stick Movement\Scripts\TwinStickCharacter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AirDrop\AirDropManager.cs" />
    <Compile Include="Assets\XLua\Src\SignatureLoader.cs" />
    <Compile Include="Assets\Script\UI2\Bag\DefDataManuItem.cs" />
    <Compile Include="Assets\Script\App\RandomName.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiEventForm.cs" />
    <Compile Include="Assets\Script\Data\playerdata\AirDropData.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\GM\GMConsole.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EvacuateBox\EvacuateBoxObject.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\ScriptableObject\SPCRJointDynamicsData.cs" />
    <Compile Include="Assets\SGC\Launcher\SGCLauncher.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\ActionAsset.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\CheckPlayerInfo\UIWndCheckPlayerInfo.cs" />
    <Compile Include="Assets\VolumetricFog\Scripts\VolumetricFogPreT.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Skidmarks.cs" />
    <Compile Include="Assets\Script\Thermodynamic\ThermoMove.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Common\FloatingWordManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\PlayerBuilder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentLearn.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\CreatecharWnd.cs" />
    <Compile Include="Assets\XLua\Src\TypeExtensions.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\HashList.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\ZombieTideConfig.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Maps\LittleMapSmallGuide.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\ListRand.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Common\MathLib.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SeasonPassData.cs" />
    <Compile Include="Assets\Script\Message\Handler\EvacuateBoxHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\AdGain\UIWindowAdGain.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateBuild.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateFall.cs" />
    <Compile Include="Assets\Script\UI2\IntenseMode\UiWndReceiveIntenseInvite.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MeshCombineTool.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZString.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIEntityManager.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\CameraTargeting.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIWndPreviewEvent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityDoubleBed.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Sector.cs" />
    <Compile Include="Assets\Script\Model\ScriptableObject\PlayerSkillConfig.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UIWndRideSettings.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UILittleBigMap.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\EasyObjectPool\LoopResourceManager.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateEvacuation.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Locomotion.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Properties.cs" />
    <Compile Include="Assets\Script\Data\playerdata\NearbyBroadcastData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\BattleRecord\UIWndBattleRecord.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetServerApp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\ActionTrack.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.15.- Prevent Standing On Other Character\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SupplySpotData.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_UpgradeManager.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.BigInteger.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\InterfaceLayout\Panel_FightLayout.cs" />
    <Compile Include="Assets\Script\UI2\NoviceGiftPack\UIWndNoviceGiftPack.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\UnityEventTool.cs" />
    <Compile Include="Assets\Poco\Utils\SingletonMonobehaviourBase.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\ReflectionTools.cs" />
    <Compile Include="Assets\Script\Model\Actor.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIGNUnlock.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeCurrency\ExchangeCurrency.cs" />
    <Compile Include="Assets\Script\Message\Handler\KillStreakLeaderboardHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\SceneObjectLoadController.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\JoyStickEvent.cs" />
    <Compile Include="Assets\DynamicBone\Demo\DynamicBoneDemo1.cs" />
    <Compile Include="Assets\Script\UI2\DrawCard\UIWndDrawCard.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SignalTowerData.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Rain\Rain.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ElectricLampData.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZString.Prepare.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_ColorPickerBySliders.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildCreate.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComMoneySelectWnd.cs" />
    <Compile Include="Assets\Script\UI2\Login\UIWindowAgeTip.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiActionMove.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_DetachablePart.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\Extensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Value.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMountConfig.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ObjectRotate.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\NPC\NPCObjectConfig.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroInteriorFog.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMalberInput.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingPiece.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\FYLocalizationManager.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Assets\Clip.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\Utility\ResizableArray.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUnityVersion.cs" />
    <Compile Include="Assets\Script\Model\Character\SurvivalPlayerCharacterBase.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIItemManuGroup.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbStep.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Lenfares\MYLensFlare.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenGame.cs" />
    <Compile Include="Assets\SDK\Sky\Util\EnumUtils.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CharacterController.cs" />
    <Compile Include="Assets\Script\RenderFeature\ScanLineRenderFeature.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SysVending\UISysBuyLayer.cs" />
    <Compile Include="Assets\Script\Data\LoginData.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\ThunderController.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CustomizerExample.cs" />
    <Compile Include="Assets\Script\UI2\FirstRecharge\UIWndFirstRecharge.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\_testing\MB3_TestBakeAllWithSameMaterial.cs" />
    <Compile Include="Assets\Script\App\DebugConfig.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Informer.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\PresetSelector.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoClient.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\MeshSimplifier.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildChangeIcon.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Box\UIItemBoxGameBagGroup.cs" />
    <Compile Include="Assets\XLua\Src\CodeEmit.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\ZString.Format.cs" />
    <Compile Include="Assets\Script\Message\Handler\FacilityUpGradeHandler.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUtils.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Interfaces\IDirectable.cs" />
    <Compile Include="Assets\Script\Message\Handler\ActivityHandler.cs" />
    <Compile Include="Assets\Script\Model\Player.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\ListFree.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeatureAPIParam.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineIncomeItem.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Clutch.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\SetModelMaterials.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\JsonException.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\FireFly\FireFlyArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySlotMachine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeaturePartFollow.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIMapShowObj.cs" />
    <Compile Include="Assets\Poco\Dumper\INodeGrabber.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateImprison.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AILaserConnect.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_MultiMeshCombiner.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\SceneTransformDetector.cs" />
    <Compile Include="Assets\Script\Tools\CalcLodValue.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DWarpJumpTunnel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Collection.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ElectricSystem\UIWndElectroCircuitSelectRide.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB3_MeshCombiner.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Thermodynamic.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ReportData.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateMeleeLeft.cs" />
    <Compile Include="Assets\Script\Tools\SerializeHelper.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Bind\BindChildContext.cs" />
    <Compile Include="Assets\Script\Model\State\State.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MountManager.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Dataset\DataSet.cs" />
    <Compile Include="Assets\Script\RenderFeature\AuroraRenderFeature.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\ModelAnimation.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateCollect.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\PrepareObjectsForDynamicBatching\MB_PrepareObjectsForDynamicBatchingDescription.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\UIWeaponBoxTitleArea.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AIWaypointsContainer.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\BufferEx.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Talent\UIWndTalent.cs" />
    <Compile Include="Assets\Script\UI2\Bag\DefDataBagItem.cs" />
    <Compile Include="Assets\Script\UI2\HelpPanel\UIWndHelpPanel.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Brake.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBApiData\FZBApiConstCheckDefine.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\LoadingProgress.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\ParserToken.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIHitTrigger.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Interactive\TrapLaserTrigger.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComTipsWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityMedicine.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIHumanLua.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SysVending\UIWindowSysVending.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\Polygon.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Forge\UIWndShowSpeedItem.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Generic\VersionInformation.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\DoorControl\UIWindowDoorControl.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillHookClaw.cs" />
    <Compile Include="Assets\Script\Effect\EffectPath.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Components\ThermoInspector.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_SpoilerManager.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\Easing.cs" />
    <Compile Include="Assets\Script\Data\playerdata\HomeAttackedAlarmData.cs" />
    <Compile Include="Assets\Script\UI2\Technology\UIWindowTechSpeedTip.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIMoveLua.cs" />
    <Compile Include="Assets\Script\UI2\Common\Portrait.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\IAimUnit.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_BatchPrefabBaker.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Debug\DebugUtils.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Lights.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHullGenerationException.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIChatBgTouch.cs" />
    <Compile Include="Assets\Script\UI2\Community\UICommunityWnd.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenFuncObjectAni.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Bomb\ShouleiObject.cs" />
    <Compile Include="Assets\XLua\LuaManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\MagicaClothManager.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildList.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.5.- Custom Deadzone\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWindowBuyPassport.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Extensions\IDirectableExtensions.cs" />
    <Compile Include="Assets\SDK\Sky\Util\RectTransformExtensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureHitHp.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\F3DTurretUI.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\Adler32.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UILimitedTimeRechargePanel.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AI.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoBornCheckPage.cs" />
    <Compile Include="Assets\SDK\Heap\MinHeap.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIButtonCustomTouch.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComReplaceWnd.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_QuaternionWrap.cs" />
    <Compile Include="Assets\Script\UI2\Welfare\UIWndWelfare.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIWndGameRecharge.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\BatchPrepareObjectsForDynamicBatching\MB_BatchPrepareObjectsForDynamicBatchingDescription.cs" />
    <Compile Include="Assets\Script\Model\Character\SurvivalPlayerCharacterLua.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\PriorityQueue.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\GM\GMCommand.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Event_Output.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\RealTimeTranslation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\ActionClipTasklBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideManager.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordLoad.cs" />
    <Compile Include="Assets\Script\Message\Handler\RechargeHandler.cs" />
    <Compile Include="Assets\Script\FZBPush\AtomType\IFZBAtomMove.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonSMBHit.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticRes\LampObject.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowDurableMsg.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\JoyStick\AutoRunArrow.cs" />
    <Compile Include="Assets\SDK\Sky\Util\TimeTool.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\FollowTarget.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowGameBag.cs" />
    <Compile Include="Assets\Script\UI2\OutsideAchieve\UIWndAchieveReward.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\PreparedFormatHelper.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.2.- Character Controller\Scripts\MyCharacterController.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Mouse\MouseShatter.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AIBrakeZone.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Scripts\MeshUniqueVertices.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningFrame.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Sensitivity\SensitivityMgr.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\PlayerDisplayController.cs" />
    <Compile Include="Assets\Script\Data\playerdata\IntenseModeData.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\SkinnedTool.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Equipment\UIEquipmentSlot.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\MeshCombine\MeshCombineUtility.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Templates\First Person\Scripts\Characters\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Common\NowEquipmentShow.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityExclusiveOrGate.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\StringBuilder.AppendJoin.cs" />
    <Compile Include="Assets\Script\Model\Pawn.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.2.- Custom Input Actions\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Characters\ThirdPersonCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIItemRechargeItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityXiangzi.cs" />
    <Compile Include="Assets\Script\Message\Handler\WarReserveHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_DemoVehicles.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SysVendingData.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\UGUI\BindUGUIImage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\TrapPlatform.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\TextUpFloat.cs" />
    <Compile Include="Assets\SDK\Sky\Net\UDP\FGUdpProtocolProc.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityChargingTable.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\IFightControl.cs" />
    <Compile Include="Assets\Script\Occlusion\OccusionHashCode.cs" />
    <Compile Include="Assets\Script\App\Define.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\ComboBox.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbLowEdge.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Bomb\C4Manager.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningMaterial.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\GameObject\VisibleTo.cs" />
    <Compile Include="Assets\Script\Model\Weapon\BulletMove\BulletMoveBlink.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_DisableHiddenAnimations.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ObjectFollow.cs" />
    <Compile Include="Assets\Script\Model\WeaponAnimProfile.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIBaseOperation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tasks\Transform\MoveByClipTask.cs" />
    <Compile Include="Assets\Script\App\UniSDK\HotFixInterfaceImpl.cs" />
    <Compile Include="Assets\Script\Data\playerdata\RechargeData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySignalTowerReward.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\SortComparer.cs" />
    <Compile Include="Assets\Script\Model\InputSystem\MobileDevice.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MBVersionConcrete.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Area\AreaConfig.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\StealBag\UIItemStealBagOtherBagGroup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\ShapeCollisions.cs" />
    <Compile Include="Assets\Script\UI2\Common\DiamondsNum.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoSocket.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Door\SceneDoor.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DRay.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionPhysics.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.Parsing.cs" />
    <Compile Include="Assets\XLua\Src\ObjectTranslatorPool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetRotation.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Interface\ITask.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\CountDownMgr.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Stronghold.cs" />
    <Compile Include="Assets\Script\UI2\BoxSupply\UIBoxSupplyExchangeWnd.cs" />
    <Compile Include="Assets\XLua\Src\LuaTable.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\ECTriangulatorJob.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Dataset\MaterialData.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf16\Utf16ValueStringBuilder.CreateFormatter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\SceneInfoSerialize.cs" />
    <Compile Include="Assets\SDK\Sky\Util\TextExtensions.cs" />
    <Compile Include="Assets\Script\UI2\IntenseMode\UIWndModeIllustration.cs" />
    <Compile Include="Assets\Script\Message\Handler\DoorControlHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\Base\TransferPoint.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Wheel.cs" />
    <Compile Include="Assets\Script\Data\playerdata\OrdnanceProcessData.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\ObjectImporterUI.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\Trigger\ElectricTrigger.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\Destroyer.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowOneKeyProduce.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\SupplyManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIWindowLingDiGuiAuth.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Canvas_Customization.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ActivityData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ComfortLevel\UIWindowComfortLevel.cs" />
    <Compile Include="Assets\SDK\Sky\Util\ParseParam.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DVector3.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Runner\Runner.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Attributes\PropertyAttributes.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMonsterPartHp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IBehaviourControl.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Scripts\MeshSimplify.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Action\BindActionString.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\Utility\Vector3d.cs" />
    <Compile Include="Assets\Poco\3rdLib\UWALitJson\ParserToken.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Activity.cs" />
    <Compile Include="Assets\Script\Model\Weapon\Bullet.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildLog.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\RoundCountDownControl.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BattleStateData.cs" />
    <Compile Include="Assets\VacuumShaders\Terrain To Mesh\Example Scenes\Scripts\Runtime_OBJExporter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectLOD\EffectLODManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillCircleExtendDamage.cs" />
    <Compile Include="Assets\Script\Model\CheckMonsterTrigger.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Constants.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIWindowAuthTip.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Move\TrapRotationParam.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\InterfaceLayout\UIWndInterfaceLayout.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComWnd.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ChatData.cs" />
    <Compile Include="Assets\Script\UI2\Common\UISensitivitySettingWnd.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AICorrosion.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_InputActions.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_TimeWrap.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UISettingsLobbyWnd.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Demo\Scripts\SpeedModifier.cs" />
    <Compile Include="Assets\SDK\zlib\zlib1.04\InfBlocks.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleResConfig.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Settings.cs" />
    <Compile Include="Assets\Script\Message\Handler\handlerbase.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Utility\EnviroLightning.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIWndMonthCardResign.cs" />
    <Compile Include="Assets\ParrelSync\Examples\CustomArgumentExample.cs" />
    <Compile Include="Assets\XLua\Gen\UnityEngine_Vector2Wrap.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Handling.cs" />
    <Compile Include="Assets\Script\Message\Handler\TaskHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetIoBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Door.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Util\GuiUtil.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ReviveData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralResObject.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB2_UpdateSkinnedMeshBoundsFromBones.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\ScriptableObject\SPCRJointDynamicsControllerData.cs" />
    <Compile Include="Assets\Script\Message\Handler\ReportHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Drop\DropParse.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionPlane.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SleepBagData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\AutoRifleTurret\UIWindowAutoRifleTurret.cs" />
    <Compile Include="Assets\Script\UI2\Technology\VisibilityToggle.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Templates\Side Scroller\Scripts\Characters\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIItemLingDiGui.cs" />
    <Compile Include="Assets\Script\Message\Handler\ElectricHandler.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\_testing\MB3_TestRenderTextureTestHarness.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowFightMove.cs" />
    <Compile Include="Assets\Script\Animation\UIAnimator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureBuffPassive.cs" />
    <Compile Include="Assets\Script\Message\Handler\SponsorHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\ScrollPane.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Events.cs" />
    <Compile Include="Assets\Script\UI2\Bag\DefDataSafeBoxItem.cs" />
    <Compile Include="Assets\Script\UI2\BoxSupply\UIBoxSupplyWnd.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Action\BindAction.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DropPackage\ExtendFeature\HuntFeatureDataSort.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityWaterCollect.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetColliderComponent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\ExtendEvent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\SceneLinkedSMB.cs" />
    <Compile Include="Assets\SDK\Sky\RPG\Attr\EnumDetailAttribute.cs" />
    <Compile Include="Assets\SDK\FG\net\FGNetProtoMsg.cs" />
    <Compile Include="Assets\XLua\Src\RawObject.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIWindowMonthCardMain.cs" />
    <Compile Include="Assets\SDK\Sky\Util\LagHandle.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.14.- Ladders\Scripts\Ladder.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SignalTower\UIWindowSignalTowerLog.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceDownloader\SGCDownloadAssetManager.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateClimbLadder.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\ToleranceSphere.cs" />
    <Compile Include="Assets\SDK\Sky\Net\UDP\NetUdpServer.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Common\CollisionDetection.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIParabola.cs" />
    <Compile Include="Assets\XLua\Src\TemplateEngine\TemplateEngine.cs" />
    <Compile Include="Assets\SDK\Sky\Sound\SoundPitch.cs" />
    <Compile Include="Assets\Script\UI2\Common\SuitItemShow.cs" />
    <Compile Include="Assets\Script\Model\PlayerShow.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningUtil.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Light.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Thunderstorm\Sources\Scripts\THOR_Lightning.cs" />
    <Compile Include="Assets\Script\Tools\EditorMonoTool\GizmosDrawMono.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningBetterList.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\PathSettings.cs" />
    <Compile Include="Assets\Script\UI2\Loading\UIWindowLoading.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\RTOccludee.cs" />
    <Compile Include="Assets\Script\Model\Character\SurvivalPlayerCharacterHP.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\MineralArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AISkillBoxDamage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIFlyHit.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\CameraTrack.cs" />
    <Compile Include="Assets\Script\Message\Handler\QuestHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Door\doorPlank.cs" />
    <Compile Include="Assets\Script\Message\Handler\ConditionPackageHandler.cs" />
    <Compile Include="Assets\SDK\FG\service\requestservice.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\GlobalParametersExample.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilitySmallChair.cs" />
    <Compile Include="Assets\Script\Message\MessageProfiler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Express\Express.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\YouMeVoiceAPI.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Collection\TaskCollection.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningQuality.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Event\BaseEventBus.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BagDataOp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticRes\FanMaiJiDiTaiObject.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\ConvexHullAlgorithm.2D.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\PingPongAni.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\WeatherManager.cs" />
    <Compile Include="Assets\Script\Model\Move\MoveBase.cs" />
    <Compile Include="Assets\Script\Message\Handler\SupplySpotHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Exterior_Cameras.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIMonsterManager.cs" />
    <Compile Include="Assets\Script\Message\Handler\WelfareHandler.cs" />
    <Compile Include="Assets\Script\Message\Message.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Records.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.13.- Planet Walk\Scripts\MyCameraController.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComGetWayItem.cs" />
    <Compile Include="Assets\SDK\LSHDotNet\ILSHashable.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenTrigger.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\SceneryMonsterManager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AssetPaths.cs" />
    <Compile Include="Assets\Script\Message\Handler\LingDiGuiHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Magic\MagicManager.cs" />
    <Compile Include="Assets\Script\Message\Handler\TentHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\CanvasPerform.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\BornNPCConfig.cs" />
    <Compile Include="Assets\Script\VoiceEngine\IVoiceEngine.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowBagFightShortcut.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgStepsManager.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIModuleSelectWnd.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateThrowGrenade.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Constructor\F3DTurretScriptable.cs" />
    <Compile Include="Assets\Script\Effect\CommandBufferGrab.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\ObjectPoint.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\Tea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Camera\CameraControl.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\RefreshEnforcer.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\ElectricSystem\UIWndElectroCircuitItem.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\MaterialCombiner.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Interfaces\ISubClipContainable.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\ObjectMaterialLinks.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.4.- Old Input Examples\1.4.4 Top Down\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowFeedNum.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Templates\Third Person\Scripts\Characters\MyCharacter.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Trigger\ObservablePointerTrigger.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TeamData.cs" />
    <Compile Include="Assets\XLua\Gen\EnumWrap.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Data\Scripts\SimplifyMeshPreview.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ManagerFeature\ManagerFeatureProvider.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\SceneMagicaCloth.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Manager\GuiSound.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIEquipShield.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CinematicCamera.cs" />
    <Compile Include="Assets\Script\Message\Handler\ViolationBehaviorHandle.cs" />
    <Compile Include="Assets\Script\Tools\LightControl.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiCustom.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\EnumUtil.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Interactive\TrapTriggerArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\SceneDetailLayerManager.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\ObjectColor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Animation\PlayAnimation.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Differential.cs" />
    <Compile Include="Assets\XLua\Src\ObjectPool.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Parabola.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIModuleReplaceItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\TerritoryLevel\UIWndTerritoryLevelDetail.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\GlobalUIBaseItemPool.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\_testing\MB3_TestAddingRemovingSkinnedMeshes.cs" />
    <Compile Include="Assets\Script\Message\Handler\WaterCollectHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AISkill\AIFollowEffect.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Messenger.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\PresetFashion.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\IVertex.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZDepthGenerater.cs" />
    <Compile Include="Assets\Script\Thermodynamic\ThermoDetailInfo.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Forge\ForgeItem.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateSquat.cs" />
    <Compile Include="Assets\XLua\Gen\SGC_SGCLuaCallCSUtilsWrap.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\PackageHead.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_PaintManager.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Characters\Character.cs" />
    <Compile Include="Assets\Poco\Utils\Deserialize.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DLightning.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Event\BaseEDatas.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_SceneManager.cs" />
    <Compile Include="Assets\Script\UI2\Common\VoucherNum.cs" />
    <Compile Include="Assets\Script\Message\Handler\FriendHandler.cs" />
    <Compile Include="Assets\Script\Occlusion\BoundsOctreeNode.cs" />
    <Compile Include="Assets\Script\App\GameConfig.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBFeature\FZBFeaturePhysic.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIItemLingDiGuiAuthFriend.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildImpeachLeader.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Utility\EnviroCamera.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Medicine\UIItemMedicineItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LingDiGui\UIWindowLingDiGuiMaintainInfo.cs" />
    <Compile Include="Assets\Script\UI2\FundRebate\UIWndFundRebate.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Transform\ScaleTo.cs" />
    <Compile Include="Assets\Poco\TcpClientDisconnectedEventArgs.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UITabGroup.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_VehicleUpgrade_Spoiler.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\Collections.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIPacemakerButton.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateFistLeft.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowMono.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\WaterCollect\UIWindowWaterCollect.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenScript.cs" />
    <Compile Include="Assets\Script\FZBPush\AtomType\IFZBAtomPhysic.cs" />
    <Compile Include="Assets\Script\Token\PlayerToken.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Stronghold\StrongholdManager.cs" />
    <Compile Include="Assets\Script\Model\CharacterAnim.Fall.cs" />
    <Compile Include="Assets\Script\App\App.cs" />
    <Compile Include="Assets\Script\Data\playerdata\VendingMachineData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityHunting.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningPlayer.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation\DelaunayTriangulation.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BookcaseData.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\ObjectManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\SelectCircle\SelectCircle.cs" />
    <Compile Include="Assets\Script\Thermodynamic\ThermoResDetailInfo.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DropPackage\IDropFeatureShowDataSort.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Utility\EnviroReflections.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TechnologyData.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Utility\EnviroAudioSource.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityTreasureArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\GrassManager.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DMissile.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Erosion.cs" />
    <Compile Include="Assets\Script\Effect\XRayEffect.cs" />
    <Compile Include="Assets\Script\Message\Handler\TechnologyHandler.cs" />
    <Compile Include="Assets\Script\Data\playerdata\TaskData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RidePet.cs" />
    <Compile Include="Assets\XLua\Src\LuaBase.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_DemoAIO.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityPterosaurTransit.cs" />
    <Compile Include="Assets\Script\UI2\Shop\DefDataShopCart.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\PresetDynamicBone.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB2_Core.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBData\FZBDataCheckDefine.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Evacuate\RadiationSourceMgr.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\SceneRuntimeExample\MB_ExampleMover.cs" />
    <Compile Include="Assets\post\PostEffectBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityTimer.cs" />
    <Compile Include="Assets\Script\UI2\Setting\UISettingsRechargeReturnWnd.cs" />
    <Compile Include="Assets\Script\Message\Handler\TeamHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Inputs.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\SplitMesh\ShatteUvMapper.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningSampler.cs" />
    <Compile Include="Assets\I2\Localization\Examples\Common\Scripts\Example_ChangeLanguage.cs" />
    <Compile Include="Assets\Poco\Utils\JsonAgent.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningWrapMode.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB2_TexturePacker.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\Utils\GizmosEx.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiCartoon.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\ColorUtility.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUiGm.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideBuff.cs" />
    <Compile Include="Assets\Script\Model\ScriptableObject\VolumeConfig.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateSleep.cs" />
    <Compile Include="Assets\Script\Message\Handler\InviteFriendsHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowLock.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityRepairDesk.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\IHull.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetCapsuleCollider.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\List\Timers.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\DyingHp\DyingHp.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerSkill\Base\PSkillManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Export\MonsterSceneryObj.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UITurntableIllustrationWnd.cs" />
    <Compile Include="Assets\Script\Message\Handler\ShopHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DropPackage\ExtendFeature\StandardFeatureDataSort.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Interface\IRunner.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\CollectionArea.cs" />
    <Compile Include="Assets\PolyFew\demo\Scripts\FlyCamera.cs" />
    <Compile Include="Assets\Poco\Dumper\UGUINode\UnityDumper.cs" />
    <Compile Include="Assets\DynamicBone\Scripts\DynamicBone.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWndPassPortUp.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\ImageIndex.cs" />
    <Compile Include="Assets\Script\Data\playerdata\InterfaceLayoutData.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_ShowroomCamera.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Collections\IDList.cs" />
    <Compile Include="Assets\Script\UI2\NetwrokCheck\UIWindowNetworkCheck.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetSendList.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Move\TrapMoveParam.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\ActivityManager.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\RaditionSourceBox\UIWndRaditionSourceBox.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Lightmap\FGPrefabLightmapData.cs" />
    <Compile Include="Assets\SGC\Unity\Utils\LuaCallCSUtils.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UITimeDActPanel.cs" />
    <Compile Include="Assets\Script\Effect\RelativeRender.cs" />
    <Compile Include="Assets\Script\Data\playerdata\BoxSupplyData.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\4.- Platforms\4.1.- Scripted Platform\Scripts\KinematicRotate.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIItemHallBagWeapon.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetMagicaBoneCloth.cs" />
    <Compile Include="Assets\XLua\Gen\WrapPusher.cs" />
    <Compile Include="Assets\Script\Data\playerdata\UltimateTalentData.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIWndMonthSignIn.cs" />
    <Compile Include="Assets\SDK\Sky\Util\FileTool.cs" />
    <Compile Include="Assets\Script\Model\Action\ActionBase.cs" />
    <Compile Include="Assets\SDK\Sky\Util\NullStruct.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\LOD\CustomLOD.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\UIWindowBuilding.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\Triangulation.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateSwimming.cs" />
    <Compile Include="Assets\Script\Common\MessageCenter.cs" />
    <Compile Include="Assets\Script\UI2\Transition\UIWindowTransitionHaven.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityWeaponLathe.cs" />
    <Compile Include="Assets\Script\Message\Handler\ChargingTableHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityTechnology.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateGrab.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityDrawingTable.cs" />
    <Compile Include="Assets\Script\Data\playerdata\SponsorData.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\NestedStringBuilderCreationException.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenAlpha.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Triangulator\Triangulator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\PresetDynamicBoneCollider.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIItemBaseItemIconGroup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Temperature\TemperatureManager.cs" />
    <Compile Include="Assets\Script\UI2\LittleMap\UIBigMapBase.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CrashPress.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_FOVForCinematicCamera.cs" />
    <Compile Include="Assets\Script\Message\Handler\PartsHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Prop.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\8.- Custom Simulation\Scripts\MySimulatedCharacter.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Exhausts.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AIBrakeZonesContainer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingTest.cs" />
    <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\ByString\ByString.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureSkillNet.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordParam.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIItemShopBigItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Lightmap\TestLightmapManager.cs" />
    <Compile Include="Assets\SDK\Sky\Token\TokenManage.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetMagicaMeshCloth.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\ColorTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityLamp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Area\ExportForSingle.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\SectorInfoControl.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\WorldUvMapper.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBConst.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\BindBase.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateMeleeCut.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentLottery.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\2.- Animation\2.3.- Root Motion Toggle\Scripts\UnityCharacter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Lightmap\FGLightmapData.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_AudioSource.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Pos.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\TerrainLodLoader.cs" />
    <Compile Include="Assets\XLua\Gen\AITriggerEventHitInfoWrap.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DFXController.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroTerrainSeasons.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Bind\BindTextArray.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterConstant.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIRenderLua.cs" />
    <Compile Include="Assets\Script\Message\Handler\RechargeSevenDayHandler.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\TestUIWindowLobbyGm.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Utility\AttributesUtility.cs" />
    <Compile Include="Assets\Script\Message\Handler\OrdnanceProcessHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Npc.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.11.- Teleporter\Scripts\Teleporter.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Common\Tools.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_PoliceSiren.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Sound\SceneSound.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIBuffPassiveLua.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Context.cs" />
    <Compile Include="Assets\UIParticles\Examples\Scripts\SwitchPanels.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\Occluder.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\StealBag\UIItemStealBagGameBagGroup.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_MeshBakerCommon.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\EditorCoroutineExtensions.cs" />
    <Compile Include="Assets\Script\UI2\ExchangeHuntShop\UIWndExchangeHuntShop.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIChatItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\OptionParams.cs" />
    <Compile Include="Assets\Script\NGUI-lite\Scripts\Internal\NGUITools.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DynamicBone\PresetFashionDynamicBones.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Thunderstorm\Sources\Scripts\THOR_Thunderstorm.cs" />
    <Compile Include="Assets\Script\Message\Handler\RideHandler.cs" />
    <Compile Include="Assets\Script\Token\Survival\StatePickup.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticRes\StaticResObject.cs" />
    <Compile Include="Assets\SGC\Unity\ResourceManager\SGCAssetManager.cs" />
    <Compile Include="Assets\SDK\Sky\Util\EnumSet.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\UIGridRenderer.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MainMenu\UIWndEvolutionInfo.cs" />
    <Compile Include="Assets\Script\Data\playerdata\FacilityUpGradeData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Tracks\AnimationTrack.cs" />
    <Compile Include="Assets\Script\UI2\Settlement\UIWindowHistoryMilitary.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Common\FloatingWordObject.cs" />
    <Compile Include="Assets\VolumetricFog\Scripts\VolumetricFog.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\BattleMainLoop.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\UI\DropDownList.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\StaticRes\StaticResManager.cs" />
    <Compile Include="Assets\RTOcclusionCulling\UnityMeshSimplifier\Runtime\Utility\SymmetricMatrix.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Utf16\Utf16ValueStringBuilder.AppendFormat.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroWeatherTemplate.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\VendingMachine\UIVendingMachineIncomeWnd.cs" />
    <Compile Include="Assets\Script\UI2\Common\ComItemInfoWnd.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DPredictTrajectory.cs" />
    <Compile Include="Assets\SDK\YouMeVoiceEngine\TalkLitJson\ParserToken.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.14.- Ladders\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\Common\LodDistanceGroup.cs" />
    <Compile Include="Assets\SDK\Sky\Util\Container.cs" />
    <Compile Include="Assets\Script\UI2\Shader\ShaderConfig.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\1.- Input\1.4.- Old Input Examples\1.4.3 Third Person\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEquipAttrInfoItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\LinearSceneTree.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket\NetSocket.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\PlayerBornConfig.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Gaia Integration\EnviroWithGAIA.cs" />
    <Compile Include="Assets\GPUSkinning\Scripts\GPUSkinningAnimEvent.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\Effects\EnviroFogDepth.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_SteeringWheelController.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\RaptorDisplay.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityWorkbench.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenScale.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroPuddle.cs" />
    <Compile Include="Assets\Script\App\UWAGPM_DEBUG.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CameraAspect.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\BakeTexturesAtRuntime\BakeTexturesAtRuntime.cs" />
    <Compile Include="Assets\Script\Message\Handler\CleanerHandler.cs" />
    <Compile Include="Assets\Script\Data\playerdata\HuntingData.cs" />
    <Compile Include="Assets\Script\UI2\NewbieChallenge\UIWndNewbiePoster.cs" />
    <Compile Include="Assets\Script\Model\AnimatorEvent.cs" />
    <Compile Include="Assets\Script\Occlusion\OcclusionManager.cs" />
    <Compile Include="Assets\RawData\Effect\fajittu\shui\shuijian\Scripts\StylizedWater.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Interfaces\IColliderFilter.cs" />
    <Compile Include="Assets\Script\UI2\BoxSupply\UIBoxSupplyRewardWnd.cs" />
    <Compile Include="Assets\XLua\Src\LuaEnv.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\ObjectImporter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MaterialPoint\MaterialPointConfig.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DPlane.cs" />
    <Compile Include="Assets\SDK\KDTree\KDTree.cs" />
    <Compile Include="Assets\SDK\Sky\Util\ArrayTool.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Runtime\PolyfewRuntime.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Mouse\MouseInstantiate.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\TargetUvMapper.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWGChip.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\Message.cs" />
    <Compile Include="Assets\Script\UI2\BoxSupply\UIBoxSupplyItem.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\TreasureObject.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\core\MB2_MBVersion.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Template\TreasureObjectTemplate.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleSprite.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\MonsterRandomPoint.cs" />
    <Compile Include="Assets\DynamicBone\Scripts\DynamicBoneColliderBase.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIWindowChipDetail.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityCookingSystem.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UIChatFriendItem.cs" />
    <Compile Include="Assets\Script\UI2\Common\PopMsgWnd.cs" />
    <Compile Include="Assets\Script\UI2\DrawCard\UIWndDrawCardPoster.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHull\ConvexHullAlgorithm.MainLoop.cs" />
    <Compile Include="Assets\Script\UI2\Activity\UIActivityControl.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Helpers\MovementController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\StreamColliderManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Area\NotAllowArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActivityPlay\ActivityScrollTip.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\F3DTurret.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectCulling\SnowStormCulling.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Treasure\UIWindowTreasureArea.cs" />
    <Compile Include="Assets\Script\FZBPush\AtomType\IFZBAtomApiParam.cs" />
    <Compile Include="Assets\Script\Model\Character\PlayerAnimatorCall.cs" />
    <Compile Include="Assets\Script\Message\Handler\DrawingHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_CrashHammer.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiList.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DMath.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\InteractableObject.cs" />
    <Compile Include="Assets\XLua\Tutorial\CSharpCallLua\CSCallLua.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateMoveSkill.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\Tracker\RemotePlayer\RemotePlayerTracker.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Helpers\RotationController.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateIdle3.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DTrailExample.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\Script\Data\Table\TblDataUtils.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Script\RTOcclusionCulling.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.Monster.cs" />
    <Compile Include="Assets\Script\Message\Handler\GacheHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UIWindowAttribute.cs" />
    <Compile Include="Assets\FORGE3D\PoolManager\F3DPool.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiNumber.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectCulling\EffectCulling.cs" />
    <Compile Include="Assets\Script\UI2\LobbyBag\UIWindowPanelNotSave.cs" />
    <Compile Include="Assets\Script\Data\playerdata\MedicineData.cs" />
    <Compile Include="Assets\Script\Message\Handler\SlotMachineHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\ServerDataAgent.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\GuideCenter.BornSite.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Advanced\HighlighterOccluder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityOrdnanceProcess.cs" />
    <Compile Include="Assets\Script\App\DebugScene.cs" />
    <Compile Include="Assets\SDK\Sky\Net\Socket2\NetProtocolTemplate.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\UltimateTalent\UIWindowUltimateTalentReset.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAIEffectLua.cs" />
    <Compile Include="Assets\Script\UI2\PlayerDisplay\Base\BasePlayerDisplayChangeSkin.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIWndWeaponLathe.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\PolyFewResetter.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\LuckyDrawTreasure\UIWndLuckyDrawTreasure.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIInterface.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZUtility.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBlendTree\AIMonsterBlendTree.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\AIMonsterLua.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\XLua\XLuaManager.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DPulsewave.cs" />
    <Compile Include="Assets\SDK\Sky\Util\TupleKeyDictionary.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\PerformanceTracker\Tracker\Building\BuildingTracker.cs" />
    <Compile Include="Assets\SDK\FG\net\networkmanager.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\WorldSpaceCanvas.cs" />
    <Compile Include="Assets\HybridCLRGenerate\AOTGenericReferences.cs" />
    <Compile Include="Assets\Script\Message\Handler\handlermanager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_GetBounds.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\ImportOptions.cs" />
    <Compile Include="Assets\SDK\Sky\Db\TableRecordRowTxt.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Interface\IProcess.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\BuildingNet.cs" />
    <Compile Include="Assets\Script\Data\playerdata\EDataNetExchange.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\PrefabInstance.cs" />
    <Compile Include="Assets\Script\Message\Handler\PopMsgHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Engine.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\VRSettingsUI.cs" />
    <Compile Include="Assets\Script\Thermodynamic\Pages\ThermoSectorCheckPage.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MainMenu\UIWndMainMenu.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Examples\Code\F3DBurnoutExample.cs" />
    <Compile Include="Assets\post\BloomAndColorEffect.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\testfps.cs" />
    <Compile Include="Assets\Script\Data\playerdata\MailData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Utils\StringZipUtils.cs" />
    <Compile Include="Assets\Script\Model\CommonCheckTrigger.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\BuildableGridManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Area\SectorSwitchMonoEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\EffectLOD\EffectLOD.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Closet\UIItemClosetCollocation.cs" />
    <Compile Include="Assets\XLua\Src\DelegateBridge.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\Point.cs" />
    <Compile Include="Assets\Script\UI2\Common\UICommonGetItems.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\DrawingTable\UIDrawingTable.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Building\PlayerEventHandler.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\NumberFormatInfoEx.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Unity\TextMeshProExtensions.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Particles.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonSMBWalk.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Dataset\ModelUtil.cs" />
    <Compile Include="Assets\Script\Tools\FogSetting.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\CharacterCustomization\MB_SwapShirts.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Directables\Clips\Audio\PlayAudio.cs" />
    <Compile Include="Assets\HighlightingSystemDemo\Scripts\Service\CameraFlyController.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.1.- Third Person Controller\Scripts\ThirdPersonController.cs" />
    <Compile Include="Assets\Script\Message\Handler\SysVendingHandler.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Dataset\Triangulator.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Installation.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.5.- Dash\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Chat\UITeamApplicantItem.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\COMMON\CommonDefine.cs" />
    <Compile Include="Assets\Script\Module\Character\CharacterAvatar.cs" />
    <Compile Include="Assets\Script\Data\playerdata\NewbieChallengeData.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DTime.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\LoopScrollDataSource.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Contracts\GuideCenterData.cs" />
    <Compile Include="Assets\XLua\Src\GenericDelegateBridge.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMalbersInputSystem.cs" />
    <Compile Include="Assets\Script\Data\playerdata\MaterialPointData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\SPCRJointDynamicsFashion.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateExpression.cs" />
    <Compile Include="Assets\Script\FZBPush\FZBDebug\FZBModify.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Tween\TweenColor.cs" />
    <Compile Include="Assets\Script\UI2\IntenseMode\UIWndIntenseMode.cs" />
    <Compile Include="Assets\SDK\Sky\Mission\MissionNpc.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Component\GuiProgressText.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\CommonAIBehaviour\CommonHeadTurn.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\UICheckFastButton.cs" />
    <Compile Include="Assets\Poco\Utils\HierarchyTranslator.cs" />
    <Compile Include="Assets\Script\Message\Handler\MaterialPointHandler.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Dash.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Mineral\CollectionResManager.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIItemBagBaseGroup.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\TerritoryLevel\UIWndTerritoryLevel.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Mouse\MouseSplit.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\SkeletonNames.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DBeam.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\AIBehaviour\AIMonsterRandomBehavior.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Bind\UIAutoBindAttribute.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\MathUtil\MathUtility.cs" />
    <Compile Include="Assets\XLua\Src\StaticLuaCallbacks.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleAudio.cs" />
    <Compile Include="Assets\Script\UI2\Passport\UIWndPassportGetReward.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\MultiObjectImporter.cs" />
    <Compile Include="Assets\SDK\Sky\Tool\CharacterController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\LinearSceneQuadTree.cs" />
    <Compile Include="Assets\SDK\KDTree\KDGridController.cs" />
    <Compile Include="Assets\Script\UI2\Shop\UIMonthCardPanel.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\MB3_BoneWeightCopier.cs" />
    <Compile Include="Assets\Poco\3rdLib\ZString\Number\Number.DiyFp.cs" />
    <Compile Include="Assets\Script\UI2\Common\UIWndDescription.cs" />
    <Compile Include="Assets\SDK\MeshBaker\Examples\SkinnedMeshRenderer\MB_SkinnedMeshSceneController.cs" />
    <Compile Include="Assets\Script\UI2\Common\PropItemDetailShow.cs" />
    <Compile Include="Assets\Script\Message\Handler\CommunityHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Math\DQuaternion.cs" />
    <Compile Include="Assets\SDK\zlib\ZlibTool.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Drop\DropVar.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\3.- Events\3.2.- Character Controller Events\Scripts\MyCharacterController.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\MalbersAnimationExtend\FgMountHp.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Ride\RideController.cs" />
    <Compile Include="Assets\PolyFew\Scripts\NonEditor\DataContainer.cs" />
    <Compile Include="Assets\Script\Message\Handler\BattleTaskHandler.cs" />
    <Compile Include="Assets\SDK\KDTree\KDBounds.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\Task\Runner\BattleRunner.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\Tree\SceneTree.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Scripts\EnviroEvents.cs" />
    <Compile Include="Assets\Best HTTP (Pro)\Examples\LitJson\JsonException.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\HiZMgr.cs" />
    <Compile Include="Assets\ActionEditor\Runtime\Attributes\Attributes.cs" />
    <Compile Include="Assets\Script\UI2\Recharge\UIMemberSignInPanel.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Feature\AIFeatureTame.cs" />
    <Compile Include="Assets\SDK\Sky\Algorithm\Pool\PoolMemory.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\GUI\Action\GuiActionTime.cs" />
    <Compile Include="Assets\Script\Token\Survival\StateMeleeChisel.cs" />
    <Compile Include="Assets\Poco\TcpServer.cs" />
    <Compile Include="Assets\Script\Message\Handler\VendingMachineHandler..cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DAudioController.cs" />
    <Compile Include="Assets\Script\Data\LangTemplateEn.cs" />
    <Compile Include="Assets\Script\UI2\Common\TaskWinQueue.cs" />
    <Compile Include="Assets\SDK\Shatter\Helpers\Game Objects\PieceRemover.cs" />
    <Compile Include="Assets\Script\UI2\Guild\UIWndGuildNominate.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DProjectile.cs" />
    <Compile Include="Assets\PolyFew\ThirdParty\AsImpL\Scripts\Dataset\ObjectBuilder.cs" />
    <Compile Include="Assets\UIParticles\Scripts\UiParticles.cs" />
    <Compile Include="Assets\SDK\KDTree\KDNode.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Sound\BattleSoundManager.cs" />
    <Compile Include="Assets\Enviro - Dynamic Enviroment\Weather\Scripts\ScanFeature.cs" />
    <Compile Include="Assets\Script\Data\playerdata\RechargeSevenDayData.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Transformer\TransformerManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\TrainingGround\TargetObjectManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MultiplayerDistanceListener.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\Model\ModelBase.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Guide\GuideCenter\Worlds\GuideWorldPointControl.cs" />
    <Compile Include="Assets\Script\Data\playerdata\ComfortData.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\MaterialPoint\UIItemMaterialPoint.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\MagicaCloth\PresetSphereCollider.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_UI_Joystick.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Hunting\UIWindowHuntingName.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Examples\5.- Gameplay\5.12.- Change Gravity Direction\Scripts\MyCharacter.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\F3DRift.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Runtime\ScriptableObject\SPCRJointDynamicsPointData.cs" />
    <Compile Include="Assets\Script\Model\CharacterMovement\Characters\FirstPersonCharacter.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UICurrentResonanceWnd.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Hull\FastHull.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\AIMonster\Core\Interface\IAISkillNetLua.cs" />
    <Compile Include="Assets\Script\Message\Handler\AdGainHandler.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\TextSpacing.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Facility\FacilityBujiXiangzi.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\WorldMap\BuildableGridVisualizerEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\LayerManager.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Runtime\ActionDirector.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\KillStreakLeaderboard\UIWindowKillStreakLeaderboard.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Runtime\MeshCombiner.cs" />
    <Compile Include="Assets\Script\Occlusion\Fix64.cs" />
    <Compile Include="Assets\SDK\MIConvexHull\ConvexHullCreationResultOutcome.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\DecalCount.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIItemBagAttr.cs" />
    <Compile Include="Assets\Script\UI2\PersonalInfo\UIWindowChangePortrait.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIModuleEntryItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIMaterialEntryTextItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIOriginalEntryItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEquipEntryItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Lathe\UIEntryReplaceWnd.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIEntryLibraryItem.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIEntryLibraryWnd.cs" />
    <Compile Include="Assets\Script\UI2\Bag\UIEntryItem.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Dungeon\UIItemDungeon.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Dungeon\DungeonTeamControl.cs" />
    <Compile Include="Assets\Script\UI2\Common\Test\SimpleDungeonTester.cs" />
    <Compile Include="Assets\Script\Message\Handler\DungeonHandler.cs" />
    <Compile Include="Assets\Script\UI2\SurvivalBattle\Dungeon\UIWindowDungeon.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\AssetBundle\Shaders\UIImage.shader" />
    <None Include="Assets\link.xml" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_mask(uv_polarcoord)_distortion.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\Main.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_Dispersion.shader" />
    <None Include="Assets\AssetBundle\Shaders\MobileParticlesAdditive.shader" />
    <None Include="Assets\XLua\Tutorial\LoadLuaScript\ByFile\Resources\byfile.lua.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro.cginc" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Ramp.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI112.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\reflectionMask.shader" />
    <None Include="Assets\AssetBundle\Shaders\UPRMeteorite.shader" />
    <None Include="Assets\UIParticles\Shaders\Ui Particle Multiply.shader" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\ShaderLibrary\ShaderLibrary.txt" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardInput.cginc" />
    <None Include="Assets\AssetBundle\Shaders\UPRRivers.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Packages\ReadMe.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\niu.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Scripts\MegaSplat Integration\MegaSplat Integration.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_Visualization_Channel.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\golss.shader" />
    <None Include="Assets\RawData\Effect\sc_SHUI\WaterShader.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI113.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI111.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityDinosaurPark.lua.txt" />
    <None Include="Assets\BuildReport\CustomBuildScriptExample.txt" />
    <None Include="Assets\EasyRoads3D\shaders\EasyRoads3D-Surface-Offset.shader" />
    <None Include="Assets\GPUSkinning\Shaders\URPGPUSkinningDiffuse.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI1001.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI135.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityPyramid.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_SimpleLOD.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_4.shader" />
    <None Include="Assets\Ultimate Game Tools\MeshSimplify\Data\Shaders\StandardSpecularTwoSided.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI128.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\quanxi.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI106.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\AuroraAnimation.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI124.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPStoneLOD.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI1000.lua.txt" />
    <None Include="Assets\GPUSkinning\Shaders\GPUSkinningSamplerEditor_UnlitColor.shader" />
    <None Include="Assets\AmplifyShaderEditor\CreatingTerrainsWithASE.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\Class.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuibei.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Resources\Light.cginc" />
    <None Include="Assets\AssetBundle\Shaders\URPSimplePlayer.shader" />
    <None Include="Assets\EasyRoads3D\shaders\ER Road Decal.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\Ultimate Game Tools\MeshSimplify\Data\Shaders\Wireframe.shader" />
    <None Include="Assets\AssetBundle\Shaders\MobileBloom.shader" />
    <None Include="Assets\EasyRoads3D\Resources\custom prefabs\_custom prefabs.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\Shaders\StylizedWater_desktop_tesselation.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\BumpedSpecular_4.shader" />
    <None Include="Assets\AssetBundle\Shaders\UVScroll.shader" />
    <None Include="Assets\AssetBundle\Shaders\GrassCS.shader" />
    <None Include="Assets\EasyRoads3D\prefab meshes\_custom prefabs.txt" />
    <None Include="Assets\VolumetricFog\Resources\Shaders\CopyDepth.shader" />
    <None Include="Assets\AssetBundle\Shaders\Grass(srp).shader" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\Shaders\haishui.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI108.lua.txt" />
    <None Include="Assets\RawData\object\models\monster\Crocodile_02.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI100.lua.txt" />
    <None Include="Assets\EasyRoads3D\shaders\ER Dual Road Blend.shader" />
    <None Include="Assets\FORGE3D\Sci-Fi Effects\Shaders\Additive.shader" />
    <None Include="Assets\AssetBundle\Shaders\UI\UIEnviroment.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI140.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityHospital.lua.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_7.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\cginc\T2M_Variables.cginc" />
    <None Include="Assets\AssetBundle\Shaders\Weather Effects.shader" />
    <None Include="Assets\Easy Character Movement 2\Docs\Version 1.1.0 Changes.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Show_41_Stars.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuilang.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\quanxi 1.shader" />
    <None Include="Assets\AssetBundle\Font\comm_text.txt" />
    <None Include="Assets\AssetBundle\Shaders\MobileBumpedDiffuse.shader" />
    <None Include="Assets\UIParticles\Shaders\Ui Particle Add.shader" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_SkinStocking.shader" />
    <None Include="Assets\UIParticles\version.txt" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardShadow.cginc" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Mask.shader" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\xlua\util.lua.txt" />
    <None Include="Assets\Scene\shader\WuLin_Grass_LerpHide.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPSimplePlayer_Galaxy.shader" />
    <None Include="Assets\AssetBundle\Shaders\TreeHLod.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI146.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPTransparent.shader" />
    <None Include="Assets\AssetBundle\Shaders\UIFlow.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Disslove.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\chuizi 1.shader" />
    <None Include="Assets\SDK\MeshBaker\Resources\Shaders\AlbedoShader.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Resources\Light.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\bianyuanguang 1.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPSimpleHair.shader" />
    <None Include="Assets\AssetBundle\Shaders\UPRDistortionOut.shader" />
    <None Include="Assets\AssetBundle\Shaders\BreathingLight.shader" />
    <None Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\Res\GpuFrustumCulling.compute" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_ramp_displament.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\kejihuan.shader" />
    <None Include="Assets\StompyRobot\Readme.txt" />
    <None Include="Assets\EasyRoads3D\Release - Install Notes.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_displament_mask(uv_polarcoord)_disslove_fre.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityWeatherArea.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\chewu_L.shader" />
    <None Include="Assets\SDK\MeshBaker\Release Notes.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIDefine.lua.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\BumpedSpecular_2.shader" />
    <None Include="Assets\RawData\object\models\Volumes\Lux URP FX SphereVolume.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuiwenli.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI118.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\kejiyuanzhu.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI133.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv.shader" />
    <None Include="Assets\AssetBundle\Shaders\UI\UIBreath.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Lenfares\LensFlare.shader" />
    <None Include="Assets\Demigiant\DOTween\DOTween.XML" />
    <None Include="Assets\AssetBundle\Shaders\Particle Alpha Blend.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_3.shader" />
    <None Include="Assets\AssetBundle\Shaders\PW_Water_Vars.cginc" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Weather\Shaders\GeometryRain.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\rongjie_BD.shader" />
    <None Include="Assets\AssetBundle\Shaders\Particle Add.shader" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\Templates\Universal\PBR.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_twomaintex_addnoise_displament_mask(uv_polarcoord).shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_WomanBody40_01_Show_aixin.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI134.lua.txt" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Rain\Rain.shader" />
    <None Include="Assets\BuildReport\README.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPTerrainTest.shader" />
    <None Include="Assets\GPUSkinning\Shaders\GPUSkinningSamplerEditor_Grid.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI142.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI132.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPDiffuse.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\init.lua.txt" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardConfig.cginc" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Mask_Disslove.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI121.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPPlayer_Galaxy.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Fres.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPRainbow.shader" />
    <None Include="Assets\AssetBundle\Shaders\ui_gray.shader" />
    <None Include="Assets\Easy Character Movement 2\Docs\Release Notes.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI110.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI109.lua.txt" />
    <None Include="Assets\Ultimate Game Tools\MeshSimplify\!Readme.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\AssetBundle\Shaders\DecalPoint.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPPlayer_laser.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_mask(uv_polarcoord)_disslove.shader" />
    <None Include="Assets\AssetBundle\Shaders\DecalSquare.shader" />
    <None Include="Assets\I2\Localization\I2 Localization - Readme.txt" />
    <None Include="Assets\AssetBundle\Shaders\UPREnergyShield.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shui.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPTree.shader" />
    <None Include="Assets\AssetBundle\Shaders\UI\UIRadiation.shader" />
    <None Include="Assets\EasyRoads3D\lib\EasyRoads3Dv3.dll" />
    <None Include="Assets\RawData\Effect\fajittu\shader\yu_jiemian 1.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_ink.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\keji.shader" />
    <None Include="Assets\AssetBundle\Shaders\DecalFire.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Ramp_Mask_AlphaMask.shader" />
    <None Include="Assets\AssetBundle\Shaders\UI\UIVertigo.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI1002.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_SubUV.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Scripts\UBER Integration\UBER Integration.txt" />
    <None Include="Assets\AssetBundle\Shaders\Sandstorm.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\EnterView\EnterViewBase.lua.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\BumpedSpecular_3.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_Ramp_UvAnimation.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPDiffuse(NOSRP).shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI137.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\WaterSurface.shader" />
    <None Include="Assets\RawData\Skybox\CubeMaterial\SkyCube.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\Shaders\StylizedWater_desktop.shader" />
    <None Include="Assets\AssetBundle\Shaders\MobileDiffuseColor.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityUndergroundWareHouse.lua.txt" />
    <None Include="Assets\EasyRoads3D\shaders\ER Single Road Blend.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIPSkillDefine.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\FloatingWordText.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityConfig.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI115.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_mask(uv_polarcoord).shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Disslove_two.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPTerrainLOD.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\version.txt" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Scripts\Playmaker Integration\Playmaker Integration.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPGodRaysBlinking.shader" />
    <None Include="Assets\AssetBundle\Shaders\GrassCS2.shader" />
    <None Include="Assets\SPCRJointDynamics\link.xml" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Update Notes.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_2.shader" />
    <None Include="Assets\OverdrawMonitor\Resources\OverdrawParallelReduction.compute" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIBase.lua.txt" />
    <None Include="Assets\HybridCLRGenerate\link.xml" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\Templates\SRP (Legacy)\README.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI999.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPSnowStorm.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI144.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\Bingdong_vogeo.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityWalkerBase.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\PW_Water_Funcs.cginc" />
    <None Include="Assets\RawData\Effect\fajittu\shader\kejiyuanzhu3.shader" />
    <None Include="Assets\AssetBundle\Shaders\UPRDoorEffect.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\waterfall.shader" />
    <None Include="Assets\AQUAS 2020\Readme.txt" />
    <None Include="Assets\AssetBundle\Shaders\DecalCut.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityFireCave.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\XRay.shader" />
    <None Include="Assets\AssetBundle\Shaders\m_tree.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Show_41_Gem.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\cginc\T2M_ODL.cginc" />
    <None Include="Assets\RTOcclusionCulling\ChangeLog.txt" />
    <None Include="Assets\PolyFew\BatchFew\BatchFewStandardSpecular.shader" />
    <None Include="Assets\EasyRoads3D\shaders\EasyRoads3D - Unity 5 StandardSpecular.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIPlayer\AIPlayer101.lua.txt" />
    <None Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Shader_CarBody.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI103.lua.txt" />
    <None Include="Assets\Resources\xlua\GameMain.lua.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\RawData\Effect\fajittu\pinmuyu\yu.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Documentation.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_8.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPIceBlock.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI117.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\UPRLake.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_disslove.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI122.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIInterface.lua.txt" />
    <None Include="Assets\EasyRoads3D\lib\DelaunayER.dll" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\perf\profiler.lua.txt" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Weather\Shaders\ScanHeight.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI136.lua.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_displament.shader" />
    <None Include="Assets\AmplifyShaderEditor\ChangeLog.txt" />
    <None Include="Assets\AssetBundle\Shaders\ImageBrightness.shader" />
    <None Include="Assets\Script\EditorCoroutines\Readme - v1.1.0.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF.shader" />
    <None Include="Assets\VolumetricFog\Resources\Shaders\VolumetricFog.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPCyberpunk.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuiwang 2-1.shader" />
    <None Include="Assets\ParrelSync\VERSION.txt" />
    <None Include="Assets\BuildReport\license.txt" />
    <None Include="Assets\Demigiant\DOTween\DOTween.dll" />
    <None Include="Assets\AssetBundle\Shaders\UPRHair.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\kuosan.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Scripts\Lux Integration\Lux Integration.txt" />
    <None Include="Assets\AssetBundle\Shaders\TreeLod-Billboard.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\RawData\Effect\sc_SHUI\WaterSurfaceDX12.shader" />
    <None Include="Assets\EasyRoads3D\shaders\ER Road Transparency.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\ui_blend_uv.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPTerrain.shader" />
    <None Include="Assets\AssetBundle\Shaders\Skybox_Galaxy.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Scripts\VacuumShaders.TerrainToMesh.dll" />
    <None Include="Assets\RawData\Effect\fajittu\shader\hongdian.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI123.lua.txt" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\Templates\Universal\2DLit.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\wuqifum.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\jingtidun.shader" />
    <None Include="Assets\AssetBundle\Shaders\PW_GeneralVars.cginc" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\Templates\Universal\Unlit.shader" />
    <None Include="Assets\UIParticles\Shaders\Ui Glow Additive Simple.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shui_S.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AIScriptTemplate.txt" />
    <None Include="Assets\AssetBundle\Shaders\computeShader\GrassCompute.compute" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI145.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\Mobile-Actor.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI101.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_displament_mask(uv_polarcoord).shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\biaojiao.shader" />
    <None Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\Res\GenerateMipmap.compute" />
    <None Include="Assets\AssetBundle\Shaders\DecalSector.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Ramp_Mask.shader" />
    <None Include="Assets\BuildReport\VERSION.txt" />
    <None Include="Assets\post\FastBloom.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPStaticObject.shader" />
    <None Include="Assets\AssetBundle\Shaders\FluXay.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPVertexColor.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_Realism_Water.shader" />
    <None Include="Assets\SDK\MeshBaker\Resources\Shaders\NormalMapShader.shader" />
    <None Include="Assets\AssetBundle\Shaders\stars.shader" />
    <None Include="Assets\AssetBundle\Shaders\WaterSea.shader" />
    <None Include="Assets\AssetBundle\Shaders\Decal.shader" />
    <None Include="Assets\RawData\Effect\sc_SHUI\pubu.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\EnterView\EnterViewSnake.lua.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_6.shader" />
    <None Include="Assets\Easy Character Movement 2\README.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI131.lua.txt" />
    <None Include="Assets\EasyRoads3D\Beta Release - Install Notes.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\UtilsFunc.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPSlime.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\UnityUtils.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPSimpleTerrain.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_RGB_Noise.shader" />
    <None Include="Assets\AssetBundle\Shaders\Scanline.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Weather\Shaders\Rain.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\FlowingGlow.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Weather\Shaders\SandStorm.shader" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\perf\memory.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI107.lua.txt" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardCoreForward.cginc" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_displament_disslove.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\niu1.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\cginc\T2M_Unlit.cginc" />
    <None Include="Assets\AssetBundle\Shaders\URPSnowStormBillboard.shader" />
    <None Include="Assets\EasyRoads3D\_Readme.txt" />
    <None Include="Assets\AssetBundle\Shaders\Moon.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPTerrainDebugTest.shader" />
    <None Include="Assets\XLua\Gen\link.xml" />
    <None Include="Assets\RawData\Effect\fajittu\shader\chewu.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\AssetBundle\Shaders\Aurora.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPFlicker.shader" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_Chiffon.shader" />
    <None Include="Assets\EasyRoads3D\shaders\EasyRoads3D - Unity 5 Standard.shader" />
    <None Include="Assets\AmplifyShaderEditor\Readme.txt" />
    <None Include="Assets\AssetBundle\Shaders\UPRDistortionWhirlpool.shader" />
    <None Include="Assets\GPUSkinning\Shaders\GPUSkinningInclude.hlsl" />
    <None Include="Assets\StompyRobot\SROptions\link.xml" />
    <None Include="Assets\RawData\Effect\fajittu\shader\pili.shader" />
    <None Include="Assets\AQUAS 2020\ReflectionWater\Reflection.shader" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardMeta.cginc" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityMachineFactory.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\ui\xingshuo\wanchen.shader" />
    <None Include="Assets\EasyRoads3D\shaders\EasyRoads3D - Unity 2018 Standard.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI119.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\UPRGlass.shader" />
    <None Include="Assets\AssetBundle\Shaders\THOR_LightningBolt.shader" />
    <None Include="Assets\AssetBundle\Shaders\FlowingGlowByMaskChannel.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIInit.lua.txt" />
    <None Include="Assets\GPUSkinning\Shaders\GPUSkinningSurface.hlsl" />
    <None Include="Assets\PolyFew\BatchFew\BatchFewStandard.shader" />
    <None Include="Assets\RawData\Effect\fajittu\pinmuyu\yu_jiemian.shader" />
    <None Include="Assets\AssetBundle\Shaders\MobileDiffuse.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\WaterShader.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIPlayer\AIPlayer100100.lua.txt" />
    <None Include="Assets\RawData\Effect\sc_SHUI\FastWaterShader.shader" />
    <None Include="Assets\AssetBundle\Shaders\UPREquipShield.shader" />
    <None Include="Assets\AssetBundle\Shaders\DecalCircle.shader" />
    <None Include="Assets\AssetBundle\Shaders\ExplosionWarning.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_ui_Base_Ramp.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPSkybox.shader" />
    <None Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\Res\Procedural.cginc" />
    <None Include="Assets\SDK\MeshBaker\README.txt" />
    <None Include="Assets\AssetBundle\Shaders\MobileGunEffect.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\huowen.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI125.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI116.lua.txt" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardCore.cginc" />
    <None Include="Assets\AssetBundle\Shaders\URPSpecular.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPOutline.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPSprite.shader" />
    <None Include="Assets\RawData\Effect\fajittu\Materials\duye.shader" />
    <None Include="Assets\AssetBundle\Shaders\Aniso_Kajiya_bump.shader" />
    <None Include="Assets\AssetBundle\Shaders\Ghost.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh (SRP)\ReadMe.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\wuli.shader" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardUtils.cginc" />
    <None Include="Assets\AssetBundle\Shaders\URPPlayer.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPSCI-FIShow.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\cginc\T2M_Deferred.cginc" />
    <None Include="Assets\AssetBundle\Shaders\THOR_LightningCloud.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Bumped_3.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityInterface.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPRealWater.shader" />
    <None Include="Assets\AssetBundle\Shaders\PbrFunction.hlsl" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_Scene_uv.shader" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardCoreForwardSimple.cginc" />
    <None Include="Assets\AssetBundle\Shaders\DepthNormals.shader" />
    <None Include="Assets\Demigiant\DOTween\readme.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Common_Wave.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\Shaders\StylizedWater_mobile_advanced.shader" />
    <None Include="Assets\UIParticles\Shaders\UI Particle Alpha Blend.shader" />
    <None Include="Assets\AssetBundle\Shaders\URPGodRaysBlinkingBillboarded.shader" />
    <None Include="Assets\RawData\Materials\mask.shader" />
    <None Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\Res\GenerateDepthRT.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\saoguang 1.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI129.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\rongjiang 1.shader" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIScriptBase.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI104.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI126.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\TreeLod0.shader" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Weather\Shaders\PPRain.shader" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\LuaPanda.lua.txt" />
    <None Include="Assets\Resources\xlua\HotFix\xLuaTest.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI120.lua.txt" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Bumped_2.shader" />
    <None Include="Assets\ActionEditor\Runtime\ThirdParty\FullSerializer.dll" />
    <None Include="Assets\post\BloomAndColorEffect.shader" />
    <None Include="Assets\PolyFew\BatchFew\Includes\BatchFewStandardBRDF.cginc" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI138.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuiwang 1.shader" />
    <None Include="Assets\DynamicBone\ReadMe.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPShadow.shader" />
    <None Include="Assets\AssetBundle\Shaders\FluctationSequence.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Diffuse_5.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\UV.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\saoguang.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\bianwenqiu.shader" />
    <None Include="Assets\AssetBundle\Shaders\CullingDiffuseNoUrp.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI127.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIManager.lua.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\shuijianwen.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shui\shuijian\Shaders\StylizedWater_mobile_basic.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\Main.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_Realism_tree.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI139.lua.txt" />
    <None Include="Assets\Enviro - Dynamic Enviroment\Scripts\RTP Integration\RTP Integration.txt" />
    <None Include="Assets\GPUSkinning\Shaders\GPUSkinningSamplerEditor_LinearToGamma.shader" />
    <None Include="Assets\AssetBundle\Shaders\Road.shader" />
    <None Include="Assets\AssetBundle\Shaders\StylizedRendering.shader" />
    <None Include="Assets\SGC\Unity\Lua\Activity\ActivityMachineFactory2.lua.txt" />
    <None Include="Assets\SGC\Unity\Lua\framework\ai\AIPlayerBase.lua.txt" />
    <None Include="Assets\Realistic Car Controller Pro\Scripts\RCCP_Shader_Skidmarks.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\bianyuanguang.shader" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\tdr\tdr.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_Diamond.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI105.lua.txt" />
    <None Include="Assets\RawData\Effect\fajittu\shader\huowen _1.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\daoguang.shader" />
    <None Include="Assets\XLua\CHANGELOG.txt" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR.shader" />
    <None Include="Assets\AssetBundle\Shaders\decal_show.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_ui_Base_Ramp_Mask.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI114.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\ui_blend_uv_mask(uv_polarcoord).shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI147.lua.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Shaders\Legacy Shaders\Bumped_4.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_Ramp_mask(uv_polarcoord)_fres.shader" />
    <None Include="Assets\AssetBundle\Shaders\PBR_MAR_Silk.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\xinpian.shader" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Ramp_Mask_Disslove.shader" />
    <None Include="Assets\RawData\Effect\fajittu\pinmuyu\shandian.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\kejiyuanzhu2.shader" />
    <None Include="Assets\RawData\Effect\sc_SHUI\WaterSurfaceDX11.shader" />
    <None Include="Assets\EasyRoads3D\prefab sources\_custom prefabs.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\blend_uv_displament_smoke.shader" />
    <None Include="Assets\RawData\Effect\fajittu\shader\_dun.shader" />
    <None Include="Assets\AssetBundle\Shaders\DecalLandmine.shader" />
    <None Include="Assets\RawData\Effect\Caustics.shader" />
    <None Include="Assets\RawData\Effect\sc_SHUI\SimpleWaterShader.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI102.lua.txt" />
    <None Include="Assets\Easy Character Movement 2\Release Notes.txt" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI141.lua.txt" />
    <None Include="Assets\AssetBundle\Shaders\PW_GeneralFuncs.cginc" />
    <None Include="Assets\AssetBundle\Shaders\unlit-texture.shader" />
    <None Include="Assets\SGC\Unity\Lua\xluacore\Utils\LogUtil.lua.txt" />
    <None Include="Assets\AssetBundle\Effect3D\Shader\Blend_Base_Ramp_Disslove.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\AmplifyShaderEditor\Plugins\EditorResources\Templates\Universal\2DUnlit.shader" />
    <None Include="Assets\Malbers Animations\Common\Fonts\OpenSans\License!.txt" />
    <None Include="Assets\AssetBundle\Shaders\URPStone.shader" />
    <None Include="Assets\Script\SurvivalBattle\WorldMap\HIZCulling\Res\HizInstance.hlsl" />
    <None Include="Assets\AssetBundle\Shaders\Grass.shader" />
    <None Include="Assets\SGC\Unity\Lua\AIScripts\AI143.lua.txt" />
    <None Include="Assets\EasyRoads3D\lib\DelaunayER.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinValidator.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Demigiant\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="I18N">
      <HintPath>Assets\Plugins\I18N.dll</HintPath>
    </Reference>
    <Reference Include="EasyRoads3Dv3">
      <HintPath>Assets\EasyRoads3D\lib\EasyRoads3Dv3.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net">
      <HintPath>Assets\Plugins\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Unsafe">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Packages\com.unity.collections@1.3.1\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="DelaunayER">
      <HintPath>Assets\EasyRoads3D\lib\DelaunayER.dll</HintPath>
    </Reference>
    <Reference Include="FullSerializer">
      <HintPath>Assets\ActionEditor\Runtime\ThirdParty\FullSerializer.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.AOTGenerated">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\AOT\Sirenix.Serialization.AOTGenerated.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Pdb">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.0.2\Runtime\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="VacuumShaders.TerrainToMesh">
      <HintPath>Assets\VacuumShaders\Terrain To Mesh\Scripts\VacuumShaders.TerrainToMesh.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Plugins\protobuf-net.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\Plugins\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
    </Reference>
    <Reference Include="I18N.CJK">
      <HintPath>Assets\Plugins\I18N.CJK.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Mdb">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>Assets\Plugins\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats">
      <HintPath>Assets\Plugins\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>Assets\Plugins\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="Ude">
      <HintPath>Assets\Plugins\Ude.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="UWA_GPM">
      <HintPath>Assets\UWA\UWA_GPM\Runtime\ManagedLibs\UWA_GPM.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Rocks">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Rocks.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Cinemachine">
      <HintPath>Library\ScriptAssemblies\Cinemachine.dll</HintPath>
    </Reference>
    <Reference Include="com.unity.cinemachine.editor">
      <HintPath>Library\ScriptAssemblies\com.unity.cinemachine.editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj">
      <Project>{3A1B43A0-276E-34FB-197E-1ACDE9001A0F}</Project>
      <Name>Assembly-CSharp-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Runtime.csproj">
      <Project>{47D3AC7D-994B-FF43-7BD2-399821E768B0}</Project>
      <Name>HybridCLR.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Shaders.csproj">
      <Project>{24A3728F-9EC4-6FC4-F6AF-9F7B197997CA}</Project>
      <Name>Unity.RenderPipelines.Universal.Shaders</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRF.Editor.csproj">
      <Project>{11A79E45-962C-EBDF-05EC-54FE51EE8C4C}</Project>
      <Name>StompyRobot.SRF.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.Editor.csproj">
      <Project>{2E319F8D-9E1D-0A9A-B421-889026ABEECA}</Project>
      <Name>YooAsset.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="com.bartofzo.nativetrees.samples.csproj">
      <Project>{881DA922-C840-2495-D6F1-8361CFB31DD1}</Project>
      <Name>com.bartofzo.nativetrees.samples</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRDebugger.Editor.csproj">
      <Project>{4B8DC056-4E1B-382A-F1F1-16E4F448DBA5}</Project>
      <Name>StompyRobot.SRDebugger.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Sirenix.OdinInspector.Modules.UnityMathematics.csproj">
      <Project>{10DB790B-7E9A-5842-FBDE-7C0ACBB264BA}</Project>
      <Name>Sirenix.OdinInspector.Modules.UnityMathematics</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{B220812B-9F36-B090-CEA1-BBA27BEB3963}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="SPCRJointDynamics.csproj">
      <Project>{EA626DCB-547D-DC2C-ED04-916E485DEC52}</Project>
      <Name>SPCRJointDynamics</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.FSM.csproj">
      <Project>{69BC925E-6C80-301F-C49D-B0815D9CC38A}</Project>
      <Name>Animancer.FSM</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.TextMeshPro.csproj">
      <Project>{AA75BE1D-D383-01A4-15DC-B38CACF827E2}</Project>
      <Name>UniTask.TextMeshPro</Name>
    </ProjectReference>
    <ProjectReference Include="RideMalbersSystem.csproj">
      <Project>{31313ECF-AD69-E4E1-B4D0-7A0DCC86E057}</Project>
      <Name>RideMalbersSystem</Name>
    </ProjectReference>
    <ProjectReference Include="RideMalbersSystemEditor.csproj">
      <Project>{9EF83544-2A1B-89F5-C22F-0F2EC419539C}</Project>
      <Name>RideMalbersSystemEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Whinarn.UnityMeshSimplifier.Editor.csproj">
      <Project>{999D9AC0-0504-57C2-3551-7C7EA26D33B4}</Project>
      <Name>Whinarn.UnityMeshSimplifier.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="zstring.csproj">
      <Project>{183E02AF-BAB0-4091-376E-05D6D94D9C93}</Project>
      <Name>zstring</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipeline.Universal.ShaderLibrary.csproj">
      <Project>{6DF604D4-361A-B9F2-EB1F-784F47182D41}</Project>
      <Name>Unity.RenderPipeline.Universal.ShaderLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.csproj">
      <Project>{8505417A-FC25-BCB9-D556-603B8F4CD50F}</Project>
      <Name>Animancer</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.Editor.csproj">
      <Project>{A737B58D-C244-1FCB-7884-DF080DBEE2B2}</Project>
      <Name>Unity.Mathematics.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Collections.csproj">
      <Project>{92277F0B-731A-8F80-2073-C19E264C2B04}</Project>
      <Name>Unity.Collections</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.csproj">
      <Project>{66A4DF8A-E657-4C7A-BEA4-51B7AECF0597}</Project>
      <Name>YooAsset</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_gpm.csproj">
      <Project>{F1224434-5197-8351-C0E1-3B44EFD16B04}</Project>
      <Name>youhu.unity_uwa_gpm</Name>
    </ProjectReference>
    <ProjectReference Include="OffScreenParticleRendering.csproj">
      <Project>{B7331974-73C3-A19D-5E67-E90538624BCA}</Project>
      <Name>OffScreenParticleRendering</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Addressables.csproj">
      <Project>{C95E8050-8D17-81EA-FAB8-39CEBB1ABC10}</Project>
      <Name>UniTask.Addressables</Name>
    </ProjectReference>
    <ProjectReference Include="OverdrawMonitor.csproj">
      <Project>{2AFBFC97-5138-74DB-F1A5-CBA57ED95054}</Project>
      <Name>OverdrawMonitor</Name>
    </ProjectReference>
    <ProjectReference Include="Whinarn.UnityMeshSimplifier.Runtime.csproj">
      <Project>{923E1988-1640-D9A6-6E0F-0315D5A35B51}</Project>
      <Name>Whinarn.UnityMeshSimplifier.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Localization.csproj">
      <Project>{78D9E99F-E53F-BDCD-F36C-4CBCC6468FDA}</Project>
      <Name>Localization</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.Examples.csproj">
      <Project>{4D345168-ACC0-58B3-9CAB-44FC0409F3F7}</Project>
      <Name>Animancer.Examples</Name>
    </ProjectReference>
    <ProjectReference Include="WaterSystem.csproj">
      <Project>{B31A9E1B-C59F-4889-4BA1-E8BF4CF7A72E}</Project>
      <Name>WaterSystem</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj">
      <Project>{937E0ECA-6A9C-46C7-F2FF-0F16CC228C0C}</Project>
      <Name>Unity.RenderPipelines.Universal.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="PPv2URPConverters.csproj">
      <Project>{DF942A2F-4BCF-43AA-A508-9C7F5CA5CF45}</Project>
      <Name>PPv2URPConverters</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRDebugger.csproj">
      <Project>{59F2960F-44FD-5DC1-8CBF-85558885274E}</Project>
      <Name>StompyRobot.SRDebugger</Name>
    </ProjectReference>
    <ProjectReference Include="com.bartofzo.nativetrees.csproj">
      <Project>{FE16973C-C194-FC9A-C1F3-8F7ECF48D69F}</Project>
      <Name>com.bartofzo.nativetrees</Name>
    </ProjectReference>
    <ProjectReference Include="MalbersAnimations.Cinemachine.csproj">
      <Project>{13BDA4B4-D002-D150-1299-EA05B554F8AC}</Project>
      <Name>MalbersAnimations.Cinemachine</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_gpm.Editor.csproj">
      <Project>{A640743C-E7EE-1538-8CB2-0748CF5E6518}</Project>
      <Name>youhu.unity_uwa_gpm.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="SGCResComponent.csproj">
      <Project>{785EA918-9D8D-93CA-0458-C7FAE74B1B4D}</Project>
      <Name>SGCResComponent</Name>
    </ProjectReference>
    <ProjectReference Include="WaterSystem.Editor.csproj">
      <Project>{E6E9EB74-4F83-DE6C-4092-3B304B72BD58}</Project>
      <Name>WaterSystem.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.DOTween.csproj">
      <Project>{41F38BD8-5A7D-2DFB-C665-647B3BE73561}</Project>
      <Name>UniTask.DOTween</Name>
    </ProjectReference>
    <ProjectReference Include="MagicaCloth.Editor.csproj">
      <Project>{E81811E8-FCB9-4674-22EB-89FE0F736A68}</Project>
      <Name>MagicaCloth.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Linq.csproj">
      <Project>{C8E48634-E7D8-1F70-2E3E-F3339CF23ACD}</Project>
      <Name>UniTask.Linq</Name>
    </ProjectReference>
    <ProjectReference Include="MagicaCloth.csproj">
      <Project>{FD597BE5-A559-54A1-2E57-3D1D615853F1}</Project>
      <Name>MagicaCloth</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Editor.csproj">
      <Project>{F7AFCDAB-EB8C-50EA-AEB8-4F5402D7E818}</Project>
      <Name>HybridCLR.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="HotFixSystem.csproj">
      <Project>{FF2A6F0E-1821-838D-C66E-5782B98B2A8D}</Project>
      <Name>HotFixSystem</Name>
    </ProjectReference>
    <ProjectReference Include="UniRx.csproj">
      <Project>{57272F7A-F591-FE44-425A-8D5F90D3EFDF}</Project>
      <Name>UniRx</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.csproj">
      <Project>{1E4C0B2A-7147-7D8B-74DB-D77243411067}</Project>
      <Name>Unity.Mathematics</Name>
    </ProjectReference>
    <ProjectReference Include="ParrelSync.csproj">
      <Project>{4A3BCF82-86B9-2A78-FB08-9D6BA4CAD2CC}</Project>
      <Name>ParrelSync</Name>
    </ProjectReference>
    <ProjectReference Include="AmplifyShaderEditor.csproj">
      <Project>{B5B5FC54-5793-0FBB-F9AA-7885AFED430D}</Project>
      <Name>AmplifyShaderEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Editor.csproj">
      <Project>{21F18CB6-20D6-CED1-0542-3AA20B219CEC}</Project>
      <Name>Unity.RenderPipelines.Universal.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="EasyCharacterMovement.CharacterMovement.csproj">
      <Project>{EB9D1ECB-1020-127B-5F3F-E7FE89DD7AA5}</Project>
      <Name>EasyCharacterMovement.CharacterMovement</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.csproj">
      <Project>{2971DB37-05CE-F0FF-87E2-A8069DD40BC2}</Project>
      <Name>Unity.ScriptableBuildPipeline</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.Editor.csproj">
      <Project>{F049DAA6-13BD-E157-237A-D48707F847E4}</Project>
      <Name>Unity.ScriptableBuildPipeline.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.csproj">
      <Project>{B8F6F662-F773-4F2F-CC1A-C6AB1D3E2A6E}</Project>
      <Name>UniTask</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRF.csproj">
      <Project>{E2AAE656-27ED-8885-B163-9F425709F0EE}</Project>
      <Name>StompyRobot.SRF</Name>
    </ProjectReference>
    <ProjectReference Include="ProtocolData.csproj">
      <Project>{958E9035-E704-7965-9AE4-9CCA3A8355EA}</Project>
      <Name>ProtocolData</Name>
    </ProjectReference>
    <ProjectReference Include="OverdrawMonitor.Editor.csproj">
      <Project>{88593884-3C2D-16C4-0E56-67B2826F5683}</Project>
      <Name>OverdrawMonitor.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="SPCRJointDynamicsEditor.csproj">
      <Project>{1F36D130-8091-39AF-24A4-91F2E4B1C3D5}</Project>
      <Name>SPCRJointDynamicsEditor</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="GenerateTargetFrameworkMonikerAttribute" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
