Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker11
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker11.log
-srvPort
2710
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22064] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 421612179 [EditorId] 421612179 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [22064] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 421612179 [EditorId] 421612179 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 968.46 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56268
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002032 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 943.72 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.658 seconds
Domain Reload Profiling:
	ReloadAssembly (1659ms)
		BeginReloadAssembly (135ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1396ms)
			LoadAssemblies (133ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (132ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (1161ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (944ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (139ms)
				ProcessInitializeOnLoadMethodAttributes (68ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.019098 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 919.37 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.267 seconds
Domain Reload Profiling:
	ReloadAssembly (4269ms)
		BeginReloadAssembly (112ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (4057ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (529ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (209ms)
			SetupLoadedEditorAssemblies (3134ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (919ms)
				BeforeProcessingInitializeOnLoad (134ms)
				ProcessInitializeOnLoadAttributes (1632ms)
				ProcessInitializeOnLoadMethodAttributes (434ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 11.77 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10224 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10745.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 30.905900 ms (FindLiveObjects: 1.131800 ms CreateObjectMapping: 1.081300 ms MarkObjects: 27.836200 ms  DeleteObjects: 0.855300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 44.40 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10745.
Memory consumption went from 295.2 MB to 293.1 MB.
Total: 22.893500 ms (FindLiveObjects: 0.953500 ms CreateObjectMapping: 1.227300 ms MarkObjects: 19.950300 ms  DeleteObjects: 0.761400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 173517.860585 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '55da1db1bc70617d3f685da65ac8e8fe') in 0.129163 seconds 
========================================================================
Received Import Request.
  Time since last request: 41.879006 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32544548e75e897f42e6852efb7372c5') in 0.020862 seconds 
========================================================================
Received Import Request.
  Time since last request: 22.608203 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '99b0a5e00edeeb467e833f9d32606e51') in 0.004724 seconds 
========================================================================
Received Import Request.
  Time since last request: 17.734351 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd4744bd93c599544929a2e5b75b845de') in 0.005924 seconds 
========================================================================
Received Import Request.
  Time since last request: 16.455521 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab
  artifactKey: Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Item_Equip.prefab using Guid(70399fde1db37a54d82804d5d5518dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9d01bb7ce314807771fcce279596703d') in 0.005881 seconds 
========================================================================
Received Import Request.
  Time since last request: 25.278809 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0e8cdf643f866e005b9eefa36c83fd6a') in 0.019594 seconds 
========================================================================
Received Import Request.
  Time since last request: 13.293067 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '36b41f42a453d40e6298175ccde81a11') in 0.017780 seconds 
========================================================================
Received Import Request.
  Time since last request: 33.013749 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b32e82326af04662ed1bb350e0ee5485') in 0.020929 seconds 
========================================================================
Received Import Request.
  Time since last request: 35.519584 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '31feafbf3ae308d133022d0174564b22') in 0.018231 seconds 
========================================================================
Received Import Request.
  Time since last request: 3092.605217 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResult.prefab
  artifactKey: Guid(de47e0e52b1bbb548b176abdec23debb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_ForgeResult.prefab using Guid(de47e0e52b1bbb548b176abdec23debb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a82d8dcc349436a46e6faac65b03418e') in 0.011223 seconds 
========================================================================
Received Import Request.
  Time since last request: 15.886065 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Entry/Item_Entry.prefab
  artifactKey: Guid(75b2b33afbda41745a48f9151089537a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Entry/Item_Entry.prefab using Guid(75b2b33afbda41745a48f9151089537a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '139911d3583db4bc32778ce89b986743') in 0.005834 seconds 
========================================================================
Received Import Request.
  Time since last request: 101.304170 seconds.
  path: Assets/AssetBundle/CommonData/Language/I2Languages.prefab
  artifactKey: Guid(01f26d64160a4254080113a351c20037) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/CommonData/Language/I2Languages.prefab using Guid(01f26d64160a4254080113a351c20037) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ebb70d61c4daeb32b76c62fe00432014') in 0.551706 seconds 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 62.51 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10746.
Memory consumption went from 296.1 MB to 294.0 MB.
Total: 31.179500 ms (FindLiveObjects: 0.892300 ms CreateObjectMapping: 0.946300 ms MarkObjects: 28.620300 ms  DeleteObjects: 0.719200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 46.167794 seconds.
  path: Assets/AssetBundle/CommonData/Language/I2Languages.prefab
  artifactKey: Guid(01f26d64160a4254080113a351c20037) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/CommonData/Language/I2Languages.prefab using Guid(01f26d64160a4254080113a351c20037) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd97d30ce7838783906fc0da4f65a0c58') in 0.353970 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Entry/Item_Entry.prefab
  artifactKey: Guid(75b2b33afbda41745a48f9151089537a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Entry/Item_Entry.prefab using Guid(75b2b33afbda41745a48f9151089537a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aeb849452d21f13d633f28f3a7806041') in 0.006892 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016255 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.47 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.630 seconds
Domain Reload Profiling:
	ReloadAssembly (3631ms)
		BeginReloadAssembly (198ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (95ms)
		EndReloadAssembly (3317ms)
			LoadAssemblies (139ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (1228ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1751ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1331ms)
				ProcessInitializeOnLoadMethodAttributes (295ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 27.95 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9736 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10759.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 23.277700 ms (FindLiveObjects: 0.939600 ms CreateObjectMapping: 1.101500 ms MarkObjects: 20.503500 ms  DeleteObjects: 0.731700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 66.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10759.
Memory consumption went from 296.4 MB to 294.3 MB.
Total: 42.907100 ms (FindLiveObjects: 1.362100 ms CreateObjectMapping: 2.625600 ms MarkObjects: 37.971200 ms  DeleteObjects: 0.946100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018722 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.797 seconds
Domain Reload Profiling:
	ReloadAssembly (2798ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2537ms)
			LoadAssemblies (116ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (491ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1737ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1364ms)
				ProcessInitializeOnLoadMethodAttributes (248ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 41.94 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10777.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 57.688400 ms (FindLiveObjects: 1.220000 ms CreateObjectMapping: 1.621200 ms MarkObjects: 53.992100 ms  DeleteObjects: 0.853600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017047 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.98 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.154 seconds
Domain Reload Profiling:
	ReloadAssembly (3155ms)
		BeginReloadAssembly (182ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (83ms)
		EndReloadAssembly (2851ms)
			LoadAssemblies (131ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (600ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (109ms)
			SetupLoadedEditorAssemblies (1898ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1515ms)
				ProcessInitializeOnLoadMethodAttributes (263ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 37.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 38.382400 ms (FindLiveObjects: 1.461600 ms CreateObjectMapping: 1.931800 ms MarkObjects: 34.162900 ms  DeleteObjects: 0.824400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 64.63 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10794.
Memory consumption went from 296.4 MB to 294.4 MB.
Total: 38.967600 ms (FindLiveObjects: 3.132300 ms CreateObjectMapping: 2.205500 ms MarkObjects: 32.621300 ms  DeleteObjects: 1.007200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 2771.549449 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipSelect.prefab
  artifactKey: Guid(25d0c9a88701c76489a0e93b0f58e7f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipSelect.prefab using Guid(25d0c9a88701c76489a0e93b0f58e7f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '70a6c8220f93d0985117e4fe822e1f10') in 0.145859 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017222 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 13.69 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.526 seconds
Domain Reload Profiling:
	ReloadAssembly (4528ms)
		BeginReloadAssembly (398ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (266ms)
		EndReloadAssembly (4015ms)
			LoadAssemblies (142ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (551ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (3095ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (14ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (2632ms)
				ProcessInitializeOnLoadMethodAttributes (336ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (33ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 39.44 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10811.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 39.402100 ms (FindLiveObjects: 1.279400 ms CreateObjectMapping: 1.676300 ms MarkObjects: 35.227200 ms  DeleteObjects: 1.217700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016822 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.74 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.572 seconds
Domain Reload Profiling:
	ReloadAssembly (2573ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2310ms)
			LoadAssemblies (124ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (468ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1543ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1211ms)
				ProcessInitializeOnLoadMethodAttributes (220ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 35.70 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10828.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 30.598100 ms (FindLiveObjects: 1.215500 ms CreateObjectMapping: 1.101200 ms MarkObjects: 27.510200 ms  DeleteObjects: 0.769300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 67.35 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10828.
Memory consumption went from 296.8 MB to 294.8 MB.
Total: 94.648600 ms (FindLiveObjects: 2.036700 ms CreateObjectMapping: 1.423400 ms MarkObjects: 90.143000 ms  DeleteObjects: 1.044000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016083 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  4.743 seconds
Domain Reload Profiling:
	ReloadAssembly (4744ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (4485ms)
			LoadAssemblies (118ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (476ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1709ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (14ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (9ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1308ms)
				ProcessInitializeOnLoadMethodAttributes (280ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1996ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 28.95 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10845.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 21.755800 ms (FindLiveObjects: 1.010400 ms CreateObjectMapping: 0.976600 ms MarkObjects: 19.139600 ms  DeleteObjects: 0.627600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018654 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 8.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  2.762 seconds
Domain Reload Profiling:
	ReloadAssembly (2763ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2489ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (494ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (110ms)
			SetupLoadedEditorAssemblies (1676ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (8ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1330ms)
				ProcessInitializeOnLoadMethodAttributes (227ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (24ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 40.64 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9737 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10862.
Memory consumption went from 0.55 GB to 0.55 GB.
Total: 36.204400 ms (FindLiveObjects: 1.746400 ms CreateObjectMapping: 1.380700 ms MarkObjects: 32.131200 ms  DeleteObjects: 0.944700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 49.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10862.
Memory consumption went from 296.9 MB to 294.8 MB.
Total: 24.609500 ms (FindLiveObjects: 1.169600 ms CreateObjectMapping: 1.271100 ms MarkObjects: 21.533100 ms  DeleteObjects: 0.634500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 3209.173730 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd59eb03f2d40ca8044fa8ae435419035') in 0.099305 seconds 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 50.92 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 171 unused Assets / (2.0 MB). Loaded Objects now: 10862.
Memory consumption went from 297.1 MB to 295.1 MB.
Total: 80.324100 ms (FindLiveObjects: 2.365000 ms CreateObjectMapping: 0.931100 ms MarkObjects: 76.308300 ms  DeleteObjects: 0.718800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
