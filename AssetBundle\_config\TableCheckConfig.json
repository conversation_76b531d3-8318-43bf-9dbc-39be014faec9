﻿{"TblSkinShow":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"SkinID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSkin","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"SkinID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":true,"CheckTblName":"TblSkinShow","CheckTblField":"ModelPath1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":true,"CheckTblName":"TblSkinShow","CheckTblField":"ModelPathHD1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"ModelPath2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"ModelPathHD2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"SightModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSkinShow","CheckTblField":"MagazineModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblSkin":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSkin","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSkinShow","TargetTblField":"SkinID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSkin","CheckTblField":"SuitID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSuit","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"IsSuit","FieldValues":[1,2]},"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":true,"CheckTblName":"TblSkin","CheckTblField":"ShowPic","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1]},"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/Itemicon/ItemiconBig/{0}.png"}],"TblItem":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblItem","CheckTblField":"Type","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItemType","TargetTblField":"TypeID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblItem","CheckTblField":"ItemDescID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblItem","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/Itemicon/{0}.png"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblItem","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblAccess":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAccess","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"StringDesc","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAccess","CheckTblField":"Param1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblProduce","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1]},"ResPathFormat":null}],"TblAccessItem":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAccessItem","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAccessItem","CheckTblField":"AccessID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblAccess","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblActionShow":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblActionShow","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblActionShow","CheckTblField":"ActionMan","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblActionShow","CheckTblField":"ActionWoman","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblAirDrop":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAirDrop","CheckTblField":"StartTip","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAirDrop","CheckTblField":"DropTip","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAirDrop","CheckTblField":"BaseReward","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblAttribute":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAttribute","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAttribute","CheckTblField":"TabName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblBagType":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBagType","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblBattery":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBattery","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblBookshelfTask":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBookshelfTask","CheckTblField":"TaskCondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblBoxSupply":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"ExclusivePoolID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"ConventionalPoolID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"MaleModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"FemaleModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/Forms/BoxSupply/UITexture/Banner/{0}.png"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblBoxSupply","CheckTblField":"Picture","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/Forms/BoxSupply/UITexture/Banner/{0}.png"}],"TblBuilding":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"Path","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/build_prefab/{0}"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"GroupName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"LevelUpBaseID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblBuilding","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"LevelUpItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"BuildItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblBuilding","CheckTblField":"MaintainItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblCollectionBase":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCollectionBase","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCollectionBase","CheckTblField":"BaseDropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCollectionBase","CheckTblField":"FirstTypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblCollectionBase","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/prop_prefab/{0}"}],"TblComfort":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblComfort","CheckTblField":"PassiveSkill1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblComfort","CheckTblField":"Buff","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraBuff","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblCookBook":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCookBook","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCookBook","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCookBook","CheckTblField":"Material","CheckTblFieldDefCheckField":"ItemID","TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblCookBookLevel":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCookBookLevel","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblCookBookLevel","CheckTblField":"Produce","CheckTblFieldDefCheckField":"ItemID","TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDaily":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDaily","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDaily","CheckTblField":"TaskCondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDailyStoreProduct":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDailyStoreProduct","CheckTblField":"ProductID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDecomposer":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDecomposer","CheckTblField":"MaterialID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDecomposer","CheckTblField":"Products","CheckTblFieldDefCheckField":"ItemID","TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDoorController":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDoorController","CheckTblField":"ConsumeItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDoorController","CheckTblField":"DoorID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDoor","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDrawDesk":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDrawDesk","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDrawDesk","CheckTblField":"CostID1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDrawDesk","CheckTblField":"CostID2","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDrawDesk","CheckTblField":"PicID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDropPackage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDropPackage","CheckTblField":"GroupID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropGroup","TargetTblField":"GroupID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDropGroup":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDropGroup","CheckTblField":"TagID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropTag","TargetTblField":"TagID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDropTag":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDropTag","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblDrugMaker":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblDrugMaker","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblEffect":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEffect","CheckTblField":"PrefabPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblEmplacement":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEmplacement","CheckTblField":"AutoWeaponID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblFgWeapon","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEmplacement","CheckTblField":"ManualWeaponID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblFgWeapon","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblEquip":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/Itemicon/{0}.png"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"ReplaceModelMale","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"ReplaceModelFemale","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"ModelPathMale","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblEquip","CheckTblField":"ModelPathFemale","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblEquipEffect":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEquipEffect","CheckTblField":"EffectName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblErrString":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblErrString","CheckTblField":"String","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblEvolutionStage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEvolutionStage","CheckTblField":"Notice","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEvolutionStage","CheckTblField":"Reach","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEvolutionStage","CheckTblField":"Plot","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblEvolutionStage","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblExchange":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExchange","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExchange","CheckTblField":"MoneyID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblExchangeShop":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExchangeShop","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblExtraBuff":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraBuff","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblExtraBuff","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/BuffIcon/{0}.png"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraBuff","CheckTblField":"EffectID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraEffect","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraBuff","CheckTblField":"Describe","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblExtraPassive":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraPassive","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraPassive","CheckTblField":"EffectID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraEffect","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblExtraPassive","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/PassiveIcon/{0}.png"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblExtraPassive","CheckTblField":"Describe","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblFace":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFace","CheckTblField":"Model","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFace","CheckTblField":"ModelHD","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFace","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/{0}"}],"TblFacility":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacility","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacility","CheckTblField":"TypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacility","CheckTblField":"JumpID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblPilotInst","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblFacilityUpgrade":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacilityUpgrade","CheckTblField":"FacilityID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacilityUpgrade","CheckTblField":"MaintainItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFacilityUpgrade","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/facility_prefab/{0}"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacilityUpgrade","CheckTblField":"UpgradeDescribe","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFacilityUpgrade","CheckTblField":"UpgradeContent","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblFgWeapon":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"AttackButtonPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/{0}.png"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"DefaultSight","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"DefaultMuzzle","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"BasicMagazineModel","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"Model2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"BulletFile1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFgWeapon","CheckTblField":"BulletFile2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblFuel":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFuel","CheckTblField":"FacilityID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFuel","CheckTblField":"MaterialID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblFurnace":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFurnace","CheckTblField":"MaterialID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblGuildBadge":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblGuildBadge","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblGuildBadge","CheckTblField":"PicID","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/Forms/Guild/GuildIcon/{0}.png"}],"TblGuildTask":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblGuildTask","CheckTblField":"CondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblGuildTask","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/Forms/Guild/GuildUI/{0}.png"}],"TblHair":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblHair","CheckTblField":"Model","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblHair","CheckTblField":"ModelHD","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblHair","CheckTblField":"ReplaceModel","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblHair","CheckTblField":"ReplaceModelHD","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblHair","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/{0}"}],"TblItemType":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblItemType","CheckTblField":"TypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblItemType","CheckTblField":"BtnName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblKillingPunishment":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblKillingPunishment","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblKillingPunishment","CheckTblField":"PassiveSkill","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblLevelMissionGroup":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLevelMissionGroup","CheckTblField":"TaskCondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLevelMissionGroup","CheckTblField":"Title","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblLoadingTips":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLoadingTips","CheckTblField":"Details","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblLockBox":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLockBox","CheckTblField":"DropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLockBox","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblMailString":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMailString","CheckTblField":"MailTitle","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMailString","CheckTblField":"MailContent","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblMonster":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMonster","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblMonster","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMonster","CheckTblField":"DropId","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMonster","CheckTblField":"FirstDrop","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMonster","CheckTblField":"PersonalDropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblMonsterBrushRate":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMonsterBrushRate","CheckTblField":"MonsterID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblMonster","TargetTblField":"MonsterID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblNpcInfo":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblNpcInfo","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblNpcInfo","CheckTblField":"FirstTalkStoryID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblStory","TargetTblField":"StoryID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblNpcInfo","CheckTblField":"FollowUpTalkStoryID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblStory","TargetTblField":"StoryID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblNpcInfo","CheckTblField":"Model","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblOutsideAchieve":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblOutsideAchieve","CheckTblField":"AchieveMent","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblOutsideAchieve","CheckTblField":"ModeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblOutsideAchieve","CheckTblField":"ClassifyName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblOutsideAchieve","CheckTblField":"TaskCondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblPersonalDrop":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPersonalDrop","CheckTblField":"DropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPersonalDrop","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblPersonalPackage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPersonalPackage","CheckTblField":"DropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblPersonalPackage","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/treasure_prefab/{0}.prefab"}],"TblPilot":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPilot","CheckTblField":"TextBoxContent","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblPilotInst":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPilotInst","CheckTblField":"TabMainName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPilotInst","CheckTblField":"TabSubName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblPilotInst","CheckTblField":"PrefabPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblPopUp":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPopUp","CheckTblField":"MessageID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblPortrait":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPortrait","CheckTblField":"PortraitID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblPortrait","CheckTblField":"PicID","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":true,"CheckSelectCondition":{"FieldName":"PortraitType","FieldValues":[1]},"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/PortraitIcon/Portrait/{0}.png"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblPortrait","CheckTblField":"PicID","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":true,"CheckSelectCondition":{"FieldName":"PortraitType","FieldValues":[2]},"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/PortraitIcon/PortraitFrame/{0}.png"}],"TblProduce":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblProduce","CheckTblField":"FacilityID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblProduce","CheckTblField":"NeedSciTech","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSciTech","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblProduce","CheckTblField":"PageID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblProducePage","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblProducePage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblProducePage","CheckTblField":"ParentName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblProducePage","CheckTblField":"SubName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblQuest":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuest","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuest","CheckTblField":"AcceptTalkID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblStory","TargetTblField":"StoryID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuest","CheckTblField":"FirstStageID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblQuestStage","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblQuestStage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuestStage","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuestStage","CheckTblField":"PilotInstID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblPilotInst","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblQuestStage","CheckTblField":"NextStageID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblQuestStage","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblRegenerateItem":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblRegenerateItem","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblRepairDesk":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblRepairDesk","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblRepairDesk","CheckTblField":"FacilityID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblReplace":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblReplace","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblReplaceGroup":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblReplaceGroup","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblResourceBase":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceBase","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceBase","CheckTblField":"ItemDropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceBase","CheckTblField":"DestroyDropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceBase","CheckTblField":"FirstTypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblResourceBase","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/prop_prefab/{0}"}],"TblResourceExtraDrop":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceExtraDrop","CheckTblField":"WeaponID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblFgWeapon","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceExtraDrop","CheckTblField":"ResBaseID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblResourceBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblResourceExtraDrop","CheckTblField":"ProduceItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSciTech":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSciTech","CheckTblField":"BookIntroduce","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSciTech","CheckTblField":"Icon","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/UI2/UITexture/Itemicon/{0}.png"}],"TblSeasonPassReward":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSeasonPassReward","CheckTblField":"CommRewardItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSeasonPassReward","CheckTblField":"AdvanceRewardItemID1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSeasonPassReward","CheckTblField":"AdvanceRewardItemID2","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSeasonPassTask":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSeasonPassTask","CheckTblField":"TaskName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSeasonPassTask","CheckTblField":"TaskInfo","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSectorObstacle":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSectorObstacle","CheckTblField":"DestroyPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSectorObstacle","CheckTblField":"Path","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblSelectCategory":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSelectCategory","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSelectGroup":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSelectGroup","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSelectMode":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSelectMode","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSelectMode","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSettlementMission":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSettlementMission","CheckTblField":"TypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSettlementMission","CheckTblField":"TaskCondID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTaskCond","TargetTblField":"CondID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSlot":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSlot","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSound":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSound","CheckTblField":"FileName","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblSponsor":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSponsor","CheckTblField":"TypeValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSponsor","CheckTblField":"TypeValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[2]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSponsor","CheckTblField":"TypeValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[3]},"ResPathFormat":null}],"TblStory":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblStory","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblStory","CheckTblField":"Content","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblStrongPoint":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblStrongPoint","CheckTblField":"CiteID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSector","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblStrongPoint","CheckTblField":"CiteID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblMapShow","TargetTblField":"ShowID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[4]},"ResPathFormat":null}],"TblSuitEffect":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSuitEffect","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSuitEffect","CheckTblField":"PrefabPath1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSuitEffect","CheckTblField":"PrefabPath2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblSupplyBase":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyBase","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSupplyGroup":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"DeployId1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSupplyBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"DeployId2","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSupplyBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"DeployId3","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSupplyBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"DeployId4","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSupplyBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"DeployId5","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSupplyBase","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"FirstTypeName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblSupplyGroup","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/Model/Mineral/{0}.prefab"}],"TblSurvivalSkill":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSurvivalSkill","CheckTblField":"PassiveSkillID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSurvivalSkill","CheckTblField":"Tips1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSurvivalSkill","CheckTblField":"Tips2","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSystem":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSystem","CheckTblField":"SystemName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTalent":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTalent","CheckTblField":"Title","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTalent","CheckTblField":"Reward","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"RewardType","FieldValues":[1]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTalent","CheckTblField":"Tips","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTame":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTame","CheckTblField":"ArmorID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTame","CheckTblField":"VehicleID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblVehicle","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTaskCond":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTaskCond","CheckTblField":"Desc","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTemperature":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTemperature","CheckTblField":"ErrorString","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTemperature","CheckTblField":"PassiveSkill1","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTemperature","CheckTblField":"PassiveSkill2","CheckTblFieldDefCheckField":null,"TargetTblName":"TblExtraPassive","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTowermtrl":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTowermtrl","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTreesBase":[{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblTreesBase","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/tree_prefab/{0}.prefab"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTreesBase","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTreesBase","CheckTblField":"DropItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblVehicle":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblVehicle","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblVehicle","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"}],"TblVehicleTip":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblVehicleTip","CheckTblField":"String","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblWeaponAmmo":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblWeaponAmmo","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblWeaponPartModel":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Magazine2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Magazine3","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Magazine4","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Muzzle1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Muzzle2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Sight1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Sight2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"Sight3","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"AimModeSight1","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"AimModeSight2","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblWeaponPartModel","CheckTblField":"AimModeSight3","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/prefab/object_prefab/weapon_prefab/{0}"}],"TblWeaponParts":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblWeaponParts","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblWeatherArea":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblWeatherArea","CheckTblField":"AreaID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSector","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblWeatherArea","CheckTblField":"WeatherGroupID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblWeatherGroup","TargetTblField":"WeatherGroupID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblTrees":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTrees","CheckTblField":"Prototype","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTreesBase","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"CanCut","FieldValues":[1]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblTrees","CheckTblField":"Prototype","CheckTblFieldDefCheckField":null,"TargetTblName":"TblTreesBase","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"CanCut","FieldValues":[1]},"ResPathFormat":null}],"TblAmbient":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblAmbient","CheckTblField":"SectorID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSector","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblFashion":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"ReplaceModelHD","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":true,"CheckSelectCondition":{"FieldName":"CustomValue","FieldValues":[2]},"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"ReplaceModel","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":true,"CheckSelectCondition":{"FieldName":"CustomValue","FieldValues":[2]},"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"ModelPath","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":1,"ErrorReason":"资源不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"ModelPathHD","CheckTblFieldDefCheckField":null,"TargetTblName":null,"TargetTblField":null,"CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":"Assets/AssetBundle/{0}"},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblFashion","CheckTblField":"SuitID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSuit","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblLobbyBagShow":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLobbyBagShow","CheckTblField":"PageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLobbyBagShow","CheckTblField":"SubPageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLobbyBagShow","CheckTblField":"ThirdPageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblLobbyBagShow","CheckTblField":"ItemType","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItemType","TargetTblField":"TypeID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblMapShow":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblMapShow","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblPacks":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPacks","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPacks","CheckTblField":"DropID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblDropPackage","TargetTblField":"PackageID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1,2,3]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPacks","CheckTblField":"Items","CheckTblFieldDefCheckField":"ItemID","TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[4]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPacks","CheckTblField":"Show","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSkin","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"ShowType","FieldValues":[2]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblPacks","CheckTblField":"Show","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"ShowType","FieldValues":[3]},"ResPathFormat":null}],"TblShop":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShop","CheckTblField":"Name","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShop","CheckTblField":"TypeValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[1,3]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShop","CheckTblField":"TypeValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSuit","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[2]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShop","CheckTblField":"SuitValue","CheckTblFieldDefCheckField":null,"TargetTblName":"TblSuit","TargetTblField":"ID","CheckSelect":true,"CheckSelectCondition":{"FieldName":"Type","FieldValues":[3]},"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShop","CheckTblField":"ClassifyID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblShopPage","TargetTblField":"ClassifyID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblShopPage":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShopPage","CheckTblField":"PageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShopPage","CheckTblField":"SubPageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblShopPage","CheckTblField":"ThirdPageName","CheckTblFieldDefCheckField":null,"TargetTblName":"TblString","TargetTblField":"ID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}],"TblSuit":[{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSuit","CheckTblField":"ID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null},{"TblCheckType":0,"ErrorReason":"需要配置但不存在","NullIsError":false,"CheckTblName":"TblSuit","CheckTblField":"ItemID","CheckTblFieldDefCheckField":null,"TargetTblName":"TblItem","TargetTblField":"ItemID","CheckSelect":false,"CheckSelectCondition":null,"ResPathFormat":null}]}