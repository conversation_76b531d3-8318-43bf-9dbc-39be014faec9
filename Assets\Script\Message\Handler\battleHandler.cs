using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using UnityEngine;
using SKY;
using UltimateSurvival.Building;
using UINT8 = System.Byte;
using UINT64 = System.UInt64;
using UINT32 = System.UInt32;
using NPOI.SS.Formula.Functions;

class BattleHandler : HandlerBase
{
    private BattleCommonManager _bcm;
    private Dictionary<UINT32, UINT64> _dicLastestMoveSeq = new Dictionary<UINT32, UINT64>();
    private UINT64 _qwLastestFireSeq = 0;
    private UINT32 m_dwFireIndex = 0;
    private Queue<UINT32> _qFireIndex = new Queue<UINT32>();
    public override void initHandler()
    {

        //场景管理
        bindMsg(GameServerProto_MSG.GMID_SCENE_RECOMMEND_ACK, _recvRecommedSceneAck);
        bindMsg(GameServerProto_MSG.GMID_SCENE_RECOMMEND_NTF, RecvRecommedSceneNtf);
        bindMsg(GameServerProto_MSG.GMID_SCENE_QUIT_ACK, RecvQuitSceneAck);
        bindMsg(GameServerProto_MSG.BTID_ENTER_SCENE_ACK, _recvEnterSceneAck);
        bindMsg(GameServerProto_MSG.BTID_ENTER_SCENE_NTF, _recvEnterSceneNtf);
        bindMsg(GameServerProto_MSG.BTID_SCENE_READY_ACK, _recvSceneReadyAck);
        bindMsg(GameServerProto_MSG.BTID_ENTER_SCENE_DONE_NTF, _recvEnterSceneDoneNtf);
        bindMsg(GameServerProto_MSG.BTID_SCENE_SIMPLE_INFO_NTF, _recvSceneSimpleInfoNtf);
        bindMsg(GameServerProto_MSG.BTID_ENTER_VIEW_NTF, _recvEnterViewNtf);
        bindMsg(GameServerProto_MSG.BTID_LEAVE_VIEW_NTF, _recvLeaveViewNtf);
        bindMsg(GameServerProto_MSG.BTID_LEAVE_SCENE_ACK, _recvLeaveSceneAck);
        bindMsg(GameServerProto_MSG.BTID_SCENE_END_NTF, _recvEndSceneNtf);

        //自定义字段
        bindMsg(GameServerProto_MSG.BTID_UNIT_INT_VAR_NTF, _recvUnitIntVarNtf);
        bindMsg(GameServerProto_MSG.BTID_UNIT_STRING_VAR_NTF, _recvUnitStringVarNtf);
        bindMsg(GameServerProto_MSG.BTID_UNIT_NUMBER_VAR_NTF, _recvUnitNumberVarNtf);

        //建筑相关
        bindMsg(GameServerProto_MSG.BTID_BUILDING_INFO_ACK, _recvBuildingInfoAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_LOCK_ACK, _recvBuildingLockAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_BUILD_ACK, _recvBuildingBuildAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_BUILD_NTF, _recvBuildingBuildNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_DEL_ACK, _recvBuildingDelAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_DEL_NTF, _recvBuildingDelNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_OWNER_CHANGE_NTF, _recvBuildingOwnerChangeNtf);
        //建筑升级
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_UPGRADE_ACK, _recvBuildingPieceUpgradeAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_UPGRADE_NTF, _recvBuildingPieceUpgradeNtf);
        //bindMsg(GameServerProto_MSG.BTID_BUILDING_UPGRADE_ACK, _recvBuildingUpgradeAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_UPGRADE_NTF, _recvBuildingUpgradeNtf);
        //建筑腐蚀
        bindMsg(GameServerProto_MSG.BTID_BUILDING_RUST_NTF, _recvBuildingRustNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_RUST_DOWM_NTF, _recgBuildingRustDownNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_HP_ACK, _recvBuildingHpAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_HP_VER_NTF, _recvBuildingHpVerNtf);
        //建筑修理
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_REPAIR_ACK, _recvBuildingPieceRepairAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_REPAIR_NTF, _recvBuildingPieceRepairNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_REPAIR_NTF, _recvBuildingRepairNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_MAKE_REAL_ACK, _recvBuildingFacilityMakeRealAck);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_VIRTUAL_REAL_NTF, _recvBuildingFacilityVirtualRealNTF);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_MAKE_REAL_WARNING_NTF, _recvBuildingFacilityMakeRealWarningNTF);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_VIRTUAL_REAL_UPDATE_NTF, _recvBuildingFacilityVirtualRealUpdateNTF);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_MAKE_REAL_NTF, _recvBuildingPieceVirtualNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_SKIN_CHANGE_NTF, _skinChangeNtf);

        //资源采集相关
        bindMsg(GameServerProto_MSG.BTID_RES_COLLECT_ACK, _recvResCollectAck);
        bindMsg(GameServerProto_MSG.BTID_RES_COLLECT_NTF, _recvResCollectNtf);
        bindMsg(GameServerProto_MSG.BTID_RES_RESET_NTF, _recvResResetNtf);



        // 角色相关
        // bindMsg(GameServerProto_MSG.BTID_ACTOR_MOVE_ACK, _recvActorMoveAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SET_POSTURE_ACK, _recvActorSetPosTureAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SET_POSTURE_NTF, _recvActorSetPosTureNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_JUMP_ACK, _recvActorJumpAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_JUMP_NTF, _recvActorJumpNtf);
        bindMsg(GameServerProto_MSG.BTID_REVEL_NTF, _recvRevelNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_DISPLAY_ACK, _recActorDisplayAck);

        // 新Move协议
        bindMsg(GameServerProto_MSG.BTID_UNIT_MOVE_ACK, _recvUnitMoveAck);
        bindMsg(GameServerProto_MSG.BTID_UNIT_MOVE_NTF, _recvUnitMoveNtf);

        // 使用道具CD时间更新
        bindMsg(GameServerProto_MSG.BTID_ITEM_USE_CD_NTF, _recvUseItemCDNtf);

        // 攻击
        bindMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_ACK, _recvActorFireAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_NTF, _recvActorFireNtf);
        // 受击
        bindMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_HIT_ACK, _recvActorFireHitAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_HIT_NTF, _recvActorFireHitNtf);
        // 换弹
        bindMsg(GameServerProto_MSG.BTID_ACTOR_WEAPON_RELOAD_ACK, _recvActorWeaponReloadAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_WEAPON_RELOAD_NTF, _recvActorWeaponReloadNtf);
        // 换武器
        bindMsg(GameServerProto_MSG.BTID_SEL_WEAPON_NTF, _recvSelWeaponNtf);
        // HP
        bindMsg(GameServerProto_MSG.BTID_UNIT_HP_CHANGE_NTF, _recvUnitHpChangeNtf);
        // 饱腹度
        bindMsg(GameServerProto_MSG.BTID_STA_CHANGE_NTF, _recvStaChangeNtf);
        // 口渴
        bindMsg(GameServerProto_MSG.BTID_WATER_CHANGE_NTF, _recvThirstChangeNtf);
        //饱腹度饮水buff
        bindMsg(GameServerProto_MSG.BTID_SURVIVALSKILL_UPDATE_NTF, _recvSurvivalSkillNtf);
        // 坠落伤害
        bindMsg(GameServerProto_MSG.BTID_ACTOR_FALLDOWN_ACK, _recvActorFallDownAck);
        // 濒死通知
        bindMsg(GameServerProto_MSG.BTID_ACTOR_WEAK_NTF, _recvActorWeakNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_WEAK_HP_NTF, _recvActorWeakHpNtf);
        // 自我解脱
        bindMsg(GameServerProto_MSG.BTID_ACTOR_BEGIN_SUICIDE_ACK, _recvActorSuicideAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SUICIDE_BREAK_NTF, _recvActorSuicideBreakNtf);
        // 心脏起搏器
        bindMsg(GameServerProto_MSG.BTID_ACTOR_PACEMAKER_ACK, _recvActorPacemakerAck);
        // 救助
        bindMsg(GameServerProto_MSG.BTID_ACTOR_HELP_ACK, _recvActorHelpAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_HELP_SUCCESS_NTF, _recvActorHelpSuccessNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_HELP_BREAK_NTF, _recvActorHelpBreakNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_HELP_NTF, _recvActorHelpNtf);
        // 表情动作
        bindMsg(GameServerProto_MSG.BTID_ACTION_ACK, _recvActionACK);
        bindMsg(GameServerProto_MSG.BTID_ACTION_NTF, _recvActionNtf);

        //buff
        bindMsg(GameServerProto_MSG.BTID_UNIT_BUFF_GET_NTF, _recvBuffGetNtf);
        bindMsg(GameServerProto_MSG.BTID_UNIT_BUFF_DISAPPEAR_NTF, _recvBuffDisappearNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_PROP_MAX_NTF, _recvActorProMaxNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_PROP_OVER_NTF, _recvActorProOverNtf);
        bindMsg(GameServerProto_MSG.BTID_PROP_CHANGE_NTF, _recvActorProChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_PROP_ACK, _recvActorProAck);
        bindMsg(GameServerProto_MSG.BTID_PROP_MANU_CHANGE_NTF, _recvActorProNtf);

        // 伤害标志位
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SET_PVP_FLAG_ACK, _recvActorPvpFlagAck);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SET_PVP_FLAG_UPDATE_NTF, _recvActorPvpFlagUpdateNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SET_PVP_FLAG_REMIND_NTF, _recvActorPvpFlagRemindNtf);
        bindMsg(GameServerProto_MSG.BTID_KILLVAL_NTF, _recvActorKillLevelChangeNtf);

        //被动
        bindMsg(GameServerProto_MSG.BTID_UNIT_PASSIVE_NTF, _recvUnitPassiveNtf);

        //测试用 MonsterThreat
        bindMsg(GameServerProto_MSG.BTID_MONSTER_THREAT_NTF, _recvMonsterThreatNtf);

        // 污染区
        bindMsg(GameServerProto_MSG.BTID_STATES_CHANGE_NTF, _recvStatesChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_ENTER_SECTOR_POLLUTED_NTF, _recvEnterSectorPollutedNtf);
        bindMsg(GameServerProto_MSG.BTID_EXIT_SECTOR_POLLUTED_NTF, _recvExitSectorPollutedNtf);
        bindMsg(GameServerProto_MSG.BTID_PURIFIER_FACILITY_START_NTF, _recvCleanerStartNtf);
        bindMsg(GameServerProto_MSG.BTID_PURIFIER_FACILITY_INFO_NTF, _recvCleanerInfoNtf);

        //补给相关
        bindMsg(GameServerProto_MSG.BTID_SUPPLY_ATTACKED_ACK, _recvSupplyAttackedAck);
        bindMsg(GameServerProto_MSG.BTID_SUPPLY_ATTACKED_NTF, _recvSupplyAttackedNtf);
        bindMsg(GameServerProto_MSG.BTID_SUPPLY_RESET_NTF, _recvSupplyResetNtf);

        //门相关
        bindMsg(GameServerProto_MSG.BTID_DOOR_OPEN_ACK, _recvDoorOpenAck);
        bindMsg(GameServerProto_MSG.BTID_DOOR_OPEN_NTF, _recvDoorOpenNtf);
        bindMsg(GameServerProto_MSG.BTID_DOOR_VISIBLE_NTF, _recvDoorVisibalNtf);
        bindMsg(GameServerProto_MSG.BTID_DOOR_INTERACTION_NTF, _recvDoorInteractionNtf);
        bindMsg(GameServerProto_MSG.BTID_PWDLOCK_DOOR_ERROR_NTF, _recvDoorPwdErrorNtf);

        //采集果子
        bindMsg(GameServerProto_MSG.BTID_COLLECTION_COLLECT_ACK, _recvCollectionCollectAck);
        bindMsg(GameServerProto_MSG.BTID_COLLECTION_COLLECT_NTF, _recvCollectionCollectNtf);
        bindMsg(GameServerProto_MSG.BTID_COLLECTION_RESET_NTF, _recvCollectionResetNtf);

        //设施建造（领地柜、睡袋、工作台)
        bindMsg(GameServerProto_MSG.BTID_FACILITY_SKIN_CHANGE_NTF, _recvFaclitySkinChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_PUT_ACK, _recvFacilityPutAck);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_PUT_NTF, _recvFacilityPutNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_DEL_ACK, _recvFacilityDelAck);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_DEL_NTF, _recvFacilityDelNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_MOVE_ACK, _recvFacilityMoveAck);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_MOVE_NTF, _recvFacilityMoveNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_STATE_CHANGE_NTF, _recvFacilityStateChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_LEVEL_CHANGE_NTF, _recvFacilityLevelChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_DESTORY_NTF, _recvFacilityDestroyNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_OWNER_LANDCAB_CHANGE_NTF, _recvFacilityOwnerLandCabChangeNtf);
        //设施升级
        //bindMsg(GameServerProto_MSG.BTPKG_FACILITY_UPGRADE_REQ, _sendFacilityUpgradeReq);
        //bindMsg(GameServerProto_MSG.BTDT_FACILITY_UPGRADE_ACK, _recvFacilityDestroyAck);
        //bindMsg(GameServerProto_MSG.BTDT_FACILITY_UPGRADE_INFO, _recvFacilityDestroyInfo);
        //bindMsg(GameServerProto_MSG.BTPKG_FACILITY_UPGRADE_NTF, _recvFacilityUpgradeNtf);

        //建筑升级（通过领地柜）
        bindMsg(GameServerProto_MSG.BTID_FACILITY_LANDCAB_UPGRADE_ACK, _recvFacilityLandCabUpgradeAck);

        //设施腐蚀&修理
        bindMsg(GameServerProto_MSG.BTID_FACILITY_RUST_NTF, _recvFacilityRustNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_REPAIR_ACK, _recvFacilityRepairAck);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_REPAIR_NTF, _recvFacilityRepairNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_LANDCAB_REPAIR_ACK, _recvFacilityLandcabRepairAck);
        //设施锁
        bindMsg(GameServerProto_MSG.BTID_FACILITY_CHECK_PASSWD_ACK, _recvFacilityCheckPasswdAck);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_SET_PASSWD_ACK, _recvFacilitySetPasswdAck);

        bindMsg(GameServerProto_MSG.BTID_FACILITY_ADD_PWDLOCK_NTF, _recvFacilityAddPwdlockNtf);
        bindMsg(GameServerProto_MSG.BTID_FACILITY_VIRTUALIZED_NTF, _recvFacilityVirtualNtf);

        //AIMonster
        bindMsg(GameServerProto_MSG.BTID_MONSTER_PRE_MOVE_NTF, _recvAIMonsterPreMoveNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_MOVE_NTF, _recvAIMonsterMoveNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_HIT_NTF, _recvAIMonsterHitNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_DEAD_NTF, _recvAIMonsterDeadNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_PRE_ATTACK_NTF, _recvAIMonsterPreAttackNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_CHECK_ALARM_VIEW_NTF, _recvAIMonsterCheckAlarmNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_PRE_TURN_ANGLE_NTF, _recvMonsterPreTurnAngleNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_STATE_CHANGE_NTF, _recvMonsterStateChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_ACTION_START_NTF, _recvMonsterActionStartNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_WARNING_START_NTF, _recvMonsterWarningStartNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_SKILL_START_NTF, _recvMonsterSkillStartNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_TAME_STATE_CHANGE_NTF, _recvMonsterTameStateChangeNtf);

        bindMsg(GameServerProto_MSG.BTID_MONSTER_HP_CHANGE_NTF, _recvMonsterHPChangeNtf);//guaiwu
        bindMsg(GameServerProto_MSG.BTID_MONSTER_SHIELD_CHANGE_NTF, _recvMonsterShieldChangNtf);//气绝
        bindMsg(GameServerProto_MSG.BTID_MONSTER_PART_DESTROY_NTF, _recvMonsterPartDestroyNtf);//部位破坏通知
        bindMsg(GameServerProto_MSG.BTID_MONSTER_PART_REBORN_NTF, _recvMonsterPartRebornNtf);//部位重生通知
        bindMsg(GameServerProto_MSG.BTID_CONFINE_DESTOR_NTF, _recvConfineDestoryNtf);//confine 破坏通知
        bindMsg(GameServerProto_MSG.BTID_MONSTER_POSTURE_CHANGE_NTF, _recvMonsterPostureChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_INVINCIBLE_CHANGE_NTF, _recvMonsterInvincibleChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_ATTACK_POS_NTF, _recvMonsterAttackPosNtf); // 获取出击点
        bindMsg(GameServerProto_MSG.BTID_MONSTER_HIDDEN_POS_NTF, _recvMonsterHiddenPosNtf); // 获取隐蔽点


        //Mount
        bindMsg(GameServerProto_MSG.BTID_VEHICLE_GET_ON_ACK, _recvVehicleGetOnAck);
        bindMsg(GameServerProto_MSG.BTID_VEHICLE_GET_OFF_ACK, _recvVehicleGetOffAck);
        // bindMsg(GameServerProto_MSG.BTID_VEHICLE_MOVE_ACK, _recvVehicleMoveAck);
        // bindMsg(GameServerProto_MSG.BTID_VEHICLE_MOVE_NTF, _recvVehicleMoveNtf);
        bindMsg(GameServerProto_MSG.BTID_VEHICLE_GET_ON_NTF, _recvVehicleGetOnNtf);
        bindMsg(GameServerProto_MSG.BTID_VEHICLE_GET_OFF_NTF, _recvVehicleGetOffNtf);

        //MonsterPoint
        bindMsg(GameServerProto_MSG.BTID_MONSTER_POINT_GET_INFO_ACK, _recvMonsterPointInfoAck);
        bindMsg(GameServerProto_MSG.BTID_MONSTER_POINT_INFO_CHANGE_NTF, _recvMonsterPointInfoChangeNtf);

        //锁相关
        bindMsg(GameServerProto_MSG.BTID_PWDLOCK_ADD_ACK, _recvPwdLockAddAck);
        bindMsg(GameServerProto_MSG.BTID_PWDLOCK_ADD_NTF, _recvPwdLockAddNtf);
        bindMsg(GameServerProto_MSG.BTID_PWDLOCK_ALERT_ACK, _recvPwdLockAlertAck);
        bindMsg(GameServerProto_MSG.BTID_PWDLOCK_ERROR_NTF, _recvPwdLockErrorNtf);


        //自己在线,视野中其他玩家下线后上线，其他玩家给自己通知
        bindMsg(GameServerProto_MSG.BTID_ACTOR_UNIT_UPDATE_NTF, _recvActorUnitUpdateNTF);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_OFFLINE_NTF, _recvActorOfflineNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_OFFLINE_TO_ONLINE_NTF, _recvActorOnlineNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_OFFLINE_LIEDOWN_NTF, _recvActorOfflineLieDownNtf);

        //天气相关
        bindMsg(GameServerProto_MSG.BTID_WEATHER_INFO_NTF, _recvWeatherUpdateNTF);

        // 服务器时间同步
        bindMsg(GameServerProto_MSG.GMID_SERVER_TIME_SYNC_NTF, _recvServerTimeSyncNTF);

        // 修正位置
        bindMsg(GameServerProto_MSG.BTID_ACTOR_OFFLINE_POS_CORRECT_ACK, _recvActorOfflinePosCorrectAck);

        // 温度
        bindMsg(GameServerProto_MSG.BTID_TEMPERATURE_UPDATE_INFO_NTF, _recvTemperatureNtf);

        // 获得经验通知
        bindMsg(GameServerProto_MSG.BTID_TALENT_LEVEL_UP_NTF, _recvActorAddExpNtf);
        bindMsg(GameServerProto_MSG.BTID_TALENT_VALUE_NTF, _recvActorTalentDailyExpNtf);



        bindMsg(GameServerProto_MSG.BTID_TALENT_NODE_LEVEL_UP_ACK, _recvTalentLevelUpAck);

        //舒适度
        bindMsg(GameServerProto_MSG.BTID_ACTOR_COMFORT_NTF, _recvComfortNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_COMFORT_FACILITY_ACK, _recvGetComfortFacilityAck);

        bindMsg(GameServerProto_MSG.BTID_NPC_VISIBLE_NTF, _recvNPCVisibleNtf);

        // 瞬移
        bindMsg(GameServerProto_MSG.BTID_DO_TRANSFER_ACK, _recvActorDoTransferAck);
        bindMsg(GameServerProto_MSG.BTID_UNIT_TRANSFER_NTF, _recvUnitTransferNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_POSITION_RESET_ACK, _recvActorPositionResetAck);
        bindMsg(GameServerProto_MSG.BTID_CLIENT_CHAOS_STATE_REMOVE_ACK, _recvClientChaosStateRemoveAck);
        bindMsg(GameServerProto_MSG.BTID_CHAOS_STATE_CHANGE_NTF, _recvChaosStateChangeNtf);

        //战局统计
        bindMsg(GameServerProto_MSG.BTID_ACTOR_RECORD_INFO_ACK, _recvRecordInfoAck);
        bindMsg(GameServerProto_MSG.BTID_VEHICLE_MOUNT_NUM_NTF, _recvVehicleMountNumNtf);

        bindMsg(GameServerProto_MSG.BTID_EVOLUTION_STAGE_DISPLAYED_ACK, _recvEvoStageAck);

        //局内货币
        bindMsg(GameServerProto_MSG.GMID_CURRENCY_CHANGE_NTF, _recvCurrencyChangeNtf);

        //地雷
        bindMsg(GameServerProto_MSG.BTID_MINE_BOMB_NTF, _recvMineBombNtf);

        //武器柜
        bindMsg(GameServerProto_MSG.BTID_WEAPONCABINET_INFO_ACK, _recvWaponBoxInfoAck);

        bindMsg(GameServerProto_MSG.BTID_WEAPONCABINET_UPDATE_NTF, _recvWaponBoxIsRefreshNtf);

        //伤害列表
        bindMsg(GameServerProto_MSG.BTID_DMGSTATS_CHANGE_NTF, _recvDamageListRefreshNtf);


        bindMsg(GameServerProto_MSG.BTID_UNIT_BOARDNAME_NTF, _recvUnitBoardNameNtf);

        bindMsg(GameServerProto_MSG.BTID_RES_CLUSTER_ADD_NTF, _recvResClusterAddNtf);
        bindMsg(GameServerProto_MSG.BTID_RES_CLUSTER_DEL_NTF, _recvResClusterDelNtf);
        bindMsg(GameServerProto_MSG.BTID_RES_CLUSTER_INFO_NTF, _recvResClusterInfoNtf);

        //语音范围切换
        bindMsg(GameServerProto_MSG.BTID_ACTOR_VOICE_STATE_NTF, _recvVoiceStateChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_LOUDSPEAKER_NTF, _recvLoudSpeakerNtf);

        //活动通知
        bindMsg(GameServerProto_MSG.BTID_GET_ALL_ANNOUNCEMENT_ACK, _recvGetAnnoucementAck);
        bindMsg(GameServerProto_MSG.BTID_GET_ALL_ANNOUNCEMENTTIP_ACK, _recvGetAnnoucementTipAck);
        bindMsg(GameServerProto_MSG.BTID_ANNOUNCEMENT_CHANGE_NTF, _recvAnnoucementChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_ANNOUNCEMENTTIP_CHANGE_NTF, _recvAnnoucementTipChangeNtf);

        // 传送
        bindMsg(GameServerProto_MSG.BTID_TRANSFER_DO_ACK, _recvTransferDoAck);
        // 战斗状态
        bindMsg(GameServerProto_MSG.BTID_ACTOR_BATTLE_STATE_NTF, _recvBattleStateNTF);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_STATE_COOLING_NTF, _recvStateCoolingNTF);
        //安全状态
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SAFE_STATE_NTF, _recvSafeStateNTF);
        bindMsg(GameServerProto_MSG.BTID_ACTOR_SAFE_STATE_CHANGE_NTF, _recvSafeStateChangeNTF);
        // 体力条
        bindMsg(GameServerProto_MSG.BTID_PHYSICAL_CHANGE_NTF, _recvPhysicalChangeNtf);

        // 护盾
        bindMsg(GameServerProto_MSG.BTID_SHIELD_HP_NTF, _recvShieldHPNtf);
        bindMsg(GameServerProto_MSG.BTID_SHIELD_STOP_NTF, _recvShieldStopNtf);

        //勋章传送
        bindMsg(GameServerProto_MSG.BTID_SCENE_MEDAL_BATTLE_END_NTF, _recvMedalBattleEndNtf);
        bindMsg(GameServerProto_MSG.BTID_SCENE_MEDAL_TRANS_BOSSTOWER_ACK, _recvBosstowerTransAck);
        bindMsg(GameServerProto_MSG.BTID_SCENE_MEDAL_TRANS_CANCEL_ACK, _recvBosstowerTransCancelAck);

        bindMsg(GameServerProto_MSG.BTID_DEVICE_RUN_STATUS_NTF, _recvDeviceRunStatusNtf);

        //拆家保护
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PROTECT_START_NTF, _recvBuildingProtectStartNtf);
        bindMsg(GameServerProto_MSG.BTID_BUILDING_PROTECT_STATUS_NTF, _recvBuildingProtectNtf);

        //玩法提示
        bindMsg(GameServerProto_MSG.GMID_INSTRUCTION_UPDATE_NTF, _recvIntroductionNtf);
        //UDP相关
        bindMsg(GameServerProto_MSG.GMID_CLT_SEND_PROTOCOL_CHANGE_NTF, _recvProtocolChangeNtf);

        //拆家报警
        bindMsg(GameServerProto_MSG.BTID_BURGLAR_ALARM_STATUS_NTF, _recvHomeAttackedNtf);
        bindMsg(GameServerProto_MSG.BTID_BURGLAR_ALARM_INFO_ACK, _recvHomeAttackedInfo);
        bindMsg(GameServerProto_MSG.BTID_BURGLAR_ALARM_OPEN_SEND_SWITCH_ACK, _recvHomeAttackedPushSwitch);

        // 辐射
        bindMsg(GameServerProto_MSG.BTID_RADIATION_BAG_ACK, _RecvRadiationAck);
        bindMsg(GameServerProto_MSG.BTID_RADIATION_VAL_CHANGE_NTF, _RecvRadiationValChangeNtf);

        // 控制对象移动
        // bindMsg(GameServerProto_MSG.BTID_TRANSFORMER_MOVE_ACK, _recvTransformMoveAck);
        // bindMsg(GameServerProto_MSG.BTID_TRANSFORMER_MOVE_NTF, _recvTransformMoveNtf);

        //观看广告次数
        bindMsg(GameServerProto_MSG.GMID_STATISTICS_REPORT_ACK, RecvAdTimes);
        bindMsg(GameServerProto_MSG.GMID_DAY_PASS_NTF, RecvDayPassNtf);

        //击杀播报
        bindMsg(GameServerProto_MSG.BTID_TEAM_KILL_BROADCAST_NTF, RecvKillAnno);

        //辐射源
        bindMsg(GameServerProto_MSG.BTID_DETECTOR_CARD_CHANGE_NTF, RecvCardWearNtf);
        bindMsg(GameServerProto_MSG.BTID_DETECTOR_MONSTER_INFO_ACK, RecvMonsterInfoAck);
        bindMsg(GameServerProto_MSG.BTID_DETECTOR_MONSTER_NOTICE_NTF, RecvMonsterNoticeNtf);
        bindMsg(GameServerProto_MSG.BTID_DETECTOR_MONSTER_DEAD_NTF, RecvMonsterDeadNtf);
        bindMsg(GameServerProto_MSG.BTID_DETECTOR_ACTIVATESECTOE_UPDATE_NTF, RecvActivateSectorUpdateNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_DATA_UPDATE_NTF, RecvEvacuateUpdateNtf);

        //撤离
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_CARD_CHANGE_NTF, RecvEvacuateCardChangeNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_SECTOR_ENTER_NTF, RecvSectorEnterNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_SECTOR_LEAVE_NTF, RecvSectorLeaveNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_SUCCESS_BROADCAST_NTF, RecvSectorBroadcastNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_RESULT_POPUP_NTF, RecvEvacuateResultPopupNtf);
        bindMsg(GameServerProto_MSG.BTID_EVACUATE_END_COUNTDOWN_NTF, RecvEvacuateEndCDNtf);
        //词条
        bindMsg(GameServerProto_MSG.BTID_ENTRY_BAG_GET_ACK, _recvEnterBagAck);
        bindMsg(GameServerProto_MSG.BTID_ENTRY_BAG_CHANGE_NTF, _RecvEnterChangeNTF);

        //蓝图
        bindMsg(GameServerProto_MSG.BTID_MAKE_BLUEPRINT_ACK, RecvMakeBlueprintAck);
        bindMsg(GameServerProto_MSG.BTID_GET_BLUEPRINT_BUILDING_ACK, RecvGetBlueprintBuilding);
        bindMsg(GameServerProto_MSG.BTID_BUILD_BLUEPRINT_BUILDING_ACK, RecvBuildBlueprintBuilding);

    }

    public override void releaseHander() { }

    public void _recvDeviceRunStatusNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_DEVICE_RUN_STATUS_NTF;
        BattleMgr.I().FacilityManager.DeviceRunStatusNtf(ntf);
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ELEC_DEVICEISRUN_UPDATE, ntf);
    }

    public void _recvResClusterAddNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_RES_CLUSTER_ADD_NTF;
        BattleMgr.I().ResClusterAddNtf(ntf.m_oListCluster);
    }

    public void _recvResClusterDelNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_RES_CLUSTER_DEL_NTF;
        BattleMgr.I().ResClusterDelNtf(ntf.m_oListCluster);
    }

    public void _recvResClusterInfoNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_RES_CLUSTER_INFO_NTF;
        BattleMgr.I().GetManager<MineralResManager>().m_showCluster = ntf.m_oListCluster;
    }

    //请求建筑信息
    public void sendBuildingInfoReq(uint buildingId, uint startPieceId)
    {
        BTPKG_BUILDING_INFO_REQ req = new BTPKG_BUILDING_INFO_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwStartPieceID = startPieceId;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_INFO_REQ, req);
    }
    //接收建筑信息
    public void _recvBuildingInfoAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_INFO_ACK ack = data as BTPKG_BUILDING_INFO_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            BattleMgr.I().GetManager<BuildingNet>().BuildingInfoAck(ack.m_stBuilding, ack.m_dwStartPieceID);
        }
        else
        {
            BattleMgr.I().GetManager<BuildingNet>().BuildingInfoAckFail(ack.m_stBuilding);
        }
    }

    //物体进入视野
    public void _recvEnterViewNtf(GMDT_BASE data)
    {
        BTPKG_ENTER_VIEW_NTF ntf = data as BTPKG_ENTER_VIEW_NTF;
        BattleMgr.I().EnterView(ntf.m_oListUnit);
    }

    //物体离开视野
    public void _recvLeaveViewNtf(GMDT_BASE data)
    {
        BTPKG_LEAVE_VIEW_NTF ntf = data as BTPKG_LEAVE_VIEW_NTF;
        BattleMgr.I().LeaveView(ntf.m_oListUnit);
    }

    #region Ai怪物
    public void _recvAIMonsterPreMoveNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_PRE_MOVE_NTF ntf = data as BTPKG_MONSTER_PRE_MOVE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterPreMove(ntf);
    }

    public void _recvAIMonsterMoveNtf(GMDT_BASE data)
    {
        // BTPKG_MONSTER_MOVE_NTF ntf = data as BTPKG_MONSTER_MOVE_NTF;
    }

    public void _recvAIMonsterHitNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_HIT_NTF ntf = data as BTPKG_MONSTER_HIT_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterHit(ntf);
    }

    public void _recvAIMonsterDeadNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_DEAD_NTF ntf = data as BTPKG_MONSTER_DEAD_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterDead(ntf);
    }

    public void _recvAIMonsterPreAttackNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_PRE_ATTACK_NTF ntf = data as BTPKG_MONSTER_PRE_ATTACK_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterPreAttack(ntf);
    }

    public void _recvAIMonsterCheckAlarmNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_CHECK_ALARM_VIEW_NTF ntf = data as BTPKG_MONSTER_CHECK_ALARM_VIEW_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().CheckMonsterAlarm(ntf);
    }

    public void _recvMonsterPreTurnAngleNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_PRE_TURN_ANGLE_NTF ntf = data as BTPKG_MONSTER_PRE_TURN_ANGLE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterPreTurnAngleNtf(ntf);

    }

    public void _recvMonsterStateChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_STATE_CHANGE_NTF ntf = data as BTPKG_MONSTER_STATE_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterStateChangeNtf(ntf);
    }

    public void _recvMonsterActionStartNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_ACTION_START_NTF ntf = data as BTPKG_MONSTER_ACTION_START_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterActionStartNtf(ntf);
    }

    public void sendAIMonsterCheckAlarmRpt(System.UInt32 monsterId, List<BTDT_VIEW_ACTOR_POS> listActorPos)
    {
        BTPKG_MONSTER_CHECK_ALARM_VIEW_RPT rpt = new BTPKG_MONSTER_CHECK_ALARM_VIEW_RPT();
        rpt.m_dwMonsterUnitID = monsterId;
        rpt.m_oListActorPos = listActorPos;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_CHECK_ALARM_VIEW_RPT, rpt);

    }

    private void _recvMonsterWarningStartNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_WARNING_START_NTF ntf = data as BTPKG_MONSTER_WARNING_START_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterWarningStartNtf(ntf);
    }

    public void RptMonsterWarningEnd(uint monsterUnitID, ushort skillID, uint skillUID, BTDT_VECTOR3 nowPos, BTDT_VECTOR3 nowEuler, byte ifBreak)
    {
        BTPKG_MONSTER_WARNING_END_RPT rpt = new BTPKG_MONSTER_WARNING_END_RPT();
        rpt.m_dwMonsterUnitID = monsterUnitID;
        rpt.m_wSkillID = skillID;
        rpt.m_dwSkillUID = skillUID;
        rpt.m_stNowPos = nowPos;
        rpt.m_stNowEuler = nowEuler;
        rpt.m_byIfBreak = ifBreak;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_WARNING_END_RPT, rpt);
    }

    private void _recvMonsterTameStateChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_TAME_STATE_CHANGE_NTF ntf = data as BTPKG_MONSTER_TAME_STATE_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterTameStateChangeNtf(ntf);
    }

    private void _recvMonsterSkillStartNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_SKILL_START_NTF ntf = data as BTPKG_MONSTER_SKILL_START_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().MonsterSkillStartNtf(ntf);
    }

    private void _recvMonsterHPChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_HP_CHANGE_NTF ntf = data as BTPKG_MONSTER_HP_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterHPChangeNtf(ntf);
    }

    private void _recvMonsterShieldChangNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_SHIELD_CHANGE_NTF ntf = data as BTPKG_MONSTER_SHIELD_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterShieldChangNtf(ntf);
    }


    private void _recvMonsterPartDestroyNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_PART_DESTROY_NTF ntf = data as BTPKG_MONSTER_PART_DESTROY_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterPartDestroyNtf(ntf);
    }

    private void _recvMonsterPartRebornNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_PART_REBORN_NTF ntf = data as BTPKG_MONSTER_PART_REBORN_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterPartRebornNtf(ntf);
    }

    public void RptMonsterSkillStart(uint monsterUnitID, ushort skillID, uint skillUID, List<BTDT_MONSTER_SKILL_ATTACK_INFO> attackInfos)
    {
        BTPKG_MONSTER_SKILL_START_RPT rpt = new BTPKG_MONSTER_SKILL_START_RPT();
        rpt.m_dwMonsterUnitID = monsterUnitID;
        rpt.m_wSkillID = skillID;
        rpt.m_dwSkillUID = skillUID;
        rpt.m_oListAttacks = attackInfos;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_SKILL_START_RPT, rpt);
    }

    public void RptMonsterSkillPos(uint monsterUnitID, ushort skillID, uint skillUID, uint seq, BTDT_VECTOR3 pos)
    {
        BTPKG_MONSTER_SKILL_POS_RPT rpt = new BTPKG_MONSTER_SKILL_POS_RPT();
        rpt.m_dwMonsterUnitID = monsterUnitID;
        rpt.m_wSkillID = skillID;
        rpt.m_dwSkillUID = skillUID;
        rpt.m_dwSeq = seq;
        rpt.m_stSkillPos = pos;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_SKILL_POS_RPT, rpt);
    }

    public BTDT_MONSTER_SKILL_ATTACK_INFO MonsterCreateSkillInfo(uint skillUID, Byte serialNumber, BTDT_VECTOR3 pos)
    {
        BTDT_MONSTER_SKILL_ATTACK_INFO info = new BTDT_MONSTER_SKILL_ATTACK_INFO();
        info.m_dwSkillUID = skillUID;
        info.m_bySerialNumber = serialNumber;
        info.m_stPos = pos;
        info.m_qwStartTimeMS = (ulong)CommonFunctions.getLocalTimeStampMilli();
        return info;
    }

    public void RptMonsterSkillHit(uint monsterUnitID, ushort skillID, uint skillUID, uint victimUnitID, Byte serialNumber,
        Byte skillOrEffectDamage, uint unitIndex, uint damageRate)
    {
        BTPKG_MONSTER_SKILL_HIT_RPT rpt = new BTPKG_MONSTER_SKILL_HIT_RPT();
        rpt.m_dwMonsterUnitID = monsterUnitID;
        rpt.m_wSkillID = skillID;
        rpt.m_dwSkillUID = skillUID;
        rpt.m_dwVictimUnitID = victimUnitID;
        rpt.m_dwVictimUnitIndex = unitIndex;
        rpt.m_bySerialNumber = serialNumber;
        rpt.m_bySkillOrEffectDamage = skillOrEffectDamage;
        rpt.m_dwDamageRate = damageRate;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_SKILL_HIT_RPT, rpt);
    }

    public void RptMonsterSkillEnd(uint monsterUnitID, ushort skillID, uint skillUID, BTDT_VECTOR3 nowPos, BTDT_VECTOR3 nowEuler, byte ifWeak)
    {
        BTPKG_MONSTER_SKILL_END_RPT rpt = new BTPKG_MONSTER_SKILL_END_RPT();
        rpt.m_dwMonsterUnitID = monsterUnitID;
        rpt.m_wSkillID = skillID;
        rpt.m_dwSkillUID = skillUID;
        rpt.m_stNowPos = nowPos;
        rpt.m_stNowEuler = nowEuler;
        rpt.m_byIfWeak = ifWeak;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_SKILL_END_RPT, rpt);
    }

    private void _recvConfineDestoryNtf(GMDT_BASE data)
    {
        BTPKG_CONFINE_DESTOR_NTF ntf = data as BTPKG_CONFINE_DESTOR_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvConfineDestoryNtf(ntf);
    }

    private void _recvMonsterPostureChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_POSTURE_CHANGE_NTF ntf = data as BTPKG_MONSTER_POSTURE_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterPostureChangeNtf(ntf);
    }

    private void _recvMonsterInvincibleChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_INVINCIBLE_CHANGE_NTF ntf = data as BTPKG_MONSTER_INVINCIBLE_CHANGE_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterInvincibleChangeNtf(ntf);
    }

    private void _recvMonsterAttackPosNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_ATTACK_POS_NTF ntf = data as BTPKG_MONSTER_ATTACK_POS_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterAttackPosNtf(ntf);
    }
    private void _recvMonsterHiddenPosNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_HIDDEN_POS_NTF ntf = data as BTPKG_MONSTER_HIDDEN_POS_NTF;
        BattleMgr.I().GetManager<AIMonsterManager>().RecvMonsterHiddenPosNtf(ntf);
    }

    public void rptMonsterHiddenPos(byte isFind, uint unitId, BTDT_VECTOR3 pos, byte hiddenType)
    {
        BTPKG_MONSTER_HIDDEN_POS_RPT rpt = new BTPKG_MONSTER_HIDDEN_POS_RPT();
        rpt.m_byIfHasHiddenPos = isFind;
        rpt.m_dwMonsterUnitID = unitId;
        rpt.m_stHiddenPos = pos;
        rpt.m_byHiddenType = hiddenType;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_HIDDEN_POS_RPT, rpt);
    }

    public void rptMonsterAttackPos(byte isFind, uint unitId, BTDT_VECTOR3 pos)
    {
        BTPKG_MONSTER_ATTACK_POS_RPT rpt = new BTPKG_MONSTER_ATTACK_POS_RPT();
        rpt.m_byIfHasAttackPos = isFind;
        rpt.m_dwMonsterUnitID = unitId;
        rpt.m_stAttackPos = pos;
        sendMsg(GameServerProto_MSG.BTID_MONSTER_ATTACK_POS_RPT, rpt);
    }
    #endregion

    #region 坐骑
    public void sendVehicleGetOnReq(UInt32 unitId)
    {
        BTPKG_VEHICLE_GET_ON_REQ req = new BTPKG_VEHICLE_GET_ON_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_VEHICLE_GET_ON_REQ, req);
    }

    public void sendVehicleGetOffReq(uint id, Vector3 pos, Vector3 euler)
    {
        BTPKG_VEHICLE_GET_OFF_REQ req = new BTPKG_VEHICLE_GET_OFF_REQ();
        req.m_stEuler = BattleUtils.V3_TO_BTDTV3(euler);
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(pos);
        req.m_dwUnitID = id;
        sendMsg(GameServerProto_MSG.BTID_VEHICLE_GET_OFF_REQ, req);
    }

    public void _recvVehicleGetOnAck(GMDT_BASE data)
    {
        BTPKG_VEHICLE_GET_ON_ACK ack = data as BTPKG_VEHICLE_GET_ON_ACK;
        //if (!checkErrCode(ack.m_nErrCode)) return;
        BattleMgr.I().GetManager<MountManager>().MountAnimal(ack);
    }

    public void _recvVehicleGetOffAck(GMDT_BASE data)
    {
        BTPKG_VEHICLE_GET_OFF_ACK ack = data as BTPKG_VEHICLE_GET_OFF_ACK;
        //if (!checkErrCode(ack.m_nErrCode)) return;
        BattleMgr.I().GetManager<MountManager>().UnmountAnimal(ack);
    }

    public void _recvVehicleGetOnNtf(GMDT_BASE data)
    {
        BTPKG_VEHICLE_GET_ON_NTF ntf = data as BTPKG_VEHICLE_GET_ON_NTF;
        BattleMgr.I().GetManager<MountManager>().MountOnNtf(ntf);
    }

    public void _recvVehicleGetOffNtf(GMDT_BASE data)
    {
        BTPKG_VEHICLE_GET_OFF_NTF ntf = data as BTPKG_VEHICLE_GET_OFF_NTF;
        BattleMgr.I().GetManager<MountManager>().MountOffNtf(ntf);
    }
    #endregion

    public void sendMonsterPointInfoReq()
    {
        BTPKG_MONSTER_POINT_GET_INFO_REQ req = new BTPKG_MONSTER_POINT_GET_INFO_REQ();
        sendMsg(GameServerProto_MSG.BTID_MONSTER_POINT_GET_INFO_REQ, req);

    }

    public void _recvMonsterPointInfoAck(GMDT_BASE data)
    {
        BTPKG_MONSTER_POINT_GET_INFO_ACK ack = data as BTPKG_MONSTER_POINT_GET_INFO_ACK;
        PlayerData.getInstance().m_oMapData.SetMonsterPointInfo(ack);
    }

    public void _recvMonsterPointInfoChangeNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_POINT_INFO_CHANGE_NTF ntf = data as BTPKG_MONSTER_POINT_INFO_CHANGE_NTF;
        PlayerData.getInstance().m_oMapData.UpdateMonsterPointInfo(ntf);
    }

    public void sendBuildingLockReq(uint buildingId, uint ver, BTDT_BUILDING_PIECE piece, uint flag = 0, uint delPieceId = 0)
    {
        BattleMgr.I().BuildManager.WaitBuildingLock = true;
        //buildingId为0表示是建筑第一块
        BTPKG_BUILDING_LOCK_REQ req = new BTPKG_BUILDING_LOCK_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwVer = ver;
        req.m_stPiece = piece;
        req.m_dwFlag = flag;
        req.m_dwDelPieceID = delPieceId;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_LOCK_REQ, req);
    }

    public void _recvBuildingLockAck(GMDT_BASE data)
    {
        BattleMgr.I().BuildManager.WaitBuildingLock = false;
        BTPKG_BUILDING_LOCK_ACK ack = data as BTPKG_BUILDING_LOCK_ACK;
        if (ack.m_dwFlag == 0) BattleMgr.I().BuildManager.m_Player.LocalPlayer.BuildingLockAck.Invoke(ack);
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            // 锁定成功
            if (ack.m_dwFlag == 1)
            {
                //拆房子锁定
                //BattleMgr.I().GetManager<BuildingNet>().BuildingDelLockAck(ack.m_dwBuildingID, ack.m_dwDelPieceID, ack.m_dwVer);
            }
            else if (ack.m_dwFlag == 2)
            {
                //摧毁房子
                //BattleMgr.I().GetManager<BuildingNet>().BuildingDestroyLockAck(ack);
            }
        }
        else
        {
            // 锁定失败
            if (ack.m_nErrCode == GameServerProto_DEF.PTERR_BUILDING_LOCKED_BY_OTHER || ack.m_nErrCode == GameServerProto_DEF.PTERR_BUILDING_VER_WRONG)
            {
                //当被其他人锁住
                //或者版本比较旧，版本比较旧的原因是刚好有人造了个新建筑块，这个时候新块和新的版本号会被推下来，这边重新发起锁定就行
                if (ack.m_dwFlag == 2)
                {
                    //摧毁房子没锁住，继续尝试
                    BattleMgr.I().GetManager<BuildingNet>().BuildingDestroyLockFail(ack.m_dwBuildingID);
                }
            }
            else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_BUILDING_NOT_EXIST)
            {
                if (ack.m_dwFlag == 2)
                {
                    //当打掉最后一片建筑，服务器直接会将建筑删除，不会等客户端上报
                }
            }
            else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_BUILDING_IN_NONBUILD_AREA)
            {
                ComMsg.getInstance().showErrString(ack.m_nErrCode);
            }
        }
    }

    public void sendBuildingBuildReq(uint buildingId, uint ver, BTDT_BUILDING_PIECE piece, List<BTDT_BUILDING_PIECE> updatePieces, BUILDING_INTEGRITY_SUM integritySum)
    {
        BTPKG_BUILDING_BUILD_REQ req = new BTPKG_BUILDING_BUILD_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwVer = ver;
        req.m_stPiece = piece;
        req.m_oListUpdatePiece = updatePieces;
        req.m_stCheckCode = integritySum;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_BUILD_REQ, req);
    }

    public void _recvBuildingBuildAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_BUILD_ACK ack = data as BTPKG_BUILDING_BUILD_ACK;
        checkErrCode(ack.m_nErrCode);
        BattleMgr.I().BuildManager.m_Player.LocalPlayer.BuildingBuildAck.Invoke(ack);
        // if (checkErrCode(ack.m_nErrCode))
        // {
        //     BattleMgr.I().BuildManager.PlayerBuildingBuild(ack);
        // }
    }

    public void _recvBuildingBuildNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_BUILD_NTF ntf = data as BTPKG_BUILDING_BUILD_NTF;
        BattleMgr.I().GetManager<BuildingNet>().BuildingBuildNtf(ntf);
    }

    public void sendBuildingDelReq(uint buildingId, uint ver, uint deleteId, int flag, BUILDING_INTEGRITY_SUM integritySum)
    {
        BTPKG_BUILDING_DEL_REQ req = new BTPKG_BUILDING_DEL_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwVer = ver;
        req.m_dwDelPieceID = deleteId;
        req.m_byFlag = (System.Byte)flag;
        req.m_stCheckCode = integritySum;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_DEL_REQ, req);
    }

    public void _recvBuildingDelAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_DEL_ACK ack = data as BTPKG_BUILDING_DEL_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            BattleMgr.I().BuildManager.BuildingDelAck(ack);
        }
    }

    public void _recvBuildingDelNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_DEL_NTF ntf = data as BTPKG_BUILDING_DEL_NTF;
        BattleMgr.I().GetManager<BuildingNet>().BuildingDelNtf(ntf);
    }

    public void _recvBuildingOwnerChangeNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_OWNER_CHANGE_NTF ntf = data as BTPKG_BUILDING_OWNER_CHANGE_NTF;
        BattleMgr.I().BuildManager.OwnerChangeNtf(ntf);
        BattleMgr.I().FacilityManager.BuildingOwnerChange(ntf);
    }

    public void sendBuildingPieceUpgrade(uint buildingId, uint ver, uint pieceId)
    {
        BTPKG_BUILDING_PIECE_UPGRADE_REQ req = new BTPKG_BUILDING_PIECE_UPGRADE_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwPieceID = pieceId;
        req.m_dwVer = ver;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_UPGRADE_REQ, req);
    }

    public void _recvBuildingPieceUpgradeAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_PIECE_UPGRADE_ACK ack = data as BTPKG_BUILDING_PIECE_UPGRADE_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {

        }
    }

    public void _recvBuildingPieceUpgradeNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_PIECE_UPGRADE_NTF ntf = data as BTPKG_BUILDING_PIECE_UPGRADE_NTF;
        BattleMgr.I().BuildManager.PieceUpgradeNtf(ntf);
    }


    public void sendFacilityLandcabUpgradeReq(uint unitId, uint toLevel)
    {
        BTPKG_FACILITY_LANDCAB_UPGRADE_REQ req = new BTPKG_FACILITY_LANDCAB_UPGRADE_REQ();
        req.m_dwUnitID = unitId;
        req.m_byToLevel = (System.Byte)toLevel;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_LANDCAB_UPGRADE_REQ, req);
    }

    public void sendFacilityLandcabRepairReq(uint unitId)
    {
        BTPKG_FACILITY_LANDCAB_REPAIR_REQ req = new BTPKG_FACILITY_LANDCAB_REPAIR_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_LANDCAB_REPAIR_REQ, req);
    }

    public void _recvFacilityLandcabRepairAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_LANDCAB_REPAIR_ACK ack = data as BTPKG_FACILITY_LANDCAB_REPAIR_ACK;
        checkErrCode(ack.m_nErrCode);
    }


    //建筑升级是通过领地柜
    public void _recvFacilityLandCabUpgradeAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_LANDCAB_UPGRADE_ACK ack = data as BTPKG_FACILITY_LANDCAB_UPGRADE_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {

        }
    }

    public void sendFacilitySetPasswdReq(uint unitId, int password, int flag)
    {
        BTPKG_FACILITY_SET_PASSWD_REQ req = new BTPKG_FACILITY_SET_PASSWD_REQ();
        req.m_dwUnitID = unitId;
        req.m_nPassword = password;
        req.m_byFlag = (System.Byte)flag;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_SET_PASSWD_REQ, req);
    }

    public void sendFacilityCheckPasswdReq(uint unitId, int flag, int password = -1)
    {
        BTPKG_FACILITY_CHECK_PASSWD_REQ req = new BTPKG_FACILITY_CHECK_PASSWD_REQ();
        req.m_dwUnitID = unitId;
        //req.m_byFlag = (System.Byte)flag;
        req.m_nPassword = password;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_CHECK_PASSWD_REQ, req);
    }

    public void _recvFacilityCheckPasswdAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_CHECK_PASSWD_ACK ack = data as BTPKG_FACILITY_CHECK_PASSWD_ACK;
        // 获取处理的设施
        var facility = BattleMgr.I().FacilityManager.GetFacilityObjectByUnitId(ack.m_dwUnitID);
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            // 打开设施
            if (facility != null)
            {
                facility.OpenFacility();
            }
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PASSWORD_IS_NEED)
        {
            // 打开密码界面
            if (facility != null)
            {
                facility.OpenFacilityWithPassword();
            }
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PASSWORD_IS_WRONG)
        {
            // 密码错误
            if (facility != null)
            {
                facility.PwdErrorOperation();
            }
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PWDLOCK_IS_FORBID_CD)
        {
            long leftTime = ack.m_dwLockForbidSec - ServerTimeService.getInstance().getCurrentServerTimeStamp();
            string showTime;
            if (leftTime > 60 * 60)
            {
                showTime = CommonFunctions.FormatLeftTime((uint)leftTime);
            }
            else
            {
                showTime = CommonFunctions.FormatTimeMS(leftTime > 0 ? (uint)leftTime : 0);
            }
            ComMsg.getInstance().showMsg(Lang.Format(TblDataUtils.GetStringByID(StringEnum.String350641), showTime));
        }
        else
        {
            // 其他错误提示
            checkErrCode(ack.m_nErrCode);
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_CHECK_PASSWD_ACK, ack);
        //if (checkErrCode(ack.m_nErrCode))
        //{
        //    if (ack.m_byFlag == 0)
        //    {
        //        BattleMgr.I().FacilityManager.CheckPasswordAck(ack.m_dwUnitID, ack.m_byNeedPasswd == 1);
        //    }
        //    else if (ack.m_byFlag == 1)
        //    {
        //        if (ack.m_byNeedPasswd == 1)
        //            BattleMgr.I().FacilityManager.PwdLockErrorNtf(ack.m_dwUnitID);
        //        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_CHECK_PASSWD_ACK, ack);
        //    }
        //}
    }
    public void _recvFacilitySetPasswdAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_SET_PASSWD_ACK ack = data as BTPKG_FACILITY_SET_PASSWD_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_SET_PWDLOCK_ACK, ack);
        }
    }
    public void _recvFacilityAddPwdlockNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_ADD_PWDLOCK_NTF ntf = data as BTPKG_FACILITY_ADD_PWDLOCK_NTF;
        BattleMgr.I().FacilityManager.AddPwdLock(ntf.m_dwUnitID, ntf.m_dwPwdLockOwnerID, ntf.m_byHavePwdLock == 1);
    }

    public void _recvFacilityVirtualNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_VIRTUALIZED_NTF ntf = data as BTPKG_FACILITY_VIRTUALIZED_NTF;
        BattleMgr.I().FacilityManager.RecvFacilityVirtualNtf(ntf);
        if (ntf.m_byVirtualState == 1)
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_VIRTUAL_NTF, ntf);
        }

    }


    public void _recvBuildingUpgradeNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_UPGRADE_NTF ntf = data as BTPKG_BUILDING_UPGRADE_NTF;
        BattleMgr.I().GetManager<BuildingNet>().BuildingUpgradeNtf(ntf.m_dwBuildingID, ntf.m_dwVer, ntf.m_oListUpgradePiece);
    }

    public void _recvBuildingRustNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_RUST_NTF ntf = data as BTPKG_BUILDING_RUST_NTF;
        BattleMgr.I().GetManager<BuildingNet>().RustNtf(ntf.m_dwBuildingID, ntf.m_oListRustPiece);
    }

    public void _recgBuildingRustDownNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_RUST_DOWM_NTF ntf = data as BTPKG_BUILDING_RUST_DOWM_NTF;
        BattleMgr.I().GetManager<BuildingNet>().RustDownNtf(ntf.m_dwBuildingID);
    }

    public void sendBuildingHpReq(uint buildingId)
    {
        BTPKG_BUILDING_HP_REQ req = new BTPKG_BUILDING_HP_REQ();
        req.m_dwBuildingID = buildingId;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_HP_REQ, req);
    }

    public void _recvBuildingHpAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_HP_ACK ack = data as BTPKG_BUILDING_HP_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            BattleMgr.I().BuildManager.BuildingHpAck(ack.m_dwBuildingID, ack.m_dwHPVer, ack.m_oListPieceHP);
        }
    }

    public void _recvBuildingHpVerNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_HP_VER_NTF ntf = data as BTPKG_BUILDING_HP_VER_NTF;
        BattleMgr.I().BuildManager.BuildingHpVerNtf(ntf.m_dwBuildingID, ntf.m_dwHPVer);
    }

    public void sendBuildingPieceRepair(uint buildingId, uint pieceId)
    {
        BTPKG_BUILDING_PIECE_REPAIR_REQ req = new BTPKG_BUILDING_PIECE_REPAIR_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwPieceID = pieceId;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_PIECE_REPAIR_REQ, req);
    }

    public void _recvBuildingPieceRepairAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_PIECE_REPAIR_ACK ack = data as BTPKG_BUILDING_PIECE_REPAIR_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {

        }
    }

    public void _recvBuildingPieceRepairNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_PIECE_REPAIR_NTF ntf = data as BTPKG_BUILDING_PIECE_REPAIR_NTF;
        BattleMgr.I().GetManager<BuildingNet>().PieceRepairNtf(ntf);
        Sound.I().PlaySound(SoundEnum.Sound495);
    }

    public void _skinChangeNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_SKIN_CHANGE_NTF ntf = data as BTPKG_BUILDING_SKIN_CHANGE_NTF;
        BattleMgr.I().GetManager<BuildingNet>().SkinChangeNtf(ntf);
    }

    public void _recvBuildingRepairNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_REPAIR_NTF ntf = data as BTPKG_BUILDING_REPAIR_NTF;
        BattleMgr.I().GetManager<BuildingNet>().BuildingRepairNtf(ntf.m_dwBuildingID);
    }

    public void sendBuildingFacilityMakeReal()
    {
        if (PlayerData.getInstance().m_oLingDiGuiData.TerritoryData != null && PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_oListVirtualBuildings.Count > 0)
        {
            BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ req = new BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ();
            for (int i = 0; i < PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_oListVirtualBuildings.Count; i++)
            {
                BuildingHolder holder = BattleMgr.I().BuildManager.GetHolderByBuildId(PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_oListVirtualBuildings[i]);
                if (holder == null) continue;
                req.m_oListCheckCode.Add(holder.GetCheckCode());
            }
            req.m_dwBuildingID = PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_oListVirtualBuildings[0];
            sendMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_MAKE_REAL_REQ, req);
        }

    }

    public void sendBuildingPieceMakeReal(uint buildingId, uint pieceId)
    {
        BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ req = new BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ();
        req.m_dwBuildingID = buildingId;
        req.m_dwPieceID = pieceId;
        BuildingHolder holder = BattleMgr.I().BuildManager.GetHolderByBuildId(buildingId);
        if (holder != null)
        {
            req.m_oListCheckCode.Add(holder.GetCheckCode());
        }

        sendMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_MAKE_REAL_REQ, req);
    }

    public void sendFacilityMakeReal(uint facilityUnitId)
    {
        BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ req = new BTPKG_BUILDING_FACILITY_MAKE_REAL_REQ();
        req.m_dwFacilityUnitID = facilityUnitId;
        sendMsg(GameServerProto_MSG.BTID_BUILDING_FACILITY_MAKE_REAL_REQ, req);
    }

    private void _recvBuildingFacilityMakeRealAck(GMDT_BASE data)
    {
        BTPKG_BUILDING_FACILITY_MAKE_REAL_ACK ack = data as BTPKG_BUILDING_FACILITY_MAKE_REAL_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            Sound.I().PlaySound(SoundEnum.Sound495);
        }
    }

    private void _recvBuildingFacilityVirtualRealNTF(GMDT_BASE data)
    {
        BTPKG_BUILDING_FACILITY_VIRTUAL_REAL_NTF ntf = data as BTPKG_BUILDING_FACILITY_VIRTUAL_REAL_NTF;
        if (ntf.m_byState == GameServerProto_DEF.PT_BT_BUILDING_FACILITY_STATE_NORMAL)
        {
            BattleMgr.I().BuildManager.BuildingFacilityVirtualReal(ntf);
        }

    }

    private void _recvBuildingFacilityMakeRealWarningNTF(GMDT_BASE data)
    {
        BTPKG_BUILDING_FACILITY_MAKE_REAL_WARNING_NTF ntf = data as BTPKG_BUILDING_FACILITY_MAKE_REAL_WARNING_NTF;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(ntf.m_dwStringID) + ":" + ntf.m_dwSeconds + "s", ComMsg.MsgStyle.Red);
    }

    private void _recvBuildingFacilityVirtualRealUpdateNTF(GMDT_BASE data)
    {
        BTPKG_BUILDING_FACILITY_VIRTUAL_REAL_UPDATE_NTF ntf = data as BTPKG_BUILDING_FACILITY_VIRTUAL_REAL_UPDATE_NTF;
        if (PlayerData.getInstance().m_oLingDiGuiData.TerritoryData != null)
        {
            PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_oListVirtualBuildings = ntf.m_oListVirtualBuildings;
            PlayerData.getInstance().m_oLingDiGuiData.TerritoryData.m_dwNextV2RTimeStamp = ntf.m_dwNextV2RTimeStamp;
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_LINGDIGUI_CHANGE_LEVEL_NTF);
        }
    }

    public void _recvBuildingPieceVirtualNtf(GMDT_BASE data)
    {
        BTPKG_BUILDING_PIECE_MAKE_REAL_NTF ntf = data as BTPKG_BUILDING_PIECE_MAKE_REAL_NTF;
        BattleMgr.I().BuildManager.BuildingPieceVirtualReal(ntf);
    }

    public void sendRecommedSceneReq()
    {
        GMPKG_SCENE_RECOMMEND_REQ req = new GMPKG_SCENE_RECOMMEND_REQ();
        sendMsg(GameServerProto_MSG.GMID_SCENE_RECOMMEND_REQ, req);
    }

    public void _recvRecommedSceneAck(GMDT_BASE data)
    {
        GMPKG_SCENE_RECOMMEND_ACK ack = data as GMPKG_SCENE_RECOMMEND_ACK;
        
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }
        
    }

    public void RecvRecommedSceneNtf(GMDT_BASE data)
    {
        var ntf = data as GMPKG_SCENE_RECOMMEND_NTF;
        PlayerData.getInstance().m_oSelectModeData.m_HuntModeMatch = false;
        PlayerData.getInstance().m_oSelectModeData.m_HuntMatchTimeStamp = 0;
        if (!checkErrCode(ntf.m_nErrCode))
        {
            UIMaincity.ResetNetworkState();
            return;
        }

        UIMaincity uiMain = (UIMaincity)UIManage.I().GetWindow<UIMaincity>();
        if (uiMain != null)
        {
            if (string.IsNullOrEmpty(ntf.m_strLocalSceneID))
            {
                PlayerData.getInstance().m_oSelectModeData.m_JoinScene = ntf.m_stRecommendScene;
                uiMain.EnterStrainScene(ntf.m_stRecommendScene.m_strPublicIP, ntf.m_stRecommendScene.m_wPort, ntf.m_stRecommendScene.m_dwUniqueSvrID, ntf.m_stRecommendScene.m_strSceneID, 1);
            }
            else
            {
                PlayerData.getInstance().m_oSelectModeData.m_JoinScene = new GMDT_SCENE()
                {
                    m_wMode = (ushort)GameServerProto_DEF.PT_SCENE_MODE_STANDARD,
                };
                uiMain.EnterStrainScene(LoginData.I().LobbyIp, LoginData.I().LobbyPort, LoginData.I().ServerId, ntf.m_strLocalSceneID, 1);
            }

        }
    }

    public void sendEnterSceneReq(String sceneID, String password)
    {
        BTPKG_ENTER_SCENE_REQ req = new BTPKG_ENTER_SCENE_REQ();
        req.m_strSceneID = sceneID;
        req.m_strPassword = password;
        Util.Log("进入战斗场景协议请求");
        sendMsg(GameServerProto_MSG.BTID_ENTER_SCENE_REQ, req);
    }

    public void _recvEnterSceneAck(GMDT_BASE data)
    {
        BTPKG_ENTER_SCENE_ACK ack = data as BTPKG_ENTER_SCENE_ACK;
        Util.Log("进入战斗场景协议Ack:code:{0}", ack.m_nErrCode);
        if (!checkErrCode(ack.m_nErrCode))
        {
            //UIWait.I().Hide();
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ENTER_SCENE_FAILED, ack);
            if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SCENE_ENDED)
            {
                //战局已关闭跳转至模式选择界面
                //UIManage.I().SendMsg<UIWindowSelectMode>(1, 1);
                UIManage.I().Show<UIWindowSelectMode>(p =>
                {
                    p.InitData(true);
                });
            }

            if (LoginData.I().CurReconectState == LoginData.ReconectState.BattleConnecting)
            {
                LoginData.I().CurReconectState = LoginData.ReconectState.BattleEnterFail;
            }
            if (LoginData.I().CheckNeedConnect(LoginData.I().LobbyIp, LoginData.I().LobbyPort, true))
            {
                // 需要重连大厅服务器
                LoginData.I().ConnectToLobby();
            }
            return;
        }
        else
        {
            Scene.I().sceneId = ack.m_strSceneID;
            PlayerData.getInstance().m_oSettlementData.EnterSceneId = ack.m_strSceneID;
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ENTER_SCENE_ACK, ack);
    }

    public void SendQuitSceneReq(string sceneID)
    {
        GMPKG_SCENE_QUIT_REQ req = new GMPKG_SCENE_QUIT_REQ();
        req.m_strSceneID = sceneID;
        sendMsg(GameServerProto_MSG.GMID_SCENE_QUIT_REQ, req);
    }

    public void RecvQuitSceneAck(GMDT_BASE data)
    {
        GMPKG_SCENE_QUIT_ACK ack = data as GMPKG_SCENE_QUIT_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            
        }
    }

    public void _recvEnterSceneNtf(GMDT_BASE data)
    {
        BTPKG_ENTER_SCENE_NTF ntf = data as BTPKG_ENTER_SCENE_NTF;
        Scene.I().battleModeId = ntf.m_wSceneMode;
        //重置UDP 序号
        FGUdpProtocolProc.GetInstance().ResetSerialNum();
        if (null == _bcm)
        {
            _bcm = BattleMgr.I().GetManager<BattleCommonManager>();
        }

        if (null != _bcm)
            _bcm.ResetMoveSeq();
        int soundId = Sound.I().PlaySound(SoundEnum.Sound519);
        Scene.I().PreloadLoadingBg(ntf.m_dwSceneStartTime, ntf.m_dwServerTime, () =>
        {
            BattleMgr.I().EnterBattle(ntf);
        });
    }

    public void sendSceneReadyReq()
    {
        BTPKG_SCENE_READY_REQ req = new BTPKG_SCENE_READY_REQ();
        sendMsg(GameServerProto_MSG.BTID_SCENE_READY_REQ, req);
    }

    public void _recvSceneReadyAck(GMDT_BASE data)
    {
        BTPKG_SCENE_READY_ACK ack = data as BTPKG_SCENE_READY_ACK;
        Util.Log("SceneReadyAck:Code{0}", ack.m_nErrCode);

        if (!checkErrCode(ack.m_nErrCode))
        {

        }

    }

    public void sendLeaveSceneReq(byte quit = 0)
    {
        BTPKG_LEAVE_SCENE_REQ req = new BTPKG_LEAVE_SCENE_REQ();
        req.m_byQuit = quit;
        sendMsg(GameServerProto_MSG.BTID_LEAVE_SCENE_REQ, req);
    }

    public void _recvLeaveSceneAck(GMDT_BASE data)
    {
        BTPKG_LEAVE_SCENE_ACK ack = data as BTPKG_LEAVE_SCENE_ACK;

        if (LoginData.I().SelectIp)
        {
            if (ack.m_oListServer.Count > 0)
            {
                GMDT_SERVER server = ack.m_oListServer[0];
                for (int i = 0; i < ack.m_oListServer.Count; i++)
                {
                    if (ack.m_oListServer[i].m_dwLoad < server.m_dwLoad)
                    {
                        server = ack.m_oListServer[i];
                    }
                }
                LoginData.I().SetOnceLobbyServer(server.m_strIP, server.m_wPort, server.m_dwUniqueSvrID);
            }
            else
            {
                Util.LogError("BTPKG_LEAVE_SCENE_ACK 返回的列表为空");
            }

        }

        BattleMgr.I().LeaveBattle();
    }

    public void _recvEnterSceneDoneNtf(GMDT_BASE data)
    {
        BTPKG_ENTER_SCENE_DONE_NTF ntf = data as BTPKG_ENTER_SCENE_DONE_NTF;
        PlayerData.getInstance().m_oTalentData.SetData(ntf.m_stActor.m_stTalentBag);
        Util.Log("EnterSceneDoneNtf");
        if (Player.I() != null)
        {
            BattleMgr.I().m_IsRevel = ntf.m_byRevel == 1;
            if (ntf.m_byRevel == 1)
            {
                if (PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag == 0)
                {
                    Player.I().killVal = 0;
                    PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag = 1;
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PVP_FLAG_CHANGE, (byte)1);
                }
            }
            if (ntf.m_byRevelFlag == 1)
            {
                ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423118));
            }
            BagDataOp.I().enterSence(ntf.m_stActor);
            PlayerActorInfo.Instance.PlayerActor = ntf.m_stActor;
            Player.I().m_dwMoveSeq = ntf.m_stActor.m_dwMoveSeq;
            Player.I().platfromId = ntf.m_dwPlatformID;
            Player.I().posture = ntf.m_stActor.m_byPosture;
            Player.I().startPos = BattleUtils.BTDTV3_TO_V3(ntf.m_stPos);
            Debug.Log("_recvEnterSceneDoneNtf pos:" + Player.I().startPos);
            Player.I().startRotation = BattleUtils.BTDTV3_TO_V3(ntf.m_stEuler);
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_SEL_WEAPON_NTF);
        }
        PlayerData.getInstance().m_oTeamData.SetTeammatesInfo(ntf.m_stActor.m_stTeam);
        BattleMgr.I().LoadState = BattleMgr.BattleState.EnterViewServerDone;
        //BattleMgr.I().m_npcManager.LoadBornNPC(ntf.m_stActor.m_stBornBag);

        if (LoginData.I().CurReconectState == LoginData.ReconectState.BattleConnecting)
        {
            LoginData.I().CurReconectState = LoginData.ReconectState.BattleEnterSceneDone;
        }
    }

    public void _recvSceneSimpleInfoNtf(GMDT_BASE data)
    {
        BTPKG_SCENE_SIMPLE_INFO_NTF ntf = data as BTPKG_SCENE_SIMPLE_INFO_NTF;
        PlayerData.getInstance().m_oSettlementData.UpdateCanSettle(ntf.m_byCanSettle);

        BattleMgr.I().m_dwSceneRecoverFromTime = ntf.m_dwPauseTimeSum;
        BattleMgr.I().m_dwEndTime = ntf.m_dwEndTime;
        Scene.I().battleModeId = ntf.m_wMode;
        Scene.I().battleRoomId = ntf.m_dwRoomID;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_BATTLEEND_COUNTDOWN);
        UIWindowFightMove fightMove = UIManage.I().GetWindow<UIWindowFightMove>();
        if (fightMove != null)
        {
            fightMove.UpdateInfo();
        }
    }

    public void _recvEndSceneNtf(GMDT_BASE data)
    {
        SDKManager.I().RptFZBData2();
        BTPKG_SCENE_END_NTF ntf = data as BTPKG_SCENE_END_NTF;
        if (ntf.m_strSceneID != PlayerData.getInstance().m_oSettlementData.EnterSceneId)
            return;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1420323));
        GameTimer.Instance.StartTimerByTimeOnce(5, (time) =>
        {
            HandlerManager.getInstance().battleHandler.sendLeaveSceneReq();
        });
        return;
    }

    public void _recvUnitIntVarNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_UNIT_INT_VAR_NTF;
        List<BTDT_UNIT_INT_VAR> vars = null;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            // 这里是赵画之前写的mod逻辑
            if (ntf.m_dwUnitID == Player.I().unitId)
            {
                vars = PlayerActorInfo.Instance.PlayerActor.m_oListIntVar;
            }
            else
            {
                var player = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwUnitID);
                if (player != null)
                {
                    vars = player.m_unit.m_oListIntVar;
                }
            }

            if (vars != null)
            {
                var item = vars.FirstOrDefault(it => it.m_strKey == ntf.m_strKey);
                if (item != null)
                {
                    item.m_lValue = ntf.m_lValue;
                }
                else
                {
                    var var = new BTDT_UNIT_INT_VAR();
                    var.m_lValue = ntf.m_lValue;
                    var.m_strKey = ntf.m_strKey;
                    vars.Add(var);
                }
            }
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_FACILITY)
        {
            var facilityObj = BattleMgr.I().FacilityManager.GetFacilityObjectByUnitId(ntf.m_dwUnitID);
            facilityObj.RecvFacilityUnitIntVarNtf(ntf);
        }


    }

    public void _recvUnitStringVarNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_UNIT_STRING_VAR_NTF;
        List<BTDT_UNIT_STRING_VAR> vars = null;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            if (ntf.m_dwUnitID == Player.I().unitId)
            {
                vars = PlayerActorInfo.Instance.PlayerActor.m_oListStringVar;
            }
            else
            {
                var player = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwUnitID);
                if (player != null)
                {
                    vars = player.m_unit.m_oListStringVar;
                }
            }
        }

        if (vars != null)
        {
            var item = vars.FirstOrDefault(it => it.m_strKey == ntf.m_strKey);
            if (item != null)
            {
                item.m_strValue = ntf.m_strValue;
            }
            else
            {
                var var = new BTDT_UNIT_STRING_VAR();
                var.m_strValue = ntf.m_strValue;
                var.m_strKey = ntf.m_strKey;
                vars.Add(var);
            }
        }
    }

    public void _recvUnitNumberVarNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_UNIT_NUMBER_VAR_NTF;
        List<BTDT_UNIT_DOUBLE_VAR> vars = null;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            if (ntf.m_dwUnitID == Player.I().unitId)
            {
                vars = PlayerActorInfo.Instance.PlayerActor.m_oListDoubleVar;
            }
            else
            {
                var player = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwUnitID);
                if (player != null)
                {
                    vars = player.m_unit.m_oListDoubleVar;
                }
            }
        }

        if (vars != null)
        {
            var item = vars.FirstOrDefault(it => it.m_strKey == ntf.m_strKey);
            if (item != null)
            {
                item.m_lValue = ntf.m_lValue;
            }
            else
            {
                var var = new BTDT_UNIT_DOUBLE_VAR();
                var.m_lValue = ntf.m_lValue;
                var.m_strKey = ntf.m_strKey;
                vars.Add(var);
            }
        }
    }

    public void sendResCollect(ushort resType, uint resId)
    {
        BTPKG_RES_COLLECT_REQ req = new BTPKG_RES_COLLECT_REQ();
        req.m_wResType = resType;
        req.m_dwResID = resId;
        sendMsg(GameServerProto_MSG.BTID_RES_COLLECT_REQ, req);
    }

    public void _recvResCollectAck(GMDT_BASE data)
    {
        BTPKG_RES_COLLECT_ACK ack = data as BTPKG_RES_COLLECT_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            if (Player.I() == null)
                return;
            BattleMgr.I().CollectAck(ack.m_wResType, ack.m_dwResID, ack.m_nHP, ack.m_nHPChange, Player.I().unitId);
        }
    }

    public void _recvResResetNtf(GMDT_BASE data)
    {
        BTPKG_RES_RESET_NTF ntf = data as BTPKG_RES_RESET_NTF;
        BattleMgr.I().ResResetNtf(ntf.m_wResType, ntf.m_dwResID);
    }

    public void _recvResCollectNtf(GMDT_BASE data)
    {
        BTPKG_RES_COLLECT_NTF ntf = data as BTPKG_RES_COLLECT_NTF;
        BattleMgr.I().CollectAck(ntf.m_wResType, ntf.m_dwResID, ntf.m_nHP, ntf.m_nHPChange, ntf.m_dwCollecterUnitID);
    }

    public void sendBoxbagPickReq(uint unitId, uint slot, int count)
    {
        BTPKG_LOCKBOX_GET_REWARD_REQ req = new BTPKG_LOCKBOX_GET_REWARD_REQ();
        req.m_dwUnitID = unitId;
        req.m_bySlot = (byte)slot;
        req.m_nCount = count;
        sendMsg(GameServerProto_MSG.BTID_LOCKBOX_GET_REWARD_REQ, req);
    }




    //开镜
    public void RptOpenSight(byte open)
    {
        BTPKG_OPEN_SIGHT_RPT rpt = new BTPKG_OPEN_SIGHT_RPT
        {
            m_byOpen = open
        };
        sendMsg(GameServerProto_MSG.BTID_OPEN_SIGHT_RPT, rpt);
    }


    //测试协议
    public void sendDropbagAddReq()
    {
        BTPKG_DROPBAG_DROP_REQ req = new BTPKG_DROPBAG_DROP_REQ();
        sendMsg(GameServerProto_MSG.BTID_DROPBAG_DROP_REQ, req);
    }


    public void _recvUnitBoardNameNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_BOARDNAME_NTF ntf = data as BTPKG_UNIT_BOARDNAME_NTF;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_MONSTER)
        {
            var monster = BattleMgr.I().AIMonsterManager.GetAIMonster(ntf.m_dwUnitID);
            if (monster != null)
            {
                monster.AIFeatureHeadShow.SetShowName(ntf.m_strBoardName);
            }
        }
        else
        {
            var treasure = BattleMgr.I().GetManager<TreasureManager>().GetTreasureObject(ntf.m_dwUnitID);
            if (treasure != null)
            {
                treasure.SetShowName(ntf.m_strBoardName);
            }
        }

    }

    public void SendUnitMoveReq(BTPKG_UNIT_MOVE_REQ req)
    {
        if (null == _bcm)
            _bcm = BattleMgr.I().GetManager<BattleCommonManager>();
        req.m_stCommon.m_qwPacketSeq = FGUdpProtocolProc.GetInstance().GenSerialNum();
        req.m_stCommon.m_dwMoveSeq = Player.I().m_dwMoveSeq;
        req.m_stCommon.m_qwTimeStampMS = ServerTimeService.getInstance().GetCurrentClientTickCount();

        NetworkLatencyManager.Instance.AddRequest(GameServerProto_MSG.BTID_UNIT_MOVE_ACK, req.m_stCommon.m_qwPacketSeq);

        if (null == _bcm || !_bcm.IfSendUdp())
        {
            sendMsg(GameServerProto_MSG.BTID_UNIT_MOVE_REQ, req);
        }
        else if (_bcm.IfSendBoth())
        {
            sendMsg(GameServerProto_MSG.BTID_UNIT_MOVE_REQ, req);
            SendUDPProtocol(GameServerProto_MSG.BTID_UNIT_MOVE_REQ, req);
        }
        else
        {
            sendUnitMoveReqUdp(req);
        }
    }

    public void sendUnitMoveReqUdp(BTPKG_UNIT_MOVE_REQ req)
    {
        SendUDPProtocol(GameServerProto_MSG.BTID_UNIT_MOVE_REQ, req);
    }

    public void _recvUnitMoveAck(GMDT_BASE data)
    {
        BTPKG_UNIT_MOVE_ACK ack = data as BTPKG_UNIT_MOVE_ACK;
        NetworkLatencyManager.Instance.UpdateRequest(GameServerProto_MSG.BTID_UNIT_MOVE_ACK, ack.m_qwPacketSeq, ack.m_stCurTimeEx);
    }

    public void _recvUnitMoveNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_MOVE_NTF ntf = data as BTPKG_UNIT_MOVE_NTF;
        // SKY.Util.LogError("_recvUnitMoveNtf seq : " + ntf.m_qwPacketSeq);
        // Util.Log($"_recvUnitMoveNtf ntf.m_dwUnitID:{ntf.m_dwUnitID} ntf.m_qwPacketSeq:{ntf.m_qwPacketSeq}");
        if (_bcm != null)
        {
            if (!_bcm.VerifiMoveSeq(ntf.m_stCommon.m_dwUnitID, ntf.m_stCommon.m_qwPacketSeq))
            {
                return;
            }
        }

        BattleMgr.I().RecvUnitMoveNtf(ntf);
    }

    public void ReqUnitEnterWaterRPT(uint unitId, bool enter)
    {
        BTPKG_UNIT_ENTER_WATER_RPT req = new BTPKG_UNIT_ENTER_WATER_RPT();
        req.m_dwUnitID = unitId;
        req.m_byEnter = (byte)(enter ? 1 : 0);
        sendFGMessage(GameServerProto_MSG.BTID_UNIT_ENTER_WATER_RPT, req);
    }

    public void sendActorSetPosTureReq(byte posture, byte additivePosture)
    {
        BTPKG_ACTOR_SET_POSTURE_REQ req = new BTPKG_ACTOR_SET_POSTURE_REQ();
        req.m_byPosture = posture;
        req.m_byAdditivePosture = additivePosture;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_SET_POSTURE_REQ, req);
    }

    public void _recvActorSetPosTureAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_SET_POSTURE_ACK ack = data as BTPKG_ACTOR_SET_POSTURE_ACK;
    }

    public void _recvActorSetPosTureNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_SET_POSTURE_NTF ntf = data as BTPKG_ACTOR_SET_POSTURE_NTF;
        BattleMgr.I().RecvActorSetPosTureNtf(ntf);
    }

    public void sendActorJumpReq(BTDT_VECTOR3 jumpParam)
    {
        BTPKG_ACTOR_JUMP_REQ req = new BTPKG_ACTOR_JUMP_REQ();
        req.m_stData = jumpParam;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_JUMP_REQ, req);
    }

    public void _recvActorJumpAck(GMDT_BASE data)
    {

    }

    public void _recvActorJumpNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_JUMP_NTF ntf = data as BTPKG_ACTOR_JUMP_NTF;
        BattleMgr.I().RecvActorJumpNtf(ntf);
    }

    public void _recvRevelNtf(GMDT_BASE data)
    {
        BTPKG_REVEL_NTF ntf = data as BTPKG_REVEL_NTF;
        BattleMgr.I().m_IsRevel = true;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423118));
        if (PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag == 0)
        {
            Player.I().killVal = 0;
            PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag = 1;
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PVP_FLAG_CHANGE, (byte)1);
        }
    }

    public void sendActorDisplayReq(ulong roleid)
    {
        BTPKG_ACTOR_DISPLAY_REQ req = new BTPKG_ACTOR_DISPLAY_REQ();
        req.m_qwRoleID = roleid;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_DISPLAY_REQ, req);
    }

    private void _recActorDisplayAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_DISPLAY_ACK ack = data as BTPKG_ACTOR_DISPLAY_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PLAYER_DISPLAY_CHECK, ack);
    }

    Dictionary<uint, Action> MineCallBacks = new Dictionary<uint, Action>();

    public void removeMineCallBack(uint unitId)
    {
        MineCallBacks[unitId] = null;
    }

    public void addMineCallBack(uint unitId, Action cb)
    {
        MineCallBacks[unitId] = cb;
    }

    public void sendActorFireReq(uint weapon, string dummy, uint index, List<BTDT_FIRE_BULLET> bulletList, uint unitId = 0, byte remoteFlag = 0)
    {
        //Debug.Log("sendActorFireReq fireIndex: " + index);
        BTPKG_ACTOR_FIRE_REQ req = new BTPKG_ACTOR_FIRE_REQ
        {
            m_dwUnitID = unitId,
            m_byRemoteFlag = remoteFlag,
            m_dwWeapon = weapon,
            m_strDummy = dummy,
            m_dwFireIndex = index,
            m_oListBullet = bulletList,
            m_qwFireTime = ServerTimeService.getInstance().GetCurrentClientTickCount()
        };
        req.m_qwPacketSeq = FGUdpProtocolProc.GetInstance().GenSerialNum();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
        SendUDPProtocol(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
    }

    public void sendMineActorFireReq(uint weapon, string dummy, uint index, List<BTDT_FIRE_BULLET> bulletList, uint unitId = 0, byte remoteFlag = 0, Action callBack = null)
    {
        if (callBack != null)
        {
            MineCallBacks[unitId] = callBack;
        }
        //Debug.Log("sendActorFireReq fireIndex: " + index);
        BTPKG_ACTOR_FIRE_REQ req = new BTPKG_ACTOR_FIRE_REQ
        {
            m_dwUnitID = unitId,
            m_byRemoteFlag = remoteFlag,
            m_dwWeapon = weapon,
            m_strDummy = dummy,
            m_dwFireIndex = index,
            m_oListBullet = bulletList,
            m_qwFireTime = ServerTimeService.getInstance().GetCurrentClientTickCount()
        };
        req.m_qwPacketSeq = FGUdpProtocolProc.GetInstance().GenSerialNum();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
        SendUDPProtocol(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
    }

    public void sendActorFireReq(BTPKG_ACTOR_FIRE_REQ req)
    {
        req.m_qwPacketSeq = FGUdpProtocolProc.GetInstance().GenSerialNum();
        req.m_qwFireTime = ServerTimeService.getInstance().GetCurrentClientTickCount();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
        SendUDPProtocol(GameServerProto_MSG.BTID_ACTOR_FIRE_REQ, req);
    }

    public void _recvActorFireAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_FIRE_ACK ack = data as BTPKG_ACTOR_FIRE_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            if (ack.m_dwMineUnitID != 0 && MineCallBacks[ack.m_dwMineUnitID] != null)
            {
                MineCallBacks[ack.m_dwMineUnitID]();
                MineCallBacks[ack.m_dwMineUnitID] = null;
            }
            if (ack.m_nBulletCount == -1 || ack.m_nDurability == -1)
            {
                return;
            }

            int slot = ack.m_wWeaponSlot;
            GMDT_ITEM shortcutItem = BagDataOp.I().GetShortcutItemBySlot((ushort)slot);
            if(shortcutItem == null)
                return;
            if(shortcutItem.m_dwItemID == ack.m_dwWeaponID)
            {
                BagDataOp.I().UpdateShortcutBulletCountBySolt(slot, ack.m_nBulletCount);
                BagDataOp.I().UpdateShortcutDurabilityBySolt(slot, ack.m_nDurability);
            }
            //有可能是放置了炸弹，清除状态
            BattleMgr.I().m_c4Manager.ReleaseLock();
        }
        else
        {
            //SKY.Util.LogError("攻击判定失败,需要客户端重置子弹 errorCode: "+ ack.m_nErrCode);
            BattleMgr.I().m_c4Manager.ReleaseLock();
        }
    }
    public void _recvActorFireNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_FIRE_NTF ntf = data as BTPKG_ACTOR_FIRE_NTF;
        if (!VerifiFireIndex(ntf.m_dwFireIndex)) return;
        // Debug.LogError("recvActorFireNtf fireIndex: " + ntf.m_dwFireIndex);
        BattleMgr.I().RecvActorFireNtf(ntf);
    }

    public void sendActorFireHitReq(uint index, uint weaponID, BTDT_BULLET_HIT bulletHit)
    {
        //Debug.Log("sendActorFireHitReq fireIndex: " + index);
        BTPKG_ACTOR_FIRE_HIT_REQ req = new BTPKG_ACTOR_FIRE_HIT_REQ();
        req.m_dwFireIndex = index;
        req.m_dwWeapon = weaponID;
        req.m_stBullet = bulletHit;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_FIRE_HIT_REQ, req);
    }

    public void _recvActorFireHitAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_FIRE_HIT_ACK ack = data as BTPKG_ACTOR_FIRE_HIT_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            if (ack.m_nDurability == -1) return;
            BagDataOp.I().UpdateShortcutDurabilityBySolt((int)BagDataOp.I().getCurSelectSlotId(), ack.m_nDurability);
        }
        else
        {
            //SKY.Util.LogError("ActorFireHitAck 判定失败");
        }
    }

    public void _recvActorFireHitNtf(GMDT_BASE data)
    {

        BTPKG_ACTOR_FIRE_HIT_NTF ntf = data as BTPKG_ACTOR_FIRE_HIT_NTF;
        BattleMgr.I().ActorFireHitNtf(ntf);

        //BTPKG_ACTOR_FIRE_HIT_NTF.m_dwUnitID 这个在没有实体的时候（子弹，手雷），是攻击者的ID
        //如果有实体（C4），是实体本身的UnitID
        if (ntf.m_dwWeapon == 0)
        {
            return;
        }
        var weaponInfo = CTblAll.g_oTblFgWeapon.Get(ntf.m_dwWeapon);
        if (weaponInfo != null)
        {
            if (weaponInfo.m_wSkillType == 3)
            {
                BattleMgr.I().m_c4Manager.PlayC4BombEffect(ntf.m_dwUnitID);
            }
            else if (weaponInfo.m_wSkillType == 4)
            {
                Vector3 pos = BattleUtils.BTDTV3_TO_V3(ntf.m_stBullet.m_stBulletPos);
                BattleMgr.I().m_c4Manager.PlayShouleiBombEffect(ntf.m_dwUnitID, ntf.m_dwFireIndex, pos);
            }
        }
    }

    public void sendActorFallDownReq(Vector3 fromPos, Vector3 fromEuler, Vector3 toPos, Vector3 toEuler, int harmFlag)
    {
        BTPKG_ACTOR_FALLDOWN_REQ req = new BTPKG_ACTOR_FALLDOWN_REQ();
        req.m_stFromPos = BattleUtils.V3_TO_BTDTV3(fromPos);
        req.m_stToPos = BattleUtils.V3_TO_BTDTV3(toPos);
        req.m_stFromEuler = BattleUtils.V3_TO_BTDTV3(fromEuler);
        req.m_stToEuler = BattleUtils.V3_TO_BTDTV3(toEuler);
        req.m_byHarmFlag = (Byte)harmFlag;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_FALLDOWN_REQ, req);
    }

    public void SendVehicleFallDownReq(uint unitId, float height)
    {
        var req = new BTPKG_VEHICLE_FALLDOWN_REQ();
        req.m_dwUnitID = unitId;
        req.m_nFallHeight = (int)height * 1000;

        sendMsg(GameServerProto_MSG.BTID_VEHICLE_FALLDOWN_REQ, req);
    }

    public void _recvActorFallDownAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_FALLDOWN_ACK ack = data as BTPKG_ACTOR_FALLDOWN_ACK;
    }

    public void _recvActorWeakNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_WEAK_NTF ntf = data as BTPKG_ACTOR_WEAK_NTF;
        BattleMgr.I().GetManager<PlayerManager>().ActorWeakNtf(ntf);
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_DYING_STATE, ntf);
    }

    public void _recvActorWeakHpNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_WEAK_HP_NTF ntf = data as BTPKG_ACTOR_WEAK_HP_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_DYING_HP_CHANGE, ntf);
    }

    // 自我解脱
    public void sendActorSuicideReq()
    {
        BTPKG_ACTOR_BEGIN_SUICIDE_REQ req = new BTPKG_ACTOR_BEGIN_SUICIDE_REQ();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_BEGIN_SUICIDE_REQ, req);
    }

    public void _recvActorSuicideAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_BEGIN_SUICIDE_ACK ack = data as BTPKG_ACTOR_BEGIN_SUICIDE_ACK;
        Player.I().ShowDyingCountdown(ack.m_dwSuicideEndTime);
    }

    public void _recvActorSuicideBreakNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_SUICIDE_BREAK_NTF ntf = data as BTPKG_ACTOR_SUICIDE_BREAK_NTF;
        BattleMgr.I().GetManager<PlayerManager>().HideCountdown();
    }

    // 使用心脏起搏器
    public void sendActorPacemakerReq(byte bagType, ushort slot)
    {
        BTPKG_ACTOR_PACEMAKER_REQ req = new BTPKG_ACTOR_PACEMAKER_REQ();
        req.m_byBagType = bagType;
        req.m_wItemSlot = slot;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_PACEMAKER_REQ, req);
    }

    public void _recvActorPacemakerAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_PACEMAKER_ACK ack = data as BTPKG_ACTOR_PACEMAKER_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            PlayerActorInfo.Instance.PlayerActor.m_byLifeState = (Byte)GameServerProto_DEF.PT_BT_ACTOR_LIFESTATE_ALIVE;
            PlayerActorInfo.Instance.PlayerActor.m_nHP = ack.m_nHP;
            PlayerActorInfo.Instance.PlayerActor.m_nSTA = ack.m_nSTA;
            PlayerActorInfo.Instance.PlayerActor.m_nWater = ack.m_nWater;
            Player.I().PopDying();
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE);
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE);
        }
    }

    // 救助
    public void sendActorHelpReq(uint helpUnitId)
    {
        BTPKG_ACTOR_HELP_REQ req = new BTPKG_ACTOR_HELP_REQ();
        req.m_dwHelpedUnitID = helpUnitId;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_HELP_REQ, req);
    }

    public void _recvActorHelpAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_HELP_ACK ack = data as BTPKG_ACTOR_HELP_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            Player.I().Help();
            Player.I().ShowHelpCountdown(ack.m_dwHelpEndTime);
        }
    }

    public void _recvActorHelpNtf(GMDT_BASE data)
    {   // 被救助者，开始救助通知
        BTPKG_ACTOR_HELP_NTF ntf = data as BTPKG_ACTOR_HELP_NTF;
        if (Player.I().uuId == ntf.m_dwUnitID)
        {
            BattleMgr.I().GetManager<PlayerManager>().ShowHelpCountdown(ntf.m_dwHelpEndTime);
        }
    }

    public void _recvActorHelpSuccessNtf(GMDT_BASE data)
    {
        // 被救助者 救助成功通知
        BTPKG_ACTOR_HELP_SUCCESS_NTF ntf = data as BTPKG_ACTOR_HELP_SUCCESS_NTF;
        if (ntf.m_byType == 1)//自己被救
        {
            if (Player.I().uuId == ntf.m_dwSelfUnitID)
            {
                if (Player.I().StateFind("濒死") != null)
                {
                    PlayerActorInfo.Instance.PlayerActor.m_byLifeState = (Byte)GameServerProto_DEF.PT_BT_ACTOR_LIFESTATE_ALIVE;
                    PlayerActorInfo.Instance.PlayerActor.m_nHP = ntf.m_nHP;
                    PlayerActorInfo.Instance.PlayerActor.m_nSTA = ntf.m_nSTA;
                    PlayerActorInfo.Instance.PlayerActor.m_nWater = ntf.m_nWater;
                    Player.I().PopDying();
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE);
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE);
                }
                RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwHelpUnitID);
                BattleMgr.I().GetManager<PlayerManager>().Rescuing = false;
                PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Kill, 1321271, remotePlayer.m_unit.m_stActor.m_strRoleName);
            }
        }
        else if (ntf.m_byType == 2)//救别人
        {
            if (Player.I().uuId == ntf.m_dwSelfUnitID)
            {
                if (Player.I().HasState(StateDefine.STATE_HELP))
                {
                    Player.I().PopHelp();
                    UIWindowFight fight = CommonFunctions.GetUIWindowFight();
                    if (fight != null)
                    {
                        fight.SetHelpDelayTime(2);
                    }
                    RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwHelpUnitID);

                    PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Kill, 1321270, remotePlayer.m_unit.m_stActor.m_strRoleName);
                }
            }
        }
    }

    public void _recvActorHelpBreakNtf(GMDT_BASE data)
    {   // 被救助者 救助者，救助打断通知

        BTPKG_ACTOR_HELP_BREAK_NTF ntf = data as BTPKG_ACTOR_HELP_BREAK_NTF;
        Debug.Log("_recvActorHelpBreakNtf unitid： " + ntf.m_dwUnitID);
        if (Player.I().uuId == ntf.m_dwHelperUnitID)// 
        {   // 救助者退出救助状态
            Player.I().PopHelp();
            BattleMgr.I().GetManager<PlayerManager>().HideCountdown();
            BattleMgr.I().GetManager<PlayerManager>().ShowHelpCancelReason(ntf.m_byReason, true);
        }
        else
        {// 被救助者 还是濒死状态
            BattleMgr.I().GetManager<PlayerManager>().HideCountdown();
            BattleMgr.I().GetManager<PlayerManager>().ShowHelpCancelReason(ntf.m_byReason, false);
            BattleMgr.I().GetManager<PlayerManager>().Rescuing = false;
        }
    }

    public void _recvUnitPassiveNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_PASSIVE_NTF ntf = data as BTPKG_UNIT_PASSIVE_NTF;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            Player player = Player.I();
            if (player != null && ntf.m_dwUnitID == player.uuId)
            {
                PlayerActorInfo.Instance.SetPassiveList(ntf.m_oListPassive);
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ACTOR_PASSIVE_REFRESH);
            }
            else
            {
                BattleMgr.I().GetManager<PlayerManager>().SetPassiveList(ntf);

            }
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_MONSTER)
        {
            BattleMgr.I().GetManager<AIMonsterManager>().SetPassiveList(ntf);
        }
    }

    public void SendActionReq(int slot)
    {
        BTPKG_ACTION_REQ req = new BTPKG_ACTION_REQ();
        req.m_nSlot = slot;
        sendMsg(GameServerProto_MSG.BTID_ACTION_REQ, req);
    }

    public void _recvActionACK(GMDT_BASE data)
    {
        BTPKG_ACTION_ACK ack = data as BTPKG_ACTION_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {

        }
    }

    public void _recvActionNtf(GMDT_BASE data)
    {
        BTPKG_ACTION_NTF ntf = data as BTPKG_ACTION_NTF;
        PlayerManager playerManager = BattleMgr.I().GetManager<PlayerManager>();
        if (playerManager != null)
        {
            playerManager.ActorExpressionNtf(ntf);
        }
    }

    public void _recvBuffGetNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_BUFF_GET_NTF ntf = data as BTPKG_UNIT_BUFF_GET_NTF;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            Player player = Player.I();
            if (player != null && ntf.m_dwUnitID == player.uuId)
            {
                for (int i = 0; i < ntf.m_oListBuffOff.Count; i++)
                {
                    for (int j = 0; j < PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.Count; j++)
                    {
                        if (ntf.m_oListBuffOff[i] == PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                        {
                            PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.RemoveAt(j);
                            break;
                        }
                    }
                }
                for (int i = 0; i < ntf.m_oListBuffOn.Count; i++)
                {
                    var buffinfo = CTblAll.g_oTblExtraBuff.Get(ntf.m_oListBuffOn[i].m_dwBuffID);
                    if (buffinfo.m_byType == 2)
                    {
                        int count = 0;
                        for (int j = 0; j < PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.Count; j++)
                        {
                            if (ntf.m_oListBuffOn[i].m_dwBuffID == PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                            {
                                count++;
                                if (count >= buffinfo.m_byStackLayer)//已经达到最大层数
                                {
                                    PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.RemoveAt(0);//把第一个移除
                                    j--;
                                    count = count - 1;
                                }
                            }
                        }
                    }
                    else
                    {
                        for (int j = 0; j < PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.Count; j++)
                        {
                            if (ntf.m_oListBuffOn[i].m_dwBuffID == PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                            {
                                PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff[j] = ntf.m_oListBuffOn[i];
                                ntf.m_oListBuffOn.RemoveAt(i);
                                i--;
                                break;
                            }
                        }
                    }
                }
                PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.AddRange(ntf.m_oListBuffOn);
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ACTOR_BUFF_REFRESH);
            }
            else
            {
                RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().GetMonsterByUintId(ntf.m_dwUnitID);
                if (remotePlayer == null)
                    return;

                for (int i = 0; i < ntf.m_oListBuffOff.Count; i++)
                {
                    for (int j = 0; j < remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.Count; j++)
                    {
                        if (ntf.m_oListBuffOff[i] == remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                        {
                            remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.RemoveAt(j);
                        }
                    }
                }
                for (int i = 0; i < ntf.m_oListBuffOn.Count; i++)
                {
                    var buffinfo = CTblAll.g_oTblExtraBuff.Get(ntf.m_oListBuffOn[i].m_dwBuffID);
                    if (buffinfo.m_byType != 2)//叠加
                    {
                        int count = 0;
                        for (int j = 0; j < remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.Count; j++)
                        {
                            if (ntf.m_oListBuffOn[i].m_dwBuffID == remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                            {
                                count++;
                                if (count >= buffinfo.m_byStackLayer)//已经达到最大层数
                                {
                                    remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.RemoveAt(0);//把第一个移除
                                    j--;
                                    count = count - 1;
                                }
                            }
                        }
                    }
                    else
                    {
                        for (int j = 0; j < remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.Count; j++)
                        {
                            if (ntf.m_oListBuffOn[i].m_dwBuffID == remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                            {
                                remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff[j] = ntf.m_oListBuffOn[i];
                                ntf.m_oListBuffOn.RemoveAt(i);
                                i--;
                                break;
                            }
                        }
                    }
                }
                remotePlayer.m_unit.m_stActor.m_stBuffBag.m_oListBuff.AddRange(ntf.m_oListBuffOn);
            }
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_MONSTER)
        {
            BattleMgr.I().GetManager<AIMonsterManager>().SetMonsterBuff(ntf.m_dwUnitID, ntf.m_oListBuffOn, ntf.m_oListBuffOff);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_VEHICLE)
        {
            BattleMgr.I().GetManager<MountManager>().SetMountBuff(ntf);
        }
    }

    public void _recvBuffDisappearNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_BUFF_DISAPPEAR_NTF ntf = data as BTPKG_UNIT_BUFF_DISAPPEAR_NTF;
        Player player = Player.I();
        if (player != null && ntf.m_dwUnitID == player.uuId)
        {
            for (int i = 0; i < ntf.m_oListBuffID.Count; i++)
            {
                for (int j = 0; j < PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.Count; j++)
                {
                    if (ntf.m_oListBuffID[i] == PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff[j].m_dwBuffID)
                    {
                        PlayerActorInfo.Instance.PlayerActor.m_stBuffBag.m_oListBuff.RemoveAt(j);
                    }
                }
            }
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ACTOR_BUFF_REFRESH);
        }
    }

    public void _recvActorProMaxNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_PROP_MAX_NTF ntf = data as BTPKG_ACTOR_PROP_MAX_NTF;
        Player player = Player.I();
        if (player != null)
        {
            if (player.GetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_HP_MAX) != ntf.m_nHPMax)
            {
                PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_HP_MAX, ntf.m_nHPMax);
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
            }
            if (player.GetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_STA_MAX) != ntf.m_nStaMax)
            {
                PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_STA_MAX, ntf.m_nStaMax);
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE);
            }
            if (player.GetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_WATER_MAX) != ntf.m_nWaterMax)
            {
                PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_WATER_MAX, ntf.m_nWaterMax);
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE);
            }
        }
    }

    public void _recvActorProOverNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_PROP_OVER_NTF ntf = data as BTPKG_ACTOR_PROP_OVER_NTF;
        Player player = Player.I();
        if (player != null)
        {
            if (PlayerActorInfo.Instance.PlayerActor.m_nHP != ntf.m_nHP)
            {
                PlayerActorInfo.Instance.PlayerActor.m_nHP = ntf.m_nHP;
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
            }
            if (PlayerActorInfo.Instance.PlayerActor.m_nSTA != ntf.m_nSTA)
            {
                PlayerActorInfo.Instance.PlayerActor.m_nSTA = ntf.m_nSTA;
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE);
            }
            if (PlayerActorInfo.Instance.PlayerActor.m_nWater != ntf.m_nWater)
            {
                PlayerActorInfo.Instance.PlayerActor.m_nWater = ntf.m_nWater;
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE);
            }

            if (PlayerActorInfo.Instance.PlayerActor.m_nPhysical != ntf.m_nPhysical)
            {
                PlayerActorInfo.Instance.PlayerActor.m_nPhysical = ntf.m_nPhysical;
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PHYSICAL_CHANGE);
            }
        }

    }

    //prop改变
    public void _recvActorProChangeNtf(GMDT_BASE data)
    {
        BTPKG_PROP_CHANGE_NTF ntf = data as BTPKG_PROP_CHANGE_NTF;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            Player player = Player.I();
            if (player != null && player.uuId == ntf.m_dwUnitID)
            {
                for (int i = 0; i < ntf.m_oListProp.Count; i++)
                {
                    if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_ATTACKED_SPEED_RATIO)
                    {
                        Util.Log($"中弹减速: {ntf.m_oListProp[i].m_nValue}");
                    }

                    if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_ATTACKED_SPEED_IGNORE_RATIO)
                    {
                        Util.Log($"中弹减速抵抗: {ntf.m_oListProp[i].m_nValue}");
                    }

                    PlayerActorInfo.Instance.SetProValueById(ntf.m_oListProp[i].m_wPropID, ntf.m_oListProp[i].m_nValue);
                    if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_HP_MAX)
                    {
                        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
                    }
                    else if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_STA_MAX)
                    {
                        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE);
                    }
                    else if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_WATER_MAX)
                    {
                        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE);
                    }
                    else if (NoticeUpdateSpeed(ntf.m_oListProp[i].m_wPropID))
                    {
                        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_MOVE_SPEED_CHANGE);
                    }
                    else if (ntf.m_oListProp[i].m_wPropID == (ushort)GameServerProto_DEF.PT_BT_PROP_LOADCAP)
                    {
                        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PROP_LOADCAP_CHANGE_NTF);
                    }
                }
                MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PROP_CHANGE_NTF);
            }
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_VEHICLE)
        {
            BattleMgr.I().GetManager<MountManager>().SetPropChange(ntf);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_MONSTER)
        {
            var monster = BattleMgr.I().GetManager<AIMonsterManager>().GetAIMonster(ntf.m_dwUnitID);

            monster?.AIFeatureBuffPassive?.SetPropChange(ntf);
        }
    }

    public bool NoticeUpdateSpeed(int propId)
    {
        if (propId == (ushort)GameServerProto_DEF.PT_BT_PROP_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_TOOL_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_MELEE_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_PISTOL_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_TOMMY_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_SHOTGUN_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_RIFLE_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_MACGUN_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_WEAPON_BAZOOKA_MOVE_SPEED ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_ATTACKED_SPEED_RATIO ||
            propId == (ushort)GameServerProto_DEF.PT_BT_PROP_ATTACKED_SPEED_IGNORE_RATIO)

        {
            return true;
        }

        return false;
    }

    public void sendActorProReq()
    {
        BTPKG_PROP_REQ req = new BTPKG_PROP_REQ();
        sendMsg(GameServerProto_MSG.BTID_PROP_REQ, req);
    }

    public void _recvActorProAck(GMDT_BASE data)
    {
        BTPKG_PROP_ACK ack = data as BTPKG_PROP_ACK;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PROP_ACK, ack);
    }

    public void _recvActorProNtf(GMDT_BASE data)
    {
        BTPKG_PROP_MANU_CHANGE_NTF ntf = data as BTPKG_PROP_MANU_CHANGE_NTF;
        var props = BagDataOp.I().ManuProps;
        foreach (var prop in ntf.m_oListDelProp)
        {
            var pro = props.Find(m => m.m_dwItemID == prop.m_dwItemID && m.m_wAttrID == prop.m_wAttrID);
            if (pro != null)
            {
                pro.m_nValue -= prop.m_nValue;
            }
        }
        foreach (var prop in ntf.m_oListAddProp)
        {
            var pro = props.Find(m => m.m_dwItemID == prop.m_dwItemID && m.m_wAttrID == prop.m_wAttrID);
            if (pro != null)
            {
                pro.m_nValue += prop.m_nValue;
            }
            else
            {
                pro = new BTDT_MANUPROP();
                pro.m_dwItemID = prop.m_dwItemID;
                pro.m_wAttrID = prop.m_wAttrID;
                pro.m_nValue = prop.m_nValue;
                props.Add(pro);
            }
        }

    }
    public void _recvMonsterThreatNtf(GMDT_BASE data)
    {
        BTPKG_MONSTER_THREAT_NTF ntf = data as BTPKG_MONSTER_THREAT_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_MONSTER_THREAT, ntf);
    }

    public void sendActorWeaponReloadReq(uint itemId)
    {
        BTPKG_ACTOR_WEAPON_RELOAD_REQ req = new BTPKG_ACTOR_WEAPON_RELOAD_REQ();
        req.m_dwItemID = itemId;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_WEAPON_RELOAD_REQ, req);
    }

    public void _recvActorWeaponReloadAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_WEAPON_RELOAD_ACK ack = data as BTPKG_ACTOR_WEAPON_RELOAD_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            BagDataOp.I().UpdateShortcutAmmoIdAndCountBySolt((int)BagDataOp.I().getCurSelectSlotId(), ack.m_dwAmmoID, ack.m_nBulletCount);
            Player.I().weapon().Reload();
            Player.I().followWeapon().Reload();
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PLAYER_RELOAD_UPDATE_AMMO);
        }
        CommonFunctions.GetUIWindowFight().EndReload();
        CommonFunctions.GetUIWindowFight().SetSendReload(false);
    }

    public void _recvActorWeaponReloadNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_WEAPON_RELOAD_NTF ntf = data as BTPKG_ACTOR_WEAPON_RELOAD_NTF;
        if (Player.I().uuId == ntf.m_dwUnitID)
        {

        }
        else
        {
            BattleMgr.I().GetManager<PlayerManager>().ActorReloadNtf(ntf);
        }
    }

    public void _recvSelWeaponNtf(GMDT_BASE data)
    {
        BTPKG_SEL_WEAPON_NTF ntf = data as BTPKG_SEL_WEAPON_NTF;
        BattleMgr.I().GetManager<PlayerManager>().ActorSelWeaponNtf(ntf);
    }

    public void _recvUnitHpChangeNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_HP_CHANGE_NTF ntf = data as BTPKG_UNIT_HP_CHANGE_NTF;
        Player player = Player.I();
        if (player != null && ntf.m_dwUnitID == player.uuId)
        {
            if (PlayerActorInfo.Instance.PlayerActor.m_nHP > ntf.m_nHP)
            {
                var Standby2 = player.StateFind(StateDefine.STATE_IDLE2);
                var Standby3 = player.StateFind(StateDefine.STATE_IDLE3);
                if (Standby3 != null)
                {
                    Standby3.PopSelf();
                }
                if (Standby2 != null)
                {
                    Standby2.PopSelf();
                }

                //打断传送
                if (player.IsTransitCountDown())
                {
                    Hashtable tbl = new Hashtable();
                    tbl["type"] = 3;
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_TRANSFER_BREAK, tbl);
                }
            }
            PlayerActorInfo.Instance.PlayerActor.m_nHP = ntf.m_nHP;
            PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_HP_MAX, ntf.m_nHPMax);
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HP_CHANGE);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_VEHICLE)
        {
            var mount = BattleMgr.I().GetManager<MountManager>().GetMountByUnitId(ntf.m_dwUnitID);
            if (mount != null)
            {
                mount.m_stUnit.m_stVehicle.m_nHP = ntf.m_nHP;
                mount.m_stUnit.m_stVehicle.m_nMaxHP = ntf.m_nHPMax;
            }

            Player.I().UpdateVehicleMountHp(ntf.m_dwUnitID, ntf.m_nHP);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_MONSTER)
        {
            var monster = BattleMgr.I().GetManager<AIMonsterManager>().GetAIMonster(ntf.m_dwUnitID);
            monster.CurHp = ntf.m_nHP;
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_OBSTACLE)
        {
            BattleMgr.I().GetManager<AIMonsterManager>().ObstacleHpChangeNTF(ntf);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_FACILITY)
        {
            FacilityObject facility = BattleMgr.I().FacilityManager.m_FacilityObjs[ntf.m_dwUnitID];
            facility.SetMaxHp(ntf.m_nHPMax);
            facility.SetHp(ntf.m_nHP);
        }
        else if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_TRANSFORMER)
        {
            SurvivalPlayerCharacter transformer = BattleMgr.I().GetManager<TransformerManager>().GetTransformer(ntf.m_dwUnitID);
            if (transformer != null)
            {
                transformer.ChangeHp(ntf.m_nHP, ntf.m_nHPMax);
            }
            if (player != null)
            {
                if (ntf.m_dwUnitID == player.PawnUnitId)
                {
                    PlayerActorInfo.Instance.PlayerActor.m_stTransformerBag.m_stTransformer.m_nHP = ntf.m_nHP;
                    PlayerActorInfo.Instance.PlayerActor.m_stTransformerBag.m_stTransformer.m_nHPMax = ntf.m_nHPMax;
                    MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_CONTROL_HP_CHANGE);
                }
            }
        }
    }

    public void _recvStaChangeNtf(GMDT_BASE data)
    {
        BTPKG_STA_CHANGE_NTF ntf = data as BTPKG_STA_CHANGE_NTF;
        Player player = Player.I();
        if (player != null)
        {
            PlayerActorInfo.Instance.PlayerActor.m_nSTA = ntf.m_nSTA;
            PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_STA_MAX, ntf.m_nSTAMax);
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_CHANGE, ntf);
    }

    public void _recvThirstChangeNtf(GMDT_BASE data)
    {
        BTPKG_WATER_CHANGE_NTF ntf = data as BTPKG_WATER_CHANGE_NTF;
        Player player = Player.I();
        if (player != null)
        {
            PlayerActorInfo.Instance.PlayerActor.m_nWater = ntf.m_nWater;
            PlayerActorInfo.Instance.SetProValueById((ushort)GameServerProto_DEF.PT_BT_PROP_WATER_MAX, ntf.m_nWaterMax);
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_THIRST_CHANGE, ntf);
    }

    public void _recvSurvivalSkillNtf(GMDT_BASE data)
    {
        BTPKG_SURVIVALSKILL_UPDATE_NTF ntf = data as BTPKG_SURVIVALSKILL_UPDATE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_HUNGER_AND_THIRST_SKILL_CHANGE, ntf);
    }

    public void sendSupplyAttackedReq(uint unitId, uint index)
    {
        BTPKG_SUPPLY_ATTACKED_REQ req = new BTPKG_SUPPLY_ATTACKED_REQ();
        req.m_dwUnitID = unitId;
        req.m_wIndex = (ushort)index;
        sendMsg(GameServerProto_MSG.BTID_SUPPLY_ATTACKED_REQ, req);
    }

    public void _recvSupplyAttackedAck(GMDT_BASE data)
    {
        BTPKG_SUPPLY_ATTACKED_ACK ack = data as BTPKG_SUPPLY_ATTACKED_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            BattleMgr.I().GetManager<SupplyManager>().Attacked(ack.m_dwUnitID, ack.m_wIndex, ack.m_nHP, ack.m_nHPChange, true);
        }
    }

    public void _recvSupplyAttackedNtf(GMDT_BASE data)
    {
        BTPKG_SUPPLY_ATTACKED_NTF ntf = data as BTPKG_SUPPLY_ATTACKED_NTF;
        if (Player.I() == null)
            return;
        BattleMgr.I().GetManager<SupplyManager>().Attacked(ntf.m_dwUnitID, ntf.m_wIndex, ntf.m_nHP, ntf.m_nHPChange, CommonFunctions.IsPlayer(ntf.m_dwAttackerUnitID));
    }

    public void _recvSupplyResetNtf(GMDT_BASE data)
    {
        BTPKG_SUPPLY_RESET_NTF ntf = data as BTPKG_SUPPLY_RESET_NTF;
        BattleMgr.I().GetManager<SupplyManager>().ResetSupply(ntf.m_dwUnitID, ntf.m_oListIndexes);
    }

    public void sendDoorOpen(uint unitId, uint doorId, uint open, int password = -1, byte direction = 0)
    {
        BTPKG_DOOR_OPEN_REQ req = new BTPKG_DOOR_OPEN_REQ();
        req.m_dwUnitID = unitId;
        req.m_dwDoorID = doorId;
        req.m_byOpen = (System.Byte)open;
        req.m_nPassword = password;
        req.m_byIsOpenInFront = direction;
        sendMsg(GameServerProto_MSG.BTID_DOOR_OPEN_REQ, req);
    }

    public void _recvDoorOpenAck(GMDT_BASE data)
    {
        BTPKG_DOOR_OPEN_ACK ack = data as BTPKG_DOOR_OPEN_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            // 打开或者关闭门
            bool isOpened = ack.m_byOpen == 1;
            if (ack.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_FACILITY)
            {
                BattleMgr.I().GetManager<FacilityManager>().DoorOpenAck(ack);
            }
            else
            {
                BattleMgr.I().m_doorMgr.ChangeDoorState(ack.m_wUnitType, ack.m_dwUnitID, ack.m_dwDoorID, isOpened, ack.m_byIsCutOff, ack.m_byIsOpenInFront);
            }
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PASSWORD_IS_WRONG || ack.m_nErrCode == GameServerProto_DEF.PTERR_PASSWORD_IS_NEED)
        {
            if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PASSWORD_IS_NEED)
            {
                ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423408));//输入密码
            }
            else
            {
                ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423409));//密码错误
            }
            BattleMgr.I().m_doorMgr.PwdLockInputError(ack.m_dwUnitID, ack.m_dwDoorID);
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_PWDLOCK_IS_FORBID_CD)
        {
            long leftTime = ack.m_dwLockForbidSec - ServerTimeService.getInstance().getCurrentServerTimeStamp();
            string showTime;
            if (leftTime > 60 * 60)
            {
                showTime = CommonFunctions.FormatLeftTime((uint)leftTime);
            }
            else
            {
                showTime = CommonFunctions.FormatTimeMS(leftTime > 0 ? (uint)leftTime : 0);
            }
            ComMsg.getInstance().showMsg(Lang.Format(TblDataUtils.GetStringByID(StringEnum.String350641), showTime));
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_NOT_FIND_BTCARD)
        {
            CTblItem.CItem item = CTblAll.g_oTblItem.Get(BattleMgr.I().m_doorMgr.m_CardID);
            if (item == null)
            {
                Debug.LogError("门不存在");
                return;
            }
            string error = String.Format(TblDataUtils.GetStringByID(CTblAll.g_oTblErrString.Get(ack.m_nErrCode).m_dwString), TblDataUtils.GetStringByID(item.m_dwName));
            ComMsg.getInstance().showMsg(error);
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_NOT_IN_FRONT)
        {

            CTblItem.CItem item = CTblAll.g_oTblItem.Get(BattleMgr.I().m_doorMgr.m_CardID);

            string error = String.Format(TblDataUtils.GetStringByID(CTblAll.g_oTblErrString.Get(ack.m_nErrCode).m_dwString), TblDataUtils.GetStringByID(item.m_dwName));
            ComMsg.getInstance().showMsg(error);
        }
        else
        {
            CTblErrString.CItem tblStringItem = CTblAll.g_oTblErrString.Get(ack.m_nErrCode);
            if (tblStringItem == null)
            {
                Util.LogError("ErrString中没有配置 m_nErrCode: {0}", ack.m_nErrCode);
                return;
            }
            string error = TblDataUtils.GetStringByID(tblStringItem.m_dwString);
            ComMsg.getInstance().showMsg(error);
        }

        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_DOOR_CHECK_PASSWD_ACK, ack);
    }

    public void _recvDoorOpenNtf(GMDT_BASE data)
    {
        BTPKG_DOOR_OPEN_NTF ntf = data as BTPKG_DOOR_OPEN_NTF;
        bool isOpened = ntf.m_byOpen == 1;
        if (ntf.m_wUnitType == GameServerProto_DEF.PT_BT_UNITTYPE_FACILITY)
        {
            BattleMgr.I().GetManager<FacilityManager>().DoorOpenNtf(ntf);
        }
        else
        {
            BattleMgr.I().m_doorMgr.ChangeDoorState(ntf.m_wUnitType, ntf.m_dwUnitID, ntf.m_dwDoorID, isOpened, ntf.m_byIsCutOff, ntf.m_byIsOpenInFront);
        }
    }

    public void _recvDoorVisibalNtf(GMDT_BASE data)
    {
        BTPKG_DOOR_VISIBLE_NTF ntf = data as BTPKG_DOOR_VISIBLE_NTF;
        BattleMgr.I().m_doorMgr.DoorVisible(ntf.m_wUnitType, ntf.m_dwUnitID, ntf.m_dwDoorID, ntf.m_byIsVisible);
    }

    public void _recvDoorInteractionNtf(GMDT_BASE data)
    {
        BTPKG_DOOR_INTERACTION_NTF ntf = data as BTPKG_DOOR_INTERACTION_NTF;
        BattleMgr.I().m_doorMgr.DoorInteractive(ntf.m_dwUnitID, ntf.m_dwDoorID, ntf.m_byIsInteraction == 1);
    }

    public void _recvDoorPwdErrorNtf(GMDT_BASE data)
    {
        BTPKG_PWDLOCK_DOOR_ERROR_NTF ntf = data as BTPKG_PWDLOCK_DOOR_ERROR_NTF;
        BattleMgr.I().m_doorMgr.PwdLockErrorNtf(ntf.m_dwUnitID, ntf.m_dwDoorID);
    }

    public void SendGMCommandReq(string cmdStr)
    {
        GMPKG_GMCMD_REQ req = new GMPKG_GMCMD_REQ();
        List<GMDT_GMCMD_PARAM> m_oListParams = new List<GMDT_GMCMD_PARAM>();
        string[] cmdStrs = cmdStr.Split(' ');
        for (int i = 0; i < cmdStrs.Length; i++)
        {
            GMDT_GMCMD_PARAM cmd = new GMDT_GMCMD_PARAM();
            cmd.m_strParam = cmdStrs[i];
            m_oListParams.Add(cmd);
        }
        req.m_oListParams = m_oListParams;
        sendMsg(GameServerProto_MSG.GMID_GMCMD_REQ, req);
    }

    public void SendCollectionCollectReq(uint collectionId)
    {
        BattleMgr.I().GetManager<CollectionResManager>().UpdateNetworkState(collectionId, NetworkState.Sending);
        BTPKG_COLLECTION_COLLECT_REQ req = new BTPKG_COLLECTION_COLLECT_REQ();
        req.m_dwCollectionID = collectionId;
        sendMsg(GameServerProto_MSG.BTID_COLLECTION_COLLECT_REQ, req);
    }

    public void _recvCollectionCollectAck(GMDT_BASE data)
    {
        BTPKG_COLLECTION_COLLECT_ACK ack = data as BTPKG_COLLECTION_COLLECT_ACK;
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            BattleMgr.I().GetManager<CollectionResManager>().CollectAck(ack.m_dwCollectionID, ack.m_nRemainTimes);
        }
        else
        {
            ComMsg.getInstance().showErrString(ack.m_nErrCode);
        }
    }

    public void _recvCollectionCollectNtf(GMDT_BASE data)
    {
        BTPKG_COLLECTION_COLLECT_NTF ntf = data as BTPKG_COLLECTION_COLLECT_NTF;
        BattleMgr.I().GetManager<CollectionResManager>().CollectAck(ntf.m_dwCollectionID, ntf.m_nRemainTimes);
    }

    public void _recvCollectionResetNtf(GMDT_BASE data)
    {
        BTPKG_COLLECTION_RESET_NTF ntf = data as BTPKG_COLLECTION_RESET_NTF;
        BattleMgr.I().GetManager<CollectionResManager>().ResetNtf(ntf.m_dwCollectionID);
    }

    public void sendFacilityPutReq(uint bagType, uint bagSlot, Vector3 pos, Vector3 euler, int attachType, uint buildingId, uint pieceId)
    {
        BTPKG_FACILITY_PUT_REQ req = new BTPKG_FACILITY_PUT_REQ();
        req.m_byBagType = (System.Byte)bagType;
        req.m_wBagSlot = (System.UInt16)bagSlot;
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(pos);
        req.m_stEuler = BattleUtils.V3_TO_BTDTV3(euler);
        req.m_byAttachType = (System.Byte)attachType;
        req.m_dwAttachID = buildingId;
        req.m_dwAttachSubID = pieceId;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_PUT_REQ, req);
    }

    public void _recvFaclitySkinChangeNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_SKIN_CHANGE_NTF ntf = data as BTPKG_FACILITY_SKIN_CHANGE_NTF;
        BattleMgr.I().FacilityManager.FaclitySkinChangeNtf(ntf);
    }
    
    public void _recvFacilityPutAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_PUT_ACK ack = data as BTPKG_FACILITY_PUT_ACK;
        BattleMgr.I().FacilityManager.ReleaseLock(ack.m_dwUnitID);
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_SHORTCUT_REFRESH_SELECT);
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_PUT_ACK);
            BattleMgr.I().FacilityManager.ClearPreview();
            if (!BattleMgr.I().IsInBuildingMode())
            {
                Player.PopBuildState();
            }
            Sound.I().PlaySound(SoundEnum.Sound497);
        }
        else if (ack.m_nErrCode == GameServerProto_DEF.PTERR_BUILDING_PROTECTED_CANNOT_PUT_LANDCAB)
        {
            int day = ServerTimeService.getInstance().GetCurrentDay();
            var startDayInfo = CTblAll.g_oTblDemolishHouse.Get((uint)day);
            var startInfo = CTblAll.g_oTblParam.Get(13605);
            var startTime = string.Format("{0:00}:{1:00}", startDayInfo?.m_dwStartTime / 100, startDayInfo?.m_dwStartTime % 100);
            var endInfo = CTblAll.g_oTblParam.Get(13606);
            var endTime = string.Format("{0:00}:{1:00}", startDayInfo?.m_dwEndTime / 100, startDayInfo?.m_dwEndTime % 100);
            ComMsg.getInstance().showMsg(StringUtils.ParseLOCALTag(string.Format(TblDataUtils.GetStringByID(StringEnum.String1423442), startTime, endTime), LoginData.I().GroupTimeZone));
        }
        else
        {
            ComMsg.getInstance().showErrString(ack.m_nErrCode);
        }
    }

    public void _recvFacilityPutNtf(GMDT_BASE data)
    {
        // 这里保存的attach是占据的槽位
        BTPKG_FACILITY_PUT_NTF ntf = data as BTPKG_FACILITY_PUT_NTF;
        BattleMgr.I().FacilityManager.PutNtf(ntf);
    }

    public void sendFacilityDelReq(uint unitId)
    {
        BTPKG_FACILITY_DEL_REQ req = new BTPKG_FACILITY_DEL_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_DEL_REQ, req);
    }

    public void _recvFacilityDelAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_DEL_ACK ack = data as BTPKG_FACILITY_DEL_ACK;
        //Do Nothing
        checkErrCode(ack.m_nErrCode);
    }

    public void _recvFacilityDelNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_DEL_NTF ntf = data as BTPKG_FACILITY_DEL_NTF;
        BattleMgr.I().FacilityManager.DelNtf(ntf);
    }

    public void sendFacilityMoveReq(uint unitId, Vector3 pos, Vector3 euler, int attachType, uint buildingId, uint pieceId)
    {
        BTPKG_FACILITY_MOVE_REQ req = new BTPKG_FACILITY_MOVE_REQ();
        req.m_dwUnitID = unitId;
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(pos);
        req.m_stEuler = BattleUtils.V3_TO_BTDTV3(euler);
        req.m_byAttachType = (System.Byte)attachType;
        req.m_dwAttachID = buildingId;
        req.m_dwAttachSubID = pieceId;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_MOVE_REQ, req);
    }

    public void _recvFacilityMoveAck(GMDT_BASE data)
    {
        //这里保存的attach是释放的槽位
        BTPKG_FACILITY_MOVE_ACK ack = data as BTPKG_FACILITY_MOVE_ACK;
        checkErrCode(ack.m_nErrCode);
        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            BattleMgr.I().FacilityManager.ClearPreview();
        }

        BattleMgr.I().FacilityManager.MoveAck(ack);
        //Do Nothing
        //会推送 BTPKG_FACILITY_DESTORY_NTF
    }

    public void _recvFacilityMoveNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_MOVE_NTF ntf = data as BTPKG_FACILITY_MOVE_NTF;
        // 这里保存的attach是占据的槽位
        BattleMgr.I().FacilityManager.MoveNtf(ntf);
    }

    public void _recvFacilityStateChangeNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_STATE_CHANGE_NTF ntf = data as BTPKG_FACILITY_STATE_CHANGE_NTF;
        BattleMgr.I().FacilityManager.ChangeStatus(ntf.m_dwUnitID, ntf.m_byState);
    }

    public void _recvFacilityLevelChangeNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_LEVEL_CHANGE_NTF ntf = data as BTPKG_FACILITY_LEVEL_CHANGE_NTF;
        BattleMgr.I().FacilityManager.ChangeLevel(ntf.m_dwUnitID, ntf.m_dwFacilityID, ntf.m_byLevel);
    }

    public void _recvFacilityDestroyNtf(GMDT_BASE data)
    {
        // 这里保存的attach是释放的槽位
        BTPKG_FACILITY_DESTORY_NTF ntf = data as BTPKG_FACILITY_DESTORY_NTF;
        BattleMgr.I().FacilityManager.DestroyNtf(ntf);
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_FACILITY_DESTORY_NTF, ntf);
    }

    public void _recvFacilityOwnerLandCabChangeNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_OWNER_LANDCAB_CHANGE_NTF ntf = data as BTPKG_FACILITY_OWNER_LANDCAB_CHANGE_NTF;
        BattleMgr.I().FacilityManager.OwnerLandCabChange(ntf.m_dwUnitID, ntf.m_oListOwnerActorUnitID);
    }

    public void _recvFacilityRustNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_RUST_NTF ntf = data as BTPKG_FACILITY_RUST_NTF;
        BattleMgr.I().FacilityManager.FacilityHpChange(ntf.m_dwUnitID, ntf.m_nHP);
    }

    public void sendFacilityRepairReq(uint unitId)
    {
        BTPKG_FACILITY_REPAIR_REQ req = new BTPKG_FACILITY_REPAIR_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_FACILITY_REPAIR_REQ, req);
    }

    public void _recvFacilityRepairAck(GMDT_BASE data)
    {
        BTPKG_FACILITY_REPAIR_ACK ack = data as BTPKG_FACILITY_REPAIR_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {

        }
    }

    public void _recvFacilityRepairNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_REPAIR_NTF ntf = data as BTPKG_FACILITY_REPAIR_NTF;
        Sound.I().PlaySound(SoundEnum.Sound495);
        BattleMgr.I().FacilityManager.FacilityHpChange(ntf.m_dwUnitID, ntf.m_nHP);
    }

    //public void _sendFacilityUpgradeReq(uint unitId)
    //{
    //    BTPKG_FACILITY_UPGRADE_REQ req = new BTPKG_FACILITY_UPGRADE_REQ();
    //    req.m_dwUnitID = unitId;
    //    sendMsg(GameServerProto_MSG.BTID_FACILITY_UPGRADE_REQ, req);
    //}

    //public void _recvFacilityUpgradeAck(GMDT_BASE data)
    //{
    //    BTPKG_FACILITY_UPGRADE_ACK ack = data as BTPKG_FACILITY_UPGRADE_ACK;
    //    checkErrCode(ack.m_nErrCode);
    //}

    //public void _recvFacilityUpgradeNtf(GMDT_BASE data)
    //{
    //    BTPKG_FACILITY_UPGRADE_NTF ntf = data as BTPKG_FACILITY_UPGRADE_NTF;
    //    BattleMgr.I().m_facilityManager.FacilityUpgrade(ntf.m_stInfo.m_dwUnitID, ntf.m_stInfo.m_byLevel);
    //}

    public void sendPwdLockAddReq(int password, uint unitId, uint doorId)
    {
        BTPKG_PWDLOCK_ADD_REQ req = new BTPKG_PWDLOCK_ADD_REQ();
        req.m_nPassword = password;
        req.m_dwUnitID = unitId;
        req.m_dwDoorID = doorId;
        sendMsg(GameServerProto_MSG.BTID_PWDLOCK_ADD_REQ, req);
    }

    public void sendPwdLockAlertReq(int password, uint unitId, uint doorId)
    {
        BTPKG_PWDLOCK_ALERT_REQ req = new BTPKG_PWDLOCK_ALERT_REQ();
        req.m_nPassword = password;
        req.m_dwUnitID = unitId;
        req.m_dwDoorID = doorId;
        sendMsg(GameServerProto_MSG.BTID_PWDLOCK_ALERT_REQ, req);
    }

    public void _recvPwdLockAddAck(GMDT_BASE data)
    {
        BTPKG_PWDLOCK_ADD_ACK ack = data as BTPKG_PWDLOCK_ADD_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
        BattleMgr.I().m_doorMgr.PwdLockAdd(ack.m_dwUnitID, ack.m_dwDoorID, ack.m_dwVer);
    }

    public void _recvPwdLockAddNtf(GMDT_BASE data)
    {
        BTPKG_PWDLOCK_ADD_NTF ntf = data as BTPKG_PWDLOCK_ADD_NTF;
        BattleMgr.I().m_doorMgr.PwdLockNtf(ntf.m_dwUnitID, ntf.m_dwDoorID, ntf.m_dwPwdlockOwnerID, ntf.m_dwVer);
    }

    public void _recvPwdLockAlertAck(GMDT_BASE data)
    {
        BTPKG_PWDLOCK_ALERT_ACK ack = data as BTPKG_PWDLOCK_ALERT_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;

        if (ack.m_nErrCode == GameServerProto_DEF.PTERR_SUCCESS)
        {
            ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423908));
        }
    }

    private void _recvPwdLockErrorNtf(GMDT_BASE data)
    {
        BTPKG_PWDLOCK_ERROR_NTF ntf = data as BTPKG_PWDLOCK_ERROR_NTF;
        BattleMgr.I().FacilityManager.PwdLockErrorNtf(ntf.m_dwUnitID);
    }

    //ActorUnitUpdate
    public void _recvActorUnitUpdateNTF(GMDT_BASE data)
    {
        BTPKG_ACTOR_UNIT_UPDATE_NTF ntf = data as BTPKG_ACTOR_UNIT_UPDATE_NTF;
        if (ntf.m_stActor.m_dwUnitID == Player.I().uuId)
        {
            PlayerActorInfo.Instance.PlayerActor.m_byLifeState = ntf.m_stActor.m_stActor.m_byLifeState;
            Player.I().killVal = ntf.m_stActor.m_stActor.m_nKillVal;
        }
        else
        {
            BattleMgr.I().GetManager<PlayerManager>().ActorUpdateUnitNtf(ntf);
        }
    }

    public void _recvActorOfflineNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_OFFLINE_NTF ntf = data as BTPKG_ACTOR_OFFLINE_NTF;
        BattleMgr.I().GetManager<PlayerManager>().ActorOfflineNtf(ntf);
    }

    public void _recvActorOnlineNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_OFFLINE_TO_ONLINE_NTF ntf = data as BTPKG_ACTOR_OFFLINE_TO_ONLINE_NTF;
        BattleMgr.I().GetManager<PlayerManager>().ActorOnlineNtf(ntf);
    }

    public void _recvActorOfflineLieDownNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_OFFLINE_LIEDOWN_NTF ntf = data as BTPKG_ACTOR_OFFLINE_LIEDOWN_NTF;
        if (ntf.m_dwUnitID == Player.I().uuId)
        {
            Player.I().Sleep();
        }
        else
        {
            BattleMgr.I().GetManager<PlayerManager>().ActorOfflineLieDownNtf(ntf);
        }

    }

    public void _recvWeatherUpdateNTF(GMDT_BASE data)
    {
        BTPKG_WEATHER_INFO_NTF ntf = data as BTPKG_WEATHER_INFO_NTF;
        EnviroSky.instance.updateWeatherDate(ntf.m_oListWeatherInfo);
    }

    public void _recvServerTimeSyncNTF(GMDT_BASE data)
    {
        GMPKG_SERVER_TIME_SYNC_NTF ntf = data as GMPKG_SERVER_TIME_SYNC_NTF;
        ServerTimeService.getInstance().updateServerTimeFromServer(ntf.m_stCurTimeEx.m_dwSec, ntf.m_stCurTimeEx.m_dwMilliSec);
    }

    public void _recvStatesChangeNtf(GMDT_BASE data)
    {
        BTPKG_STATES_CHANGE_NTF ntf = data as BTPKG_STATES_CHANGE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_STATES_CHANGE_NTF, ntf.m_oListStates);
    }

    public void _recvEnterSectorPollutedNtf(GMDT_BASE data)
    {
        BTPKG_ENTER_SECTOR_POLLUTED_NTF ntf = data as BTPKG_ENTER_SECTOR_POLLUTED_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ENTER_SECTOR_POLLUTED_NTF, ntf);
        PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Area, 1321254, ntf.m_byLevel);
    }

    public void _recvExitSectorPollutedNtf(GMDT_BASE data)
    {
        BTPKG_EXIT_SECTOR_POLLUTED_NTF ntf = data as BTPKG_EXIT_SECTOR_POLLUTED_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_EXIT_SECTOR_POLLUTED_NTF, ntf);
        PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Area, 1321251);
    }
    public void _recvCleanerStartNtf(GMDT_BASE data)
    {
        BTPKG_PURIFIER_FACILITY_START_NTF ntf = data as BTPKG_PURIFIER_FACILITY_START_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_CLEANERSTART_INFO_NTF, ntf);
    }
    public void _recvCleanerInfoNtf(GMDT_BASE data)
    {
        BTPKG_PURIFIER_FACILITY_INFO_NTF ntf = data as BTPKG_PURIFIER_FACILITY_INFO_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_CLEANER_NTF, ntf);
    }

    public void sendActorOfflinePosCorrectReq(uint unitId, Byte posture, Vector3 pos, Vector3 euler)
    {
        BTPKG_ACTOR_OFFLINE_POS_CORRECT_REQ req = new BTPKG_ACTOR_OFFLINE_POS_CORRECT_REQ();
        req.m_dwActorUnitID = unitId;
        req.m_byReason = 0;
        req.m_byPosture = posture;
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(BattleMgr.I().WorldMover.ConvertToRealPosition(pos));
        req.m_stEuler = BattleUtils.V3_TO_BTDTV3(euler);
        sendMsg(GameServerProto_MSG.BTID_ACTOR_OFFLINE_POS_CORRECT_REQ, req);
    }

    public void _recvActorOfflinePosCorrectAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_OFFLINE_POS_CORRECT_ACK ack = data as BTPKG_ACTOR_OFFLINE_POS_CORRECT_ACK;
        //if (!this.checkErrCode(ack.m_nErrCode))
        //{
        //    return;
        //}
    }

    void _recvActorAddExpNtf(GMDT_BASE data)
    {
        BTPKG_TALENT_LEVEL_UP_NTF ntf = data as BTPKG_TALENT_LEVEL_UP_NTF;
        ComMsg.getInstance().showGetExp(ntf);

        if (ntf.m_byTalentType == 1)
        {
            PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Talent, 1321275, ntf.m_wAddProficiency);
        }
        else if (ntf.m_byTalentType == 2)
        {
            PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Talent, 1321286, ntf.m_wAddProficiency);
        }
        else if (ntf.m_byTalentType == 3)
        {
            PlayerData.getInstance().m_oChatData.SystemNotice(SystemNoticeType.Talent, 1321276, ntf.m_wAddProficiency);
        }
        PlayerData.getInstance().m_oTalentData.RecvAddExpNtf(ntf);
    }

    void _recvActorTalentDailyExpNtf(GMDT_BASE data)
    {
        BTPKG_TALENT_VALUE_NTF ntf = data as BTPKG_TALENT_VALUE_NTF;
        PlayerData.getInstance().m_oTalentData.RecvActorTalentDailyExpNtf(ntf);
    }

    public void sendTalentLevelUp(byte type, ushort id)
    {
        BTPKG_TALENT_NODE_LEVEL_UP_REQ req = new BTPKG_TALENT_NODE_LEVEL_UP_REQ();
        req.m_byTalentType = type;
        req.m_wTalentID = id;
        sendMsg(GameServerProto_MSG.BTID_TALENT_NODE_LEVEL_UP_REQ, req);
    }

    void _recvTalentLevelUpAck(GMDT_BASE data)
    {
        BTPKG_TALENT_NODE_LEVEL_UP_ACK ack = data as BTPKG_TALENT_NODE_LEVEL_UP_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
        PlayerData.getInstance().m_oTalentData.RecvLevelUpAck(ack);
    }

    public void sendActorInDoorReq(byte type)
    {
        BTPKG_ACTOR_INDOOR_REQ req = new BTPKG_ACTOR_INDOOR_REQ();
        req.m_byisIndoor = type;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_INDOOR_REQ, req);
    }

    void _recvTemperatureNtf(GMDT_BASE data)
    {
        BTPKG_TEMPERATURE_UPDATE_INFO_NTF ntf = data as BTPKG_TEMPERATURE_UPDATE_INFO_NTF;
        BattleMgr.I().GetManager<TemperatureManager>().UpdateTemperature(ntf);
    }

    void _recvComfortNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_COMFORT_NTF ntf = data as BTPKG_ACTOR_COMFORT_NTF;
        PlayerData.getInstance().m_oComfortData.UpdateComfort(ntf);
    }

    public void sendGetComfortFacilityReq()
    {
        BTPKG_ACTOR_COMFORT_FACILITY_REQ req = new BTPKG_ACTOR_COMFORT_FACILITY_REQ();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_COMFORT_FACILITY_REQ, req);
    }

    void _recvGetComfortFacilityAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_COMFORT_FACILITY_ACK ack = data as BTPKG_ACTOR_COMFORT_FACILITY_ACK;
        PlayerData.getInstance().m_oComfortData.UpdateComfortFacility(ack.m_oListFacilityID);
    }

    void _recvNPCVisibleNtf(GMDT_BASE data)
    {
        BTPKG_NPC_VISIBLE_NTF ntf = data as BTPKG_NPC_VISIBLE_NTF;
        BattleMgr.I().m_npcManager.RecvNPCVisibleNtf(ntf);
    }

    /// <summary>
    /// 掉落到地面下后修正位置，防止服务器作弊警告
    /// </summary>
    /// <param name="worldPos"></param>
    public void sendActorPositionResetReq(Vector3 worldPos, Collider collider)
    {
        Util.Log("sendActorPositionResetReq");
        BTPKG_ACTOR_POSITION_RESET_REQ req = new BTPKG_ACTOR_POSITION_RESET_REQ();
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(worldPos);
        bool onBuilding = false;
        if (collider != null && collider.gameObject != null)
        {
            collider.gameObject.TryGetComponent<BuildingHitStub>(out BuildingHitStub buildingHitStub);
            collider.gameObject.TryGetComponent<FacilityObject>(out FacilityObject facilityObject);
            if ((buildingHitStub != null && buildingHitStub.m_Piece != null) || facilityObject != null)
            {
                onBuilding = true;
            }
        }
        req.m_byOnBuilding = onBuilding ? (byte)1 : (byte)0;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_POSITION_RESET_REQ, req);
    }

    void _recvActorPositionResetAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_POSITION_RESET_ACK ack = data as BTPKG_ACTOR_POSITION_RESET_ACK;
    }

    // 加载状态状态
    public void sendClientChaosStateRemoveReq(int posY)
    {
        Util.Log("sendClientChaosStateRemoveReq");
        BTPKG_CLIENT_CHAOS_STATE_REMOVE_REQ req = new BTPKG_CLIENT_CHAOS_STATE_REMOVE_REQ();
        req.m_nY = posY;
        sendMsg(GameServerProto_MSG.BTID_CLIENT_CHAOS_STATE_REMOVE_REQ, req);
    }

    void _recvClientChaosStateRemoveAck(GMDT_BASE data)
    {
        BTPKG_CLIENT_CHAOS_STATE_REMOVE_ACK ack = data as BTPKG_CLIENT_CHAOS_STATE_REMOVE_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
    }

    /// <summary>
    /// 加载状态变化
    /// </summary>
    /// <param name="data"></param>
    void _recvChaosStateChangeNtf(GMDT_BASE data)
    {
        BTPKG_CHAOS_STATE_CHANGE_NTF ntf = data as BTPKG_CHAOS_STATE_CHANGE_NTF;
        if (ntf.m_dwUnitID == Player.I().unitId)
        {
            PlayerActorInfo.Instance.PlayerActor.m_byChaosState = ntf.m_byChaosState;
            Player.I().ChaosEffect(ntf.m_byChaosState == 1);
        }
        else
        {
            PlayerManager playerManager = BattleMgr.I().GetManager<PlayerManager>();
            if (playerManager != null)
            {
                playerManager.ActorChaosStateChangeNtf(ntf);
            }
        }
    }

    /// <summary>
    /// 瞬移请求
    /// </summary>
    /// <param name="worldPos"></param>
    public void sendActorDoTransferReq(Vector3 worldPos)
    {
        Util.Log("sendActorDoTransferReq");
        BTPKG_DO_TRANSFER_REQ req = new BTPKG_DO_TRANSFER_REQ();
        req.m_stPos = BattleUtils.V3_TO_BTDTV3(worldPos);
        sendMsg(GameServerProto_MSG.BTID_DO_TRANSFER_REQ, req);
    }

    void _recvActorDoTransferAck(GMDT_BASE data)
    {
        BTPKG_DO_TRANSFER_ACK ack = data as BTPKG_DO_TRANSFER_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
    }

    // 瞬移
    void _recvUnitTransferNtf(GMDT_BASE data)
    {
        BTPKG_UNIT_TRANSFER_NTF ntf = data as BTPKG_UNIT_TRANSFER_NTF;
        if (ntf.m_byType == GameServerProto_DEF.PT_BT_UNITTYPE_ACTOR)
        {
            if (ntf.m_dwUnitID == Player.I().uuId)
            {
                Vector3 pos = BattleUtils.BTDTV3_TO_V3(ntf.m_stPos);

                Player.I().TransientPos(pos, ntf.m_byServerTransFlag, ntf.m_dwActorMoveSeq);
            }
            else
            {
                RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().GetMonsterByUintId(ntf.m_dwUnitID);
                if (remotePlayer == null)
                {
                    //因为视野内广播有问题，在视野里的人可能收不到，改成了全服广播，monster可能找不到
                    Util.Log("_recvUnitTransferNtf, not found player, unitid: " + ntf.m_dwUnitID);
                    return;
                }

                if (remotePlayer.HasState(StateDefine.STATE_RIDE))
                {
                    return;
                }

                MoveMonster move = (MoveMonster)remotePlayer.move;
                move._movePos = BattleMgr.I().WorldMover.ConvertToWorldPosition(BattleUtils.BTDTV3_TO_V3(ntf.m_stPos));
                move.platformID = 0;
                remotePlayer.trans.position = BattleMgr.I().WorldMover.ConvertToWorldPosition(BattleUtils.BTDTV3_TO_V3(ntf.m_stPos));
                Vector3 euler = BattleUtils.BTDTV3_TO_V3(ntf.m_stEuler);
                remotePlayer.trans.rotation = Quaternion.Euler(new Vector3(0, euler.y, 0));
                move.ResetPos();
                //传送的时候，BTPKG_UNIT_TRANSFER_NTF变了位置，但是后面又收到了BTPKG_ACTOR_MOVE_NTF，里面是老的位置，所以位置又变回来了，这里忽略几个
                BattleMgr.I().GetManager<PlayerManager>().IgnoreMoveNtf(ntf.m_dwUnitID, 2);
            }
        }
    }


    public void sendRecordInfoReq()
    {
        BTPKG_ACTOR_RECORD_INFO_REQ req = new BTPKG_ACTOR_RECORD_INFO_REQ();
        sendMsg(GameServerProto_MSG.BTID_ACTOR_RECORD_INFO_REQ, req);
    }

    void _recvRecordInfoAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_RECORD_INFO_ACK ack = data as BTPKG_ACTOR_RECORD_INFO_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_RECORDINFO_ACK, ack);
    }

    void _recvVehicleMountNumNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_VEHICLE_MOUNT_NUM_NTF;
        if (ntf.m_byType == 1)
        {
            Player.I().AddVehicleMountNumData(ntf);
        }
        else if (ntf.m_byType == 2)
        {
            Player.I().RemoveVehicleMountNumData(ntf);
        }
    }

    public void sendEvoStageReq(uint day)
    {
        var req = new BTPKG_EVOLUTION_STAGE_DISPLAYED_REQ();
        req.m_dwDisplayedDay = day;
        sendMsg(GameServerProto_MSG.BTID_EVOLUTION_STAGE_DISPLAYED_REQ, req);
    }

    void _recvEvoStageAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_EVOLUTION_STAGE_DISPLAYED_ACK;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_EVOLUTION_STAGE_ACK, ack.m_cFlag);
    }

    public void sendActorPvpFlagReq(byte flag)
    {
        BTPKG_ACTOR_SET_PVP_FLAG_REQ req = new BTPKG_ACTOR_SET_PVP_FLAG_REQ();
        req.m_byPvPFlag = flag;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_SET_PVP_FLAG_REQ, req);
    }

    void _recvActorPvpFlagAck(GMDT_BASE data)
    {
        BTPKG_ACTOR_SET_PVP_FLAG_ACK ack = data as BTPKG_ACTOR_SET_PVP_FLAG_ACK;
        if (!checkErrCode(ack.m_nErrCode))
            return;
    }

    void _recvActorPvpFlagUpdateNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_SET_PVP_FLAG_UPDATE_NTF ntf = data as BTPKG_ACTOR_SET_PVP_FLAG_UPDATE_NTF;
        if (ntf.m_dwActorUnitID == Player.I().uuId)
        {
            //Player.I().killLevel = ntf.m_byKillLevel;
            Player.I().killVal = ntf.m_nKillVal;
            if (PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag == 0 && ntf.m_byPvPFlag == 1)
            {
                if (PlayerActorInfo.Instance.PlayerActor.m_stKillValBag.m_dwSwitchEndTime - ServerTimeService.getInstance().getCurrentServerTimeStamp() < 0)
                {
                    PlayerActorInfo.Instance.PlayerActor.m_stKillValBag.m_dwSwitchEndTime = (uint)ServerTimeService.getInstance().getCurrentServerTimeStamp() + 300;
                }
            }

            Player.I().UpdateLastPVPState();
            PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag = ntf.m_byPvPFlag;
            ComMsg.getInstance().ShowPvpStateTip();
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PVP_FLAG_CHANGE, ntf.m_byPvPFlag);
        }
        else
        {
            RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwActorUnitID);
            if (remotePlayer == null)
            {
                Util.LogError("_recvActorPvpFlagUpdateNtf not find actor unitId： " + ntf.m_dwActorUnitID);
                return;
            }
            //monster.m_unit.m_stActor.m_byKillValLevel = ntf.m_byKillLevel;
            remotePlayer.m_unit.m_stActor.m_byPvPFlag = ntf.m_byPvPFlag;
            remotePlayer.UpdatePvpIcon();
            remotePlayer.UpdatePlayerInfo();
        }
    }

    void _recvActorPvpFlagRemindNtf(GMDT_BASE data)
    {
        BTPKG_ACTOR_SET_PVP_FLAG_REMIND_NTF ntf = data as BTPKG_ACTOR_SET_PVP_FLAG_REMIND_NTF;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1423115));
    }

    private Dictionary<uint, byte> warningDic = new Dictionary<uint, byte>();

    void _recvActorKillLevelChangeNtf(GMDT_BASE data)
    {
        BTPKG_KILLVAL_NTF ntf = data as BTPKG_KILLVAL_NTF;
        var killingData = CTblAll.g_oTblKillingPunishment.GetAllItems();
        if (ntf.m_dwActorUnitID == Player.I().uuId)
        {
            string changeKillLevel = "";
            if (ntf.m_nKillVal - Player.I().killVal > 0)
            {
                changeKillLevel = "+" + (ntf.m_nKillVal - Player.I().killVal);
                ComMsg.getInstance().showKillLvTips(TblDataUtils.GetStringByID(StringEnum.String1423101) + changeKillLevel, 2);

            }
            else if (ntf.m_nKillVal - Player.I().killVal < 0)
            {
                changeKillLevel = "-" + (Player.I().killVal - ntf.m_nKillVal);
                ComMsg.getInstance().showKillLvTips(TblDataUtils.GetStringByID(StringEnum.String1423101) + changeKillLevel, 2);
            }
            else
            {
                return;
            }
            if (PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag == 0 && ntf.m_byPvPFlag == 1)
            {
                if (PlayerActorInfo.Instance.PlayerActor.m_stKillValBag.m_dwSwitchEndTime - ServerTimeService.getInstance().getCurrentServerTimeStamp() < 0)
                {
                    PlayerActorInfo.Instance.PlayerActor.m_stKillValBag.m_dwSwitchEndTime = (uint)ServerTimeService.getInstance().getCurrentServerTimeStamp() + 300;
                }
            }
            Player.I().UpdateLastPVPState();
            PlayerActorInfo.Instance.PlayerActor.m_byPvPFlag = ntf.m_byPvPFlag;
            ComMsg.getInstance().ShowPvpStateTip();
            uint PassiveSkill = 0;
            foreach (var item in killingData)
            {
                if (Player.I().killVal < item.Value.m_nKillValueMin && item.Value.m_nKillValueMin <= ntf.m_nKillVal && item.Value.m_nKillValueMax >= ntf.m_nKillVal)
                {
                    PassiveSkill = item.Value.m_dwPassiveSkill;
                    var detailText = CTblAll.g_oTblExtraPassive.Get(PassiveSkill);
                    ComMsg.getInstance().showKillLvTips(Lang.Format(TblDataUtils.GetStringByID(StringEnum.String1423116), ntf.m_nKillVal, TblDataUtils.GetStringByID(detailText.m_dwDescribe)), 3);

                    break;
                }
            }
            Player.I().killVal = ntf.m_nKillVal;
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PVP_FLAG_CHANGE, ntf.m_byPvPFlag);

        }
        else
        {
            RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().getPlayer(ntf.m_dwActorUnitID);
            if (remotePlayer == null)
            {
                Util.LogError("_recvActorPvpFlagUpdateNtf not find actor unitId： " + ntf.m_dwActorUnitID);
                return;
            }
            byte HazardNotice = 0;
            foreach (var item in killingData)
            {
                if (remotePlayer.m_unit.m_stActor.m_nKillVal < item.Value.m_nKillValueMin && item.Value.m_nKillValueMin <= ntf.m_nKillVal && item.Value.m_nKillValueMax >= ntf.m_nKillVal)
                {
                    HazardNotice = item.Value.m_byHazardNotice;
                    GameObject.Destroy(remotePlayer.m_PvpIcon);
                    string path = "";
                    foreach (var item1 in killingData)
                    {
                        if (item1.Value.m_nKillValueMin <= ntf.m_nKillVal && item1.Value.m_nKillValueMax >= ntf.m_nKillVal)
                        {
                            path = item.Value.m_strIcon;
                        }
                    }
                    ResourceManager.I().LoadAssetAsync(path, (pvpReq) =>
                    {
                        remotePlayer.m_PvpIcon = CommonFunctions.InstantiateGameObject(pvpReq, remotePlayer.trans, Vector3.zero, Vector3.one * 0.03f);
                        remotePlayer.ChangePvpIcon();
                    });
                    break;
                }
            }
            remotePlayer.m_unit.m_stActor.m_nKillVal = ntf.m_nKillVal;
            remotePlayer.m_unit.m_stActor.m_byPvPFlag = ntf.m_byPvPFlag;
            if (HazardNotice == 1 && (!warningDic.TryGetValue(ntf.m_dwActorUnitID, out byte warning) || warning == 0))
            {
                ComMsg.getInstance().showKillLvTips(TblDataUtils.GetStringByID(StringEnum.String1423117), 1);
                warningDic[ntf.m_dwActorUnitID] = HazardNotice;
            }
            else
            {
                warningDic[ntf.m_dwActorUnitID] = HazardNotice;
            }
            remotePlayer.UpdatePvpIcon();
            remotePlayer.UpdatePlayerInfo();
        }
    }

    void _recvCurrencyChangeNtf(GMDT_BASE data)
    {
        GMPKG_CURRENCY_CHANGE_NTF ntf = data as GMPKG_CURRENCY_CHANGE_NTF;
        for (int i = 0; i < ntf.m_oListCurrency.Count; i++)
        {
            int count = 0;

            uint dwcurrencyId = ntf.m_oListCurrency[i].m_dwCurrencyID;
            var tblItem = CTblAll.g_oTblItem.Get(dwcurrencyId);
            if (tblItem == null) continue;
            if (tblItem.m_wType == GameServerProto_DEF.PT_ITEMTYPE_BATTLE_CURRENCY)
            {
                bool has = false;
                for (int j = 0; j < PlayerActorInfo.Instance.PlayerActor.m_stWallet.m_oListCurrency.Count; j++)
                {
                    if (ntf.m_oListCurrency[i].m_dwCurrencyID == PlayerActorInfo.Instance.PlayerActor.m_stWallet.m_oListCurrency[j].m_dwCurrencyID)
                    {
                        count = PlayerActorInfo.Instance.PlayerActor.m_stWallet.m_oListCurrency[j].m_nCount;
                        PlayerActorInfo.Instance.PlayerActor.m_stWallet.m_oListCurrency[j].m_nCount = ntf.m_oListCurrency[i].m_nCount;
                        has = true;
                    }
                }
                if (!has)
                {
                    PlayerActorInfo.Instance.PlayerActor.m_stWallet.m_oListCurrency.Add(ntf.m_oListCurrency[i]);
                }
            }
            else if (tblItem.m_wType == GameServerProto_DEF.PT_ITEMTYPE_LOBBY_CURRENCY)
            {
                count = PlayerData.getInstance().GetWalletCount(dwcurrencyId);
                PlayerData.getInstance().SetWalletCount(dwcurrencyId, ntf.m_oListCurrency[i].m_nCount);
            }
            else if (tblItem.m_wType == GameServerProto_DEF.PT_ITEMTYPE_LOBBY_VALUE)
            {
                count = PlayerData.getInstance().GetWalletCount(dwcurrencyId);
                PlayerData.getInstance().SetWalletCount(dwcurrencyId, ntf.m_oListCurrency[i].m_nCount);
            }

            if (ntf.m_byTip != 0)
            {
                GMDT_ITEM_COUNT item = new GMDT_ITEM_COUNT();
                item.m_dwItemID = ntf.m_oListCurrency[i].m_dwCurrencyID;
                item.m_nCount = ntf.m_oListCurrency[i].m_nCount - count;
                var quality = CTblAll.g_oTblItem.Get(ntf.m_oListCurrency[i].m_dwCurrencyID).m_byQuality;
                ComMsg.getInstance().showGetGoodsMsg(item, quality);
            }

            if (ntf.m_oListCurrency[i].m_dwCurrencyID == GameServerProto_DEF.PT_ITEMID_HONORPOINT)
            {
                ntf.m_oListCurrency[i].m_nCount = ntf.m_oListCurrency[i].m_nCount - count;
            }
        }
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_CURRENCY_CHANGE, ntf);
    }


    void _recvMineBombNtf(GMDT_BASE data)
    {
        BTPKG_FACILITY_MINE_BOMB_NTF ntf = data as BTPKG_FACILITY_MINE_BOMB_NTF;
        BattleMgr.I().FacilityManager.FacilityMineBombNtf(ntf);
    }


    public void SendWaponBoxInfoReq(uint unitId)
    {
        var req = new BTPKG_WEAPONCABINET_INFO_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_WEAPONCABINET_INFO_REQ, req);
    }

    void _recvWaponBoxInfoAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_WEAPONCABINET_INFO_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_WEAPONCABINET_UPDATE, data);
        }
    }

    void _recvWaponBoxIsRefreshNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_WEAPONCABINET_UPDATE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_WEAPONCABINET_UPDATE, ntf);
    }

    public void SendWeaponBoxAreaPickOneReq(uint unitId, byte slot)
    {
        var req = new BTPKG_WEAPONCABINET_PICK_REQ
        {
            m_dwUnitID = unitId,
            m_bySlot = slot
        };
        sendMsg(GameServerProto_MSG.BTID_WEAPONCABINET_PICK_REQ, req);
    }

    void _recvWeaponBoxPickOneAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_WEAPONCABINET_PICK_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            // BattleMgr.I().m_treasureMgr.PickAck(ack.m_dwID, ack.m_wSlot, (ushort)ack.m_nCount, ack.m_oListItem);
        }
    }

    void _recvDamageListRefreshNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_DMGSTATS_CHANGE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_DAMAGELIST_UPDATE, data);
    }

    public void SendDamageListClearReq()
    {
        var req = new BTPKG_DMGSTATS_CLEAR_REQ
        {
            m_byType = 0
        };
        sendMsg(GameServerProto_MSG.BTID_DMGSTATS_CLEAR_REQ, req);
    }

    void _recvDamageListClearAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_DMGSTATS_CLEAR_ACK;
    }

    //切换语音范围
    public void SendChangeVoiceRangeRpt(ushort range)
    {
        BTPKG_ACTOR_VOICE_STATE_RPT rpt = new BTPKG_ACTOR_VOICE_STATE_RPT();
        rpt.m_wVoiceState = range;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_VOICE_STATE_RPT, rpt);
    }

    void _recvVoiceStateChangeNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_ACTOR_VOICE_STATE_NTF;
        //切换成组队语音&&不是我的队友 隐藏语音icon
        RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().GetMonsterByUintId(ntf.m_dwUnitID);
        if (remotePlayer != null)
        {
            remotePlayer.SetVoiceState(ntf.m_wVoiceState);
        }
    }

    public void SendLoudSpeakerRpt(byte range)
    {
        var rpt = new BTPKG_ACTOR_LOUDSPEAKER_RPT();
        rpt.m_byLoudspeaker = range;
        sendMsg(GameServerProto_MSG.BTID_ACTOR_LOUDSPEAKER_RPT, rpt);
    }

    void _recvLoudSpeakerNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_ACTOR_LOUDSPEAKER_NTF;
        RemotePlayer remotePlayer = BattleMgr.I().GetManager<PlayerManager>().GetMonsterByUintId(ntf.m_dwUnitID);
        if (remotePlayer != null)
        {
            remotePlayer.SetLoudSpeakerState(ntf.m_byLoudspeaker);
        }

        var voiceEngine = VoiceCenter.I().GetEngine();
        voiceEngine.UpdateLoudSpeaker(ntf.m_qwRoleID, ntf.m_byLoudspeaker);
    }

    public void _recvUseItemCDNtf(GMDT_BASE data)
    {
        BTPKG_ITEM_USE_CD_NTF ntf = data as BTPKG_ITEM_USE_CD_NTF;
        if (Player.I() != null)
        {
            Player.I().UpdateItemCDTime(ntf.m_dwItemID, ntf.m_dwCD);
        }

    }

    //活动通知
    public void SendGetAnnoucementReq()
    {
        BTPKG_GET_ALL_ANNOUNCEMENT_REQ req = new BTPKG_GET_ALL_ANNOUNCEMENT_REQ();
        sendMsg(GameServerProto_MSG.BTID_GET_ALL_ANNOUNCEMENT_REQ, req);
    }

    void _recvGetAnnoucementAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_GET_ALL_ANNOUNCEMENT_ACK;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ANNOUCEMENT_NOTICEALL, data);
    }

    public void SendGetAnnoucementTipReq()
    {
        BTPKG_GET_ALL_ANNOUNCEMENTTIP_REQ req = new BTPKG_GET_ALL_ANNOUNCEMENTTIP_REQ();
        sendMsg(GameServerProto_MSG.BTID_GET_ALL_ANNOUNCEMENTTIP_REQ, req);
    }

    void _recvGetAnnoucementTipAck(GMDT_BASE data)
    {
        var ack = data as BTPKG_GET_ALL_ANNOUNCEMENTTIP_ACK;
        PlayerData.getInstance().m_oMapData.InitActivityIcons(ack.m_oListAnnouncementTip);
    }

    public void _recvAnnoucementChangeNtf(GMDT_BASE data)
    {
        BTPKG_ANNOUNCEMENT_CHANGE_NTF ntf = data as BTPKG_ANNOUNCEMENT_CHANGE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ANNOUCEMENT_NOTICE, data);
    }

    public void _recvAnnoucementTipChangeNtf(GMDT_BASE data)
    {
        BTPKG_ANNOUNCEMENTTIP_CHANGE_NTF ntf = data as BTPKG_ANNOUNCEMENTTIP_CHANGE_NTF;
        if (ntf.m_byStart == 1)
        {
            PlayerData.getInstance().m_oMapData.AddActivityIcon(ntf.m_stAnnouncementTip);
        }
        else
        {
            PlayerData.getInstance().m_oMapData.DelActivityIcon(ntf.m_stAnnouncementTip);
        }
    }

    public void ReqTransferDo(uint unitId)
    {
        BTPKG_TRANSFER_DO_REQ req = new BTPKG_TRANSFER_DO_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_TRANSFER_DO_REQ, req);
    }

    private void _recvTransferDoAck(GMDT_BASE data)
    {
        BTPKG_TRANSFER_DO_ACK ack = data as BTPKG_TRANSFER_DO_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }
    }

    private void _recvBattleStateNTF(GMDT_BASE data)
    {
        BTPKG_ACTOR_BATTLE_STATE_NTF ntf = data as BTPKG_ACTOR_BATTLE_STATE_NTF;
        Player.I().UpdateLastPVPState();
        PlayerData.getInstance().m_oBattleStateData.RecvBattleStateNTF(ntf);
        ComMsg.getInstance().ShowPvpStateTip();
    }

    private void _recvStateCoolingNTF(GMDT_BASE data)
    {
        BTPKG_ACTOR_STATE_COOLING_NTF ntf = data as BTPKG_ACTOR_STATE_COOLING_NTF;
        PlayerData.getInstance().m_oBattleStateData.RecvStateCoolingNTF(ntf);
    }

    private void _recvSafeStateNTF(GMDT_BASE data)
    {
        BTPKG_ACTOR_SAFE_STATE_NTF ntf = data as BTPKG_ACTOR_SAFE_STATE_NTF;
    }

    private void _recvSafeStateChangeNTF(GMDT_BASE data)
    {
        BTPKG_ACTOR_SAFE_STATE_CHANGE_NTF ntf = data as BTPKG_ACTOR_SAFE_STATE_CHANGE_NTF;

    }

    private void _recvPhysicalChangeNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_PHYSICAL_CHANGE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_PHYSICAL_CHANGE_NTF, ntf);
    }

    private void _recvShieldHPNtf(GMDT_BASE data)
    {
        BTPKG_SHIELD_HP_NTF ntf = data as BTPKG_SHIELD_HP_NTF;
        Util.Log($"_recvShieldHPNtf {ntf.m_dwShield}/{ntf.m_dwShieldMax}");
        UpdatePlayerShield(ntf.m_dwUnitID, ntf.m_dwShield, ntf.m_dwShieldMax);
    }

    private void _recvShieldStopNtf(GMDT_BASE data)
    {
        BTPKG_SHIELD_STOP_NTF ntf = data as BTPKG_SHIELD_STOP_NTF;
        Util.Log($"_recvShieldStopNtf {ntf.m_dwShield}/{ntf.m_dwShieldMax}");
        UpdatePlayerShield(ntf.m_dwUnitID, ntf.m_dwShield, ntf.m_dwShieldMax);
        PlayShieldAtkSound(ntf.m_dwUnitID);
    }

    private void _recvMedalBattleEndNtf(GMDT_BASE data)
    {
        BTPKG_SCENE_MEDAL_BATTLE_END_NTF ntf = data as BTPKG_SCENE_MEDAL_BATTLE_END_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_BOSSTOWER_COUNTDOWN_NTF, ntf);
    }

    public void ReqBosstowerTrans()
    {
        BTPKG_SCENE_MEDAL_TRANS_BOSSTOWER_REQ req = new BTPKG_SCENE_MEDAL_TRANS_BOSSTOWER_REQ();
        sendMsg(GameServerProto_MSG.BTID_SCENE_MEDAL_TRANS_BOSSTOWER_REQ, req);
    }

    private void _recvBosstowerTransAck(GMDT_BASE data)
    {
        BTPKG_SCENE_MEDAL_TRANS_BOSSTOWER_ACK ntf = data as BTPKG_SCENE_MEDAL_TRANS_BOSSTOWER_ACK;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_BOSSTOWER_TRANSFER_ACK);
    }

    public void ReqBosstowerTransCancel(byte time)
    {
        BTPKG_SCENE_MEDAL_TRANS_CANCEL_REQ req = new BTPKG_SCENE_MEDAL_TRANS_CANCEL_REQ();
        req.m_byCancelTime = time;
        sendMsg(GameServerProto_MSG.BTID_SCENE_MEDAL_TRANS_CANCEL_REQ, req);
    }

    private void _recvBosstowerTransCancelAck(GMDT_BASE data)
    {
        BTPKG_SCENE_MEDAL_TRANS_CANCEL_ACK ntf = data as BTPKG_SCENE_MEDAL_TRANS_CANCEL_ACK;
    }

    /// <summary>
    ///  更新角色护盾值
    /// </summary>
    /// <param name="unitId"></param>
    /// <param name="shield"></param>
    /// <param name="shieldMax"></param>
    private void UpdatePlayerShield(uint unitId, uint shield, uint shieldMax)
    {
        if (Player.I() != null && Player.I().uuId == unitId)
        {
            Player.I().UpdateEquipShieldValue(shield, shieldMax);
        }
        else
        {
            PlayerManager playerManager = BattleMgr.I().GetManager<PlayerManager>();
            RemotePlayer remotePlayer = playerManager.getPlayer(unitId);
            if (remotePlayer != null)
            {
                remotePlayer.UpdateEquipShieldValue(shield, shieldMax);
            }
        }
    }

    private void PlayShieldAtkSound(uint unitId)
    {
        if (Player.I() != null && Player.I().uuId == unitId)
        {
            Sound.I().PlaySound(SoundEnum.Sound515, Player.I().transform);
        }
        else
        {
            PlayerManager playerManager = BattleMgr.I().GetManager<PlayerManager>();
            RemotePlayer remotePlayer = playerManager.getPlayer(unitId);
            if (remotePlayer != null)
            {
                Sound.I().PlaySound(SoundEnum.Sound515, remotePlayer.transform);
            }
        }
    }

    //拆家保护
    public void _recvBuildingProtectStartNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_BUILDING_PROTECT_START_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_BUILDING_PROTECT_START, ntf);
    }

    public void _recvBuildingProtectNtf(GMDT_BASE data)
    {
        var ntf = data as BTPKG_BUILDING_PROTECT_STATUS_NTF;
        BattleMgr.I().m_BuildingProtect = ntf.m_byStatus != 2;
        if (ntf.m_byStatus != 5)//等于5关闭但不提示。处理登陆时同步玩家拆家状态。
        {
            ComMsg.getInstance().ShowHouseProtectTip(ntf.m_byStatus);
        }
    }
    //玩法介绍
    private void _recvIntroductionNtf(GMDT_BASE data)
    {
        GMPKG_INSTRUCTION_UPDATE_NTF ntf = data as GMPKG_INSTRUCTION_UPDATE_NTF;

        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_INSTRUCTION_UPDATE, ntf);
    }

    private bool VerifiFireIndex(UINT32 fireIndex)
    {
        if (_qFireIndex.Contains(fireIndex))
            return false;
        _qFireIndex.Enqueue(fireIndex);
        if (_qFireIndex.Count >= 100)
        {
            _qFireIndex.Dequeue();
        }
        return true;
    }

    public void SendUDPProtocol(GameServerProto_MSG msgId, GMDT_BASE req)
    {
        if (null == _bcm) return;
        FGUdpProtocol udpProto = new FGUdpProtocol();
        udpProto.m_qwRoleId = PlayerData.getInstance().m_qwRoleID;
        udpProto.m_qwKey = FGUdpProtocolProc.GetInstance().GetKey();
        udpProto.m_oData = req;
        udpProto.m_nMsgID = msgId;

        NetUdpServer udp = _bcm.GetUdpServer();
        if (udp.isRun) udp.Send(udpProto);
    }

    public void _recvProtocolChangeNtf(GMDT_BASE data)
    {
        var ntf = data as GMPKG_CLT_SEND_PROTOCOL_CHANGE_NTF;
        BattleCommonManager bcm = BattleMgr.I().GetManager<BattleCommonManager>();
        if (bcm == null) return;
        bcm.RecvProtocolChange(ntf);
    }

    public void SendProtocolChangeReq(int type)
    {
        GMPKG_SVR_SEND_PROTOCOL_CHANGE_RPT req = new GMPKG_SVR_SEND_PROTOCOL_CHANGE_RPT();
        req.m_byType = (UINT8)type;
        sendMsg(GameServerProto_MSG.GMID_SVR_SEND_PROTOCOL_CHANGE_RPT, req);
    }

    public void _recvHomeAttackedNtf(GMDT_BASE data)
    {
        BTPKG_BURGLAR_ALARM_STATUS_NTF ntf = data as BTPKG_BURGLAR_ALARM_STATUS_NTF;
        PlayerData.getInstance().m_oHomeAttackedAlarmData.HomeAttackedNtf(ntf);
    }

    public void SendHomeAttackedInfoReq(uint unitId)
    {
        BTPKG_BURGLAR_ALARM_INFO_REQ req = new BTPKG_BURGLAR_ALARM_INFO_REQ();
        req.m_dwUnitID = unitId;
        sendMsg(GameServerProto_MSG.BTID_BURGLAR_ALARM_INFO_REQ, req);
    }

    public void _recvHomeAttackedInfo(GMDT_BASE ackData)
    {
        BTPKG_BURGLAR_ALARM_INFO_ACK ack = ackData as BTPKG_BURGLAR_ALARM_INFO_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            UIManage.I().Show<UIHommeAttackedAlarm>();
            PlayerData.getInstance().m_oHomeAttackedAlarmData.SetPushMsg(ack.m_bySendClt);
        }
    }

    public void SendHomeAttackedPuchSwitch(uint unitId, byte isOpen)
    {
        byte curIsOpen = PlayerData.getInstance().m_oHomeAttackedAlarmData.GetPushMsg();
        if (curIsOpen == isOpen) return;
        BTPKG_BURGLAR_ALARM_OPEN_SEND_SWITCH_REQ req = new BTPKG_BURGLAR_ALARM_OPEN_SEND_SWITCH_REQ();
        req.m_dwUnitID = unitId;
        req.m_bySendClt = isOpen;
        sendMsg(GameServerProto_MSG.BTID_BURGLAR_ALARM_OPEN_SEND_SWITCH_REQ, req);
    }

    public void _recvHomeAttackedPushSwitch(GMDT_BASE ackData)
    {
        BTPKG_BURGLAR_ALARM_OPEN_SEND_SWITCH_ACK ack = ackData as BTPKG_BURGLAR_ALARM_OPEN_SEND_SWITCH_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            byte curIsOpen = PlayerData.getInstance().m_oHomeAttackedAlarmData.GetPushMsg();
            if (curIsOpen == 0)
            {
                PlayerData.getInstance().m_oHomeAttackedAlarmData.SetPushMsg(1);
            }
            else
            {
                PlayerData.getInstance().m_oHomeAttackedAlarmData.SetPushMsg(0);
            }
        }
    }

    #region 辐射
    public void RadiationBagReq()
    {
        BTPKG_RADIATION_BAG_REQ req = new BTPKG_RADIATION_BAG_REQ();
        sendMsg(GameServerProto_MSG.BTID_RADIATION_BAG_REQ, req);
    }

    public void _RecvRadiationAck(GMDT_BASE ackData)
    {
        BTPKG_RADIATION_BAG_ACK ack = ackData as BTPKG_RADIATION_BAG_ACK;
        BattleMgr.I().RadiationManager.RecvRadiationValChange(ack.m_nVal);
    }

    public void _RecvRadiationValChangeNtf(GMDT_BASE ackData)
    {
        BTPKG_RADIATION_VAL_CHANGE_NTF ntf = ackData as BTPKG_RADIATION_VAL_CHANGE_NTF;
        BattleMgr.I().RadiationManager.RecvRadiationValChange(ntf.m_nNewVal);
    }
    #endregion

    /// <summary>
    /// 观看广告次数
    /// </summary>
    /// <param name="type"></param>
    /// <param name="id"></param>
    public void ReqAdTimes(byte type, int id)
    {
        GMPKG_STATISTICS_REPORT_REQ req = new GMPKG_STATISTICS_REPORT_REQ();
        req.m_byType = type;
        req.m_nParam = id;
        sendFGMessage(GameServerProto_MSG.GMID_STATISTICS_REPORT_REQ, req);
    }

    private void RecvAdTimes(GMDT_BASE data)
    {
        GMPKG_STATISTICS_REPORT_ACK ack = data as GMPKG_STATISTICS_REPORT_ACK;
        PlayerData.getInstance().m_stStatisticsBag.m_dwDailyAdViewCnt = (uint)ack.m_nResult;
    }

    private void RecvDayPassNtf(GMDT_BASE data)
    {
        GMPKG_DAY_PASS_NTF ntf = data as GMPKG_DAY_PASS_NTF;
        PlayerData.getInstance().m_stStatisticsBag.m_dwDailyAdViewCnt = 0;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_DAYPASS_NTF, ntf);
    }

    private void RecvKillAnno(GMDT_BASE data)
    {
        BTPKG_TEAM_KILL_BROADCAST_NTF ntf = data as BTPKG_TEAM_KILL_BROADCAST_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_KILLER_ANNOUNCEMENT, ntf);
    }

    private void RecvCardWearNtf(GMDT_BASE data)
    {
        BTPKG_DETECTOR_CARD_CHANGE_NTF ntf = data as BTPKG_DETECTOR_CARD_CHANGE_NTF;
        PlayerData.getInstance().m_oMapData.m_DetectorCardID = ntf.m_dwDetectorCardID;
        if (ntf.m_dwDetectorCardID != 0)//获得探测器就发送协议获取信息
        {
            SendMonsterInfo();
        }
        else
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_RADIATION_INFO_UPDATE);
        }
    }

    public void SendMonsterInfo()
    {
        BTPKG_DETECTOR_MONSTER_INFO_REQ req = new BTPKG_DETECTOR_MONSTER_INFO_REQ();
        sendFGMessage(GameServerProto_MSG.BTID_DETECTOR_MONSTER_INFO_REQ, req);
    }

    private void RecvMonsterInfoAck(GMDT_BASE data)
    {
        BTPKG_DETECTOR_MONSTER_INFO_ACK ack = data as BTPKG_DETECTOR_MONSTER_INFO_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            PlayerData.getInstance().m_oMapData.UpdateDetectorSectors(ack);
        }
    }

    private void RecvMonsterNoticeNtf(GMDT_BASE data)
    {
        BTPKG_DETECTOR_MONSTER_NOTICE_NTF ntf = data as BTPKG_DETECTOR_MONSTER_NOTICE_NTF;
        PlayerData.getInstance().m_oMapData.SectorMonsterRefresh(ntf);
    }
    
    private void RecvMonsterDeadNtf(GMDT_BASE data)
    {
        BTPKG_DETECTOR_MONSTER_DEAD_NTF ntf = data as BTPKG_DETECTOR_MONSTER_DEAD_NTF;
        var sectorInfo = CTblAll.g_oTblMapShow.Get(ntf.m_dwDeadSectorID);
        var name = TblDataUtils.GetStringByID(sectorInfo.m_dwName);
        ComMsg.getInstance().showMsg(string.Format(TblDataUtils.GetStringByID(StringEnum.String1429501), name), ComMsg.MsgStyle.RedWithWarningIcon);
        PlayerData.getInstance().m_oMapData.SectorMonsterDead(ntf);
    }

    private void RecvActivateSectorUpdateNtf(GMDT_BASE data)
    {
        BTPKG_DETECTOR_ACTIVATESECTOE_UPDATE_NTF ntf = data as BTPKG_DETECTOR_ACTIVATESECTOE_UPDATE_NTF;
        PlayerData.getInstance().m_oMapData.UpdateDetectorSectors(ntf);
    }

    private void RecvEvacuateUpdateNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_DATA_UPDATE_NTF ntf = data as BTPKG_EVACUATE_DATA_UPDATE_NTF;
        PlayerData.getInstance().m_oMapData.UpdateEvacuate(ntf);
    }

    private void RecvEvacuateCardChangeNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_CARD_CHANGE_NTF ntf = data as BTPKG_EVACUATE_CARD_CHANGE_NTF;
        PlayerData.getInstance().m_oMapData.UpdateEvacuateCardInfo(ntf);
    }

    private void RecvSectorEnterNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_SECTOR_ENTER_NTF ntf = data as BTPKG_EVACUATE_SECTOR_ENTER_NTF;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1429502));
        //ComMsg.getInstance().HideEvacuateTip();
        ComMsg.getInstance().ShowExtractCdTip(ntf);
        //MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_EXTRACT_CD, ntf);
    }

    private void RecvSectorLeaveNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_SECTOR_LEAVE_NTF ntf = data as BTPKG_EVACUATE_SECTOR_LEAVE_NTF;
        ComMsg.getInstance().showMsg(TblDataUtils.GetStringByID(StringEnum.String1429503));
        PlayerData.getInstance().m_oMapData.ShowEvacuateTip();
        //MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_EXTRACT_LEAVE, ntf);
    }

    private void RecvSectorBroadcastNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_SUCCESS_BROADCAST_NTF ntf = data as BTPKG_EVACUATE_SUCCESS_BROADCAST_NTF;
        ComMsg.getInstance().showMsg(string.Format(TblDataUtils.GetStringByID(StringEnum.String1429504), ntf.m_strRoleName), ComMsg.MsgStyle.Red);
    }

    private void RecvEvacuateResultPopupNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_RESULT_POPUP_NTF ntf = data as BTPKG_EVACUATE_RESULT_POPUP_NTF;
        UIManage.I().CloseAllExcludeGroupAndOtherView<UIWindowLoading>(WindowGrouping.Fight, 6, false);
        if (ntf.m_byResult == GameServerProto_DEF.PT_BT_EVACUTAE_RESULT_SUCCESS)//撤离成功
        {
            // 先让玩家进入撤离状态
            var player = Player.I();
            if (player != null && !player.IsEvacuation())
            {
                player.Evacuation();
            }

            // 延迟显示撤离结果界面，等动作播放完成后再显示
            // 根据撤离动作的实际时长来设置延迟时间
            float actionDuration = 3.0f; // 默认3秒，如果有准确时长可以从ActionAsset获取
            DelayAction.Exec(actionDuration, () =>
            {
                UIManage.I().Show<UIWndExtract>((p) =>
                {
                    p.UpdatePanel(ntf);
                });
            });
        }
        else
        {
            UIManage.I().Show<UIWndExtract>((p) =>
            {
                p.UpdatePanel(ntf);
            });
        }
    }

    private void RecvEvacuateEndCDNtf(GMDT_BASE data)
    {
        BTPKG_EVACUATE_END_COUNTDOWN_NTF ntf = data as BTPKG_EVACUATE_END_COUNTDOWN_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_EXTRACT_END, ntf);
    }

    #region 词条

    public void SendGetEnterBagReq(uint unitid)
    {
        BTPKG_ENTRY_BAG_GET_REQ req = new BTPKG_ENTRY_BAG_GET_REQ();
        req.m_dwUnitID = unitid;
        sendFGMessage(GameServerProto_MSG.BTID_ENTRY_BAG_GET_REQ, req);
    }

    private void _recvEnterBagAck(GMDT_BASE data)
    {
        BTPKG_ENTRY_BAG_GET_ACK ack = data as BTPKG_ENTRY_BAG_GET_ACK;
        if (checkErrCode(ack.m_nErrCode))
        {
            MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ENTRY_SHOW, ack);
        }
    }

    private void _RecvEnterChangeNTF(GMDT_BASE data)
    {
        BTPKG_ENTRY_BAG_CHANGE_NTF ntf = data as BTPKG_ENTRY_BAG_CHANGE_NTF;
        MessageCenter.Instance.PostMessage(MessageCenter.MsgType.E_MT_ENTRY_UPDATE, ntf);
    }
    #endregion

    #region 蓝图
    public void ReqMakeBlueprint(uint buildingId)
    {
        BTPKG_MAKE_BLUEPRINT_REQ req = new BTPKG_MAKE_BLUEPRINT_REQ();
        req.m_dwBuildingId = buildingId;
        sendMsg(GameServerProto_MSG.BTID_MAKE_BLUEPRINT_REQ, req);
        BlueprintBuilder.Instance.NoLandcabDestroyMsg = true;
    }

    private void RecvMakeBlueprintAck(GMDT_BASE data)
    {
        BlueprintBuilder.Instance.NoLandcabDestroyMsg = false;
        BTPKG_MAKE_BLUEPRINT_ACK ack = data as BTPKG_MAKE_BLUEPRINT_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }

        PlayerActorInfo.Instance.PlayerActor.m_byHasBluePrint = 1;
        var fight = CommonFunctions.GetUIWindowFight();
        if (fight != null)
        {
            fight.SetBlueprinitButtonShow(true);
        }
    }

    public void ReqGetBlueprintBuilding()
    {
        BTPKG_GET_BLUEPRINT_BUILDING_REQ req = new BTPKG_GET_BLUEPRINT_BUILDING_REQ();
        sendMsg(GameServerProto_MSG.BTID_GET_BLUEPRINT_BUILDING_REQ, req);
    }

    private void RecvGetBlueprintBuilding(GMDT_BASE data)
    {
        BTPKG_GET_BLUEPRINT_BUILDING_ACK ack = data as BTPKG_GET_BLUEPRINT_BUILDING_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }

        BlueprintBuilder.Instance.SetBuildingInfo(ack);
        BlueprintBuilder.Instance.SetBuildingState(true);
    }

    public void ReqBuildBlueprintBuilding(BTDT_VECTOR3 targetPos)
    {
        BTPKG_BUILD_BLUEPRINT_BUILDING_REQ req = new BTPKG_BUILD_BLUEPRINT_BUILDING_REQ();
        req.m_stTargetPos = targetPos;
        sendMsg(GameServerProto_MSG.BTID_BUILD_BLUEPRINT_BUILDING_REQ, req);
    }

    private void RecvBuildBlueprintBuilding(GMDT_BASE data)
    {
        BTPKG_BUILD_BLUEPRINT_BUILDING_ACK ack = data as BTPKG_BUILD_BLUEPRINT_BUILDING_ACK;
        if (!checkErrCode(ack.m_nErrCode))
        {
            return;
        }

        BlueprintBuilder.Instance.SetBuildingState(false);
        BlueprintBuilder.Instance.SetBuildingInfo(null);
        BlueprintBuilder.Instance.DestroyAllPiece();
        PlayerActorInfo.Instance.PlayerActor.m_byHasBluePrint = 0;
        var fight = CommonFunctions.GetUIWindowFight();
        if (fight != null)
        {
            fight.SetBlueprinitButtonShow(false);
        }
        if (BattleMgr.I().GetManager<RaycastManager>() != null)
        {
            BattleMgr.I().GetManager<RaycastManager>().EnableRaycast(true, RaycastManager.DisableType.BlueprintBuilding);
        }
    }
    #endregion

}
