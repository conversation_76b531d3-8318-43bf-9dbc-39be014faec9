Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker4.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18152] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3680707185 [EditorId] 3680707185 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [18152] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3680707185 [EditorId] 3680707185 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 2216.36 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56332
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002120 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 970.17 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.660 seconds
Domain Reload Profiling:
	ReloadAssembly (1660ms)
		BeginReloadAssembly (85ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1445ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (155ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (1186ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (970ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (150ms)
				ProcessInitializeOnLoadMethodAttributes (55ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.018153 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 999.14 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.12 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.662 seconds
Domain Reload Profiling:
	ReloadAssembly (4664ms)
		BeginReloadAssembly (126ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (4430ms)
			LoadAssemblies (132ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (570ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (239ms)
			SetupLoadedEditorAssemblies (3412ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (999ms)
				BeforeProcessingInitializeOnLoad (146ms)
				ProcessInitializeOnLoadAttributes (1754ms)
				ProcessInitializeOnLoadMethodAttributes (497ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 14.80 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 23.635900 ms (FindLiveObjects: 0.920200 ms CreateObjectMapping: 1.189400 ms MarkObjects: 20.866100 ms  DeleteObjects: 0.658900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 79.25 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 168 Unused Serialized files (Serialized files now loaded: 0)
Unloading 168 unused Assets / (2.0 MB). Loaded Objects now: 10746.
Memory consumption went from 295.2 MB to 293.1 MB.
Total: 79.621300 ms (FindLiveObjects: 2.310400 ms CreateObjectMapping: 2.574700 ms MarkObjects: 73.783700 ms  DeleteObjects: 0.951100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 293374.711701 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chat_icon.prefab
  artifactKey: Guid(26524487348b1a549828080bf7251a15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chat_icon.prefab using Guid(26524487348b1a549828080bf7251a15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61257bab583b3aa71d36755519cf3ac9') in 0.585899 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_diantimen03.prefab
  artifactKey: Guid(8dc3d10580786af47be8cd79a07d7cdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_diantimen03.prefab using Guid(8dc3d10580786af47be8cd79a07d7cdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'af7f8f9c5ab2b9adf9142f9df070afb4') in 0.043615 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen01.prefab
  artifactKey: Guid(cdf44cc72f821154fbf64c10cc1f107e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen01.prefab using Guid(cdf44cc72f821154fbf64c10cc1f107e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b5c6e0f7a4151abfada8bce7c13006ae') in 0.017998 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door.prefab
  artifactKey: Guid(23e88668587d4bc41bf04600de800110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door.prefab using Guid(23e88668587d4bc41bf04600de800110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bded51bcdb87b958386c2404ccbd5dde') in 0.033869 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi01_01.prefab
  artifactKey: Guid(36fa3b3f845c36a40be57b04fec9ed4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_door_shuzi01_01.prefab using Guid(36fa3b3f845c36a40be57b04fec9ed4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fc27c034c11240e4e658c75486cfe970') in 0.020285 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chuangtougui.prefab
  artifactKey: Guid(5f2024734158ed84689bd4e24dfed68d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_chuangtougui.prefab using Guid(5f2024734158ed84689bd4e24dfed68d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2bff644d8bf6efe09e7b880d07cf0e4c') in 0.016808 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door_L.prefab
  artifactKey: Guid(465476efa3afe59499fd8d74884c192d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_jiayouzhan02_door_L.prefab using Guid(465476efa3afe59499fd8d74884c192d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cc7cb6a0387cb081ed9968d1022c7932') in 0.028472 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dengshanbao01.prefab
  artifactKey: Guid(85a8a86c7be8524479475de498a47837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_prop_dengshanbao01.prefab using Guid(85a8a86c7be8524479475de498a47837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2a316790f25e424872da8fd5f1f9d96') in 0.022165 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen02.prefab
  artifactKey: Guid(5dda8cf61ef06324d97e8b15008ead49) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/prefab/object_prefab/prop_prefab/c_build_diantimen02.prefab using Guid(5dda8cf61ef06324d97e8b15008ead49) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f7f72893768c26e26d3ed4cc3e4c6c35') in 0.017538 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.650192 seconds.
  path: Assets/AssetBundle/Model/BattleManagerRoot.prefab
  artifactKey: Guid(565f05372d8290e4e95b2d7f9dd4b7c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/BattleManagerRoot.prefab using Guid(565f05372d8290e4e95b2d7f9dd4b7c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b5042992618e5db474158cd74be989c4') in 0.064966 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AssetBundle/Model/Sergeant@tps_01_skin_show_2.prefab
  artifactKey: Guid(7f39dec282e6f174c9c45d1694c61b0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/Sergeant@tps_01_skin_show_2.prefab using Guid(7f39dec282e6f174c9c45d1694c61b0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '359cc1f80541b862afb112d3bd1b4d84') in 0.102269 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/AssetBundle/Model/mountCameraRoot.prefab
  artifactKey: Guid(cdf035927543ea9408061c623ee9a205) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/mountCameraRoot.prefab using Guid(cdf035927543ea9408061c623ee9a205) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dedb1b42716e16a224c96994f430615d') in 0.029820 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/AssetBundle/Model/Fx_yudi.FBX
  artifactKey: Guid(28ba12e58c6ea21429e6277de24cf767) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/Fx_yudi.FBX using Guid(28ba12e58c6ea21429e6277de24cf767) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8cd73f8e388112d838af3dedf077493b') in 0.082961 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AssetBundle/Model/Sergeant@fps_01_skin_front_1.prefab
  artifactKey: Guid(b68223bbba9c89d48a88c1310804b7cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/Model/Sergeant@fps_01_skin_front_1.prefab using Guid(b68223bbba9c89d48a88c1310804b7cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '20f5db25a7060302418761584d25a307') in 0.073553 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017807 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 9.66 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
ForwardRenderer is missing RendererFeatures
This could be due to missing scripts or compile error.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object,UnityEngine.Object)
UnityEngine.Debug:LogError (object,UnityEngine.Object)
UnityEngine.Rendering.Universal.ScriptableRendererData:ValidateRendererFeatures () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:181)
UnityEngine.Rendering.Universal.ScriptableRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs:76)
UnityEngine.Rendering.Universal.UniversalRendererData:OnValidate () (at Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/UniversalRendererData.cs:269)

(Filename: Packages/com.unity.render-pipelines.universal@12.1.7/Runtime/ScriptableRendererData.cs Line: 181)

Mono: successfully reloaded assembly
- Completed reload, in  3.176 seconds
Domain Reload Profiling:
	ReloadAssembly (3176ms)
		BeginReloadAssembly (174ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (78ms)
		EndReloadAssembly (2899ms)
			LoadAssemblies (124ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (507ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (2029ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (10ms)
				BeforeProcessingInitializeOnLoad (103ms)
				ProcessInitializeOnLoadAttributes (1639ms)
				ProcessInitializeOnLoadMethodAttributes (263ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (27ms)
Platform modules already initialized, skipping
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 38.67 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 9745 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (2.0 MB). Loaded Objects now: 10820.
Memory consumption went from 0.56 GB to 0.56 GB.
Total: 38.830700 ms (FindLiveObjects: 1.151200 ms CreateObjectMapping: 1.070800 ms MarkObjects: 35.855000 ms  DeleteObjects: 0.752500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0