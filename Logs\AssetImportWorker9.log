Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.15f1 (e8e88683f834) revision 15263878'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 24441 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\Unity 2021.3.15f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker9
-projectPath
F:/Project/njcs2clt/trunk/client
-logFile
Logs/AssetImportWorker9.log
-srvPort
14447
Successfully changed project path to: F:/Project/njcs2clt/trunk/client
F:/Project/njcs2clt/trunk/client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20928] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1914506762 [EditorId] 1914506762 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [20928] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1914506762 [EditorId] 1914506762 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DKGB08D) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 1057.39 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.15f1 (e8e88683f834)
[Subsystems] Discovering subsystems at path F:/Unity 2021.3.15f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Project/njcs2clt/trunk/client/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 Ti (ID=0x2182)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.7216
Initialize mono
Mono path[0] = 'F:/Unity 2021.3.15f1/Editor/Data/Managed'
Mono path[1] = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/Unity 2021.3.15f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56788
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/Unity 2021.3.15f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002088 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 906.66 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.472 seconds
Domain Reload Profiling:
	ReloadAssembly (1472ms)
		BeginReloadAssembly (73ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1299ms)
			LoadAssemblies (71ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (111ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (30ms)
			SetupLoadedEditorAssemblies (1094ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (907ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (129ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.019420 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 918.33 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Mono: successfully reloaded assembly
- Completed reload, in  4.143 seconds
Domain Reload Profiling:
	ReloadAssembly (4144ms)
		BeginReloadAssembly (128ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (3899ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (549ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (205ms)
			SetupLoadedEditorAssemblies (2923ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (918ms)
				BeforeProcessingInitializeOnLoad (134ms)
				ProcessInitializeOnLoadAttributes (1560ms)
				ProcessInitializeOnLoadMethodAttributes (298ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Switching render pipeline 79b82a151cf05a845b4e808edb263fac -> 7fd21b3714887c14a8a8db2c8552932c
Refreshing native plugins compatible for Editor in 13.48 ms, found 36 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 191 unused Assets / (2.1 MB). Loaded Objects now: 10746.
Memory consumption went from 0.52 GB to 0.52 GB.
Total: 27.832700 ms (FindLiveObjects: 1.106700 ms CreateObjectMapping: 1.564400 ms MarkObjects: 24.581900 ms  DeleteObjects: 0.578300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:srp/default-pipeline: f72db1734188c7418a8abdc2582539c2 -> 978ba251c10fa548b5e408e8bd62f3ca
========================================================================
Received Import Request.
  Time since last request: 309387.248506 seconds.
  path: Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipRefine.prefab
  artifactKey: Guid(e3a3d81081c4752469d10f21de6bb48f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/Bag/Prefab/Chip/PanelChipRefine.prefab using Guid(e3a3d81081c4752469d10f21de6bb48f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '079c7b4d658e6656594a6591a71dc825') in 0.110497 seconds 
========================================================================
Received Import Request.
  Time since last request: 346.084119 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab
  artifactKey: Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_EquipLathe.prefab using Guid(348f6b5a595914b49a0eb2cd646f27b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee35eb248d46254fe85f21c14fe9947a') in 0.031660 seconds 
========================================================================
Received Import Request.
  Time since last request: 503.615111 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_MaterialSelect.prefab
  artifactKey: Guid(bb1b123c76650a44ab90f665021d9e62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/Lathe/Prefab/Panel_MaterialSelect.prefab using Guid(bb1b123c76650a44ab90f665021d9e62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '64bca376f49c9e5e0c2b0e47d778675a') in 0.009591 seconds 
========================================================================
Received Import Request.
  Time since last request: 331.874029 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/battle_lingdigui_matnotfull.png
  artifactKey: Guid(5c1c94160dee1874fa8f36c7cc83bc25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/battle_lingdigui_matnotfull.png using Guid(5c1c94160dee1874fa8f36c7cc83bc25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '31bd9cded4daff5bbbf2b156d44d3b74') in 0.358851 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_01.png
  artifactKey: Guid(c5595f8cb2ae5964b921bc6b31beb9a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_01.png using Guid(c5595f8cb2ae5964b921bc6b31beb9a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0557e6732dd21a7eb7596d4bad0378b9') in 0.648304 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_03.png
  artifactKey: Guid(45a8adad6e14cc14a9dcb2822afe6e8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_03.png using Guid(45a8adad6e14cc14a9dcb2822afe6e8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '578b9f5a11022149e077cd3819dabe77') in 0.289393 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_02_1.png
  artifactKey: Guid(81a3354b865cca046b5e7a752a044cdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_02_1.png using Guid(81a3354b865cca046b5e7a752a044cdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd27d30ce1a6b266b82fa8af2a9c6f3e9') in 0.016130 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_05.png
  artifactKey: Guid(8e7c1f660f079ed4bbfd1da67a6c9caf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_05.png using Guid(8e7c1f660f079ed4bbfd1da67a6c9caf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a891c66ff24104e8779a46a341870608') in 0.168185 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_02.png
  artifactKey: Guid(9634d4f43f696564d930e277d266b7f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldzt_icon_02.png using Guid(9634d4f43f696564d930e277d266b7f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e502b59693a6ce420c6f49baf5e92f66') in 0.073370 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/frame.png
  artifactKey: Guid(ec6f96ff6ba0b1843a17de87a46a09b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/frame.png using Guid(ec6f96ff6ba0b1843a17de87a46a09b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2d592c092859aa7ea6662e9f3015ef87') in 0.055869 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_06.png
  artifactKey: Guid(d78eafe5a4d91964eb3522d74a3fb59a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_06.png using Guid(d78eafe5a4d91964eb3522d74a3fb59a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e227518b2f15b62a8a52a558d35bcab') in 0.015628 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_02_2.png
  artifactKey: Guid(41832c86686f82e4d9b95bda71ab463e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_02_2.png using Guid(41832c86686f82e4d9b95bda71ab463e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3f1a28c6d00ba6e200d3543df85478af') in 0.061041 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_bg_06.png
  artifactKey: Guid(caf7b31fdba30a54eb1980bced13b4e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_bg_06.png using Guid(caf7b31fdba30a54eb1980bced13b4e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f82e5c5926e54ada1370c85db26b2cd7') in 0.024786 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_01.png
  artifactKey: Guid(331698540145cd749b86ff4bc1ee60e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_img_01.png using Guid(331698540145cd749b86ff4bc1ee60e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c59e2a69e768eb992c8daf7efe052c67') in 0.017535 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_bg_01.png
  artifactKey: Guid(ca8b93c2753489a4c859b57281566eb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AssetBundle/UI2/Forms/SurvivalBattle/LingDiGui/UITexture/ldg_bg_01.png using Guid(ca8b93c2753489a4c859b57281566eb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '758bccc951f02c86955ab527a0b8304b') in 0.035441 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0