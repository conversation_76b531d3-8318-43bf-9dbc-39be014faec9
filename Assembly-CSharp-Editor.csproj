﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{2FC59FCE-BCB1-13D3-FB79-5DD1AF79A2AF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2021_3_15;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;USE_SEARCH_ENGINE_API;USE_SEARCH_TABLE;USE_SEARCH_MODULE;USE_PROPERTY_DATABASE;USE_SEARCH_EXTENSION_API;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;SKY_PROTOBUF;SKY_JSON_DOT_NET;SKY_DEBUG;CROSS_PLATFORM_INPUT;UNITY_POST_PROCESSING_STACK_V1;AMPLIFY_SHADER_EDITOR;ODIN_INSPECTOR;ODIN_INSPECTOR_3;TextMeshPro;ODIN_INSPECTOR_3_1;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;NO_PACKAGE;DISABLE_UWA_SDK;ACTK_WALLHACK_LINK_XML;FLORA_PRESENT;GAIA_MESH_PRESENT;UPPipeline;BCG_RCCP;BCG_NEWINPUTSYSTEM;UNITY_POST_PROCESSING_STACK_V2;NWH_DWP2;RCCP_PHOTON;THIRD_PERSON_CONTROLLER;ULTIMATE_CHARACTER_CONTROLLER_UNIVERSALRP;TEXTMESH_PRO_PRESENT;GAIA_2_PRESENT;GAIA_PRO_PRESENT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.16</UnityProjectGeneratorVersion>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2021.3.15f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\ClientTableXmlGenerator.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\ExecutionOrderSetter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\PlayAnimationPreview.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\MeahReduction.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\TreeDataModel\AssetTreeView.cs" />
    <Compile Include="Assets\I2\Localization\Editor\PostProcessBuild_UnloadLanguages.cs" />
    <Compile Include="Assets\Editor\ScenePhsyXExport\ScenePhysxExport.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\NumericLabelCreator.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\MonsterSpawnAreaGenerator.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapObjectEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\TerrainGrassDetailGenerator.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MultiMeshBakerEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\CreateAssetWindow.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\QEM\QEM.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\SVNTools.cs" />
    <Compile Include="Assets\Script\Editor\Validator\MeshRendererValidator.cs" />
    <Compile Include="Assets\Script\Editor\Validator\MaterialValidator.cs" />
    <Compile Include="Assets\Script\Editor\GenMineralPosition.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DependenciesWindow\AssetDependenciesTreeModel.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Header\HeaderGUI.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\struct\Edge.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\ResourceRetrievalTool.cs" />
    <Compile Include="Assets\Script\Editor\ValidatorPro\ValidationErrorWindow.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LocalizeInspector.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Editor\GenSPCRPrefabFromFBX.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\Editor\MapPointEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\ZombieWaveConfigGenerator.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\OldResBaseControllEditor.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_ShaderTexturePropertyDrawer.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_UnityBuildSettings.cs" />
    <Compile Include="Assets\SGC\Editor\AssetBundleDebugger\RemoteDebuggerDefine.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LanguageSourceInspector.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\AddRemoveComponentsRecursively.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_AssetListScreen_Delete.cs" />
    <Compile Include="Assets\Easy Character Movement 2\Editor\ECM2FactoryEditor.cs" />
    <Compile Include="Assets\SGC\Editor\AssetBundleDebugger\VisualViewers\DebuggerBundleListViewer.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\ReferenceWindow\AssetReferenceWindow.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\MonsterCheckGridExportIsOverlap.cs" />
    <Compile Include="Assets\Script\Editor\FontCustom.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariUtility.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapTriggerAreaEditor.cs" />
    <Compile Include="Assets\Script\Editor\URPPlayerGalaxyshader.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\SearchTreeNumInRect.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsPrefabResolver.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_BuildPlatformType.cs" />
    <Compile Include="Assets\Script\Editor\Utils\QuickJobByThread.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapMovePlatformDrawer.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\Com\TrackInspector.cs" />
    <Compile Include="Assets\Script\Editor\FireFlyAreaEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\PreviewResizer.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Car\Editor\FgCarEditor.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildTasks\TaskCreateVersionManifest.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildTasks\TaskPrepare.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\SpawnPointLocationGenerator.cs" />
    <Compile Include="Assets\SDK\Shatter\Core\Library\Editor\ShatterToolEditor.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\core\MB3_BakeInPlace.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\SetLanguageInspector.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_StabilityEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Integrated\CommonIntegrated.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Editor\RTOccluderWindow.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\QEM\Pair.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\MaterialKeywordOptimizer.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Languages.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\UIAssetBundleBuilderFtp.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\StrongholdMover.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Editor\UIWindowControlViewEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\RegionConfigGenerator.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\Extensions\EditorWindowExtensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\PlayAudioPreview.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\FurnitureBaseEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\LongestCommonSubsequence.cs" />
    <Compile Include="Assets\Script\Editor\GenMaterialPointPosition.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\EditorTerrainTool.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\Editor\LoopScrollRectInspector.cs" />
    <Compile Include="Assets\SGC\Editor\UxmlLoader.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_EditorWindows.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\QEM\Vertex.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\SearchLayer.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\EditorCoroutineRunner.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\NewResBaseControllEditor.cs" />
    <Compile Include="Assets\Script\Editor\ModifyURPPlayerAO.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\InspectorsBase.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\DoorCoordinateGenerator.cs" />
    <Compile Include="Assets\VolumetricFog\Editor\VolumetricFogGaiaExtension.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\MeshDetailInfo.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\HLodCreate.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_RecordsEditor.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Editor\RTSimpleOccluderEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_SizeStatsScreen.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\TreeDataModel\AssetTreeModel.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\HLodTreeCreate.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Initializer.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_WheelColliderEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerUpHandler.cs" />
    <Compile Include="Assets\Editor\GetObjectPosition.cs" />
    <Compile Include="Assets\I2\Localization\Editor\PostProcessBuild_IOS.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_ClutchEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\Extensions\RectExtensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Sampler\ModelSampler.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Terms_Description.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\AIHiddenPointsGenerator.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\Editor\ObjectPointEditor.cs" />
    <Compile Include="Assets\Editor\ShaderKeywords\ShaderKeywords.cs" />
    <Compile Include="Assets\Script\Editor\GenStrongholdMsg.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\MiniJSON\MiniJSON.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Training\Editor\TargetObjectEditor.cs" />
    <Compile Include="Assets\Script\Editor\GenStaticResPosition.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenCollectionEditor.cs" />
    <Compile Include="Assets\ShaderComplexAnalysis\Editor\ShaderComplexAnalysis.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariStyle.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\IntegratedToolWindowSceneGUI.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\GenMonsterRandomPoint.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\WindowsExplorer.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MARParmGUI.cs" />
    <Compile Include="Assets\Script\Editor\Utils\SenceExportPictureEditor.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\GenSceneryMonster.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DependenciesWindow\AssetDependenciesWindow.cs" />
    <Compile Include="Assets\Script\Editor\UpdateGrassMessage.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\SmokeTrailEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\ChangeFontTool.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildTasks\TaskBuilding.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Integrated\MonsterIntegrated.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Editor\RTOccludeeEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\PackageSettings\EditorStatusWatcher.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_BatchPrefabBakerEditor.cs" />
    <Compile Include="Assets\FORGE3D\HelpMenu\Editor\F3DReadmeEditor.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\Editor\ShowNameEditor.cs" />
    <Compile Include="Assets\PolyFew\BatchFew\Editor\BatchFewShaderGUI.cs" />
    <Compile Include="Assets\FORGE3D\HelpMenu\Editor\F3DHelpMenu.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\FuzzyStringComparisonOptions.cs" />
    <Compile Include="Assets\Script\Editor\URPUnlitShader.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\core\MB3_MeshBakerEditorFunctions.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_Visual_DashboardEditor.cs" />
    <Compile Include="Assets\Editor\LayoutEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_MeshData.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\BRT_WindowLabels.cs" />
    <Compile Include="Assets\UIParticles\Editor\MenuOption.cs" />
    <Compile Include="Assets\Script\Editor\MineralAreaEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_BuildStepsScreen.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\DuplicateNodeNameHighlighter.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\MakeSplatLiner.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\HierarchySearcher.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\Com\ActionClipInspector.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MBVersionConcreteEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\Editor\SGMenuOptions.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\ResolverShader.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\OpenScene.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AIBZEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_DifferentialEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\NotBuildingGenerator.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\UIAssetBundleBuilder.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_DemoMaterialsEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\TransformUtilitiesWindow.cs" />
    <Compile Include="Assets\GPUSkinning\Editor\GPUSkinningStandardShaderGUI.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_Categorize.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\HiZDataGenerater.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\CheckIllegalName.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Editor\F3DTurretHandles.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Editor\RTConvexTowerOccluderEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\Customization\RCCP_VehicleUpgrade_WheelEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\PrefabUsageErrorFinder.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\TreeDataModel\AssetBaseWindow.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\core\MB3_MBVersionEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Def\Styles.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_UnityBuildReport.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\RoadTools.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\ThreadReturnData.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\ReferenceWindow\AssetReferenceTreeModel.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Editor\DecoratorEditor.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\RenameSetMonsterData.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\HotFixBuilderHelper.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_HelpScreen.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_MergeTerms.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\ResourceReplacementAreaChecker.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\BuildConfig.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_CarControllerEditor.cs" />
    <Compile Include="Assets\Script\Editor\GenDoorsPosition.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\MissingPrefabFinder.cs" />
    <Compile Include="Assets\Script\Editor\GenTransferPoint.cs" />
    <Compile Include="Assets\Script\Editor\Utils\EffectTestTool.cs" />
    <Compile Include="Assets\ActionEditor\Editor\App.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\AssetBundleFinder.cs" />
    <Compile Include="Assets\Script\Editor\FindShader.cs" />
    <Compile Include="Assets\Script\Editor\Utils\TableExportTemplate.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByRenderType.cs" />
    <Compile Include="Assets\SDK\Sky\MapPoint\Editor\TeamPointEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_BuildSettingsScreen.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_WelcomeWindow.cs" />
    <Compile Include="Assets\Editor\FGGrassEdit\GrassEditTool.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_NosEditor.cs" />
    <Compile Include="Assets\Script\EditorCoroutines\Editor\CoroutineWindowExample.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\DuplicateNodeNameFinder.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\LostPrefabLocator.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\core\MB_EditorUtil.cs" />
    <Compile Include="Assets\I2\Localization\Editor\EditorTools.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MeshBakerEditorWindow.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\UGUIJoystickEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\TblLocationCheck.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_TextureBakerEditorInternal.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Utillities\Editor\RangePropertyDrawer.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenNPCBornEditor.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\ResolverName.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\SyncBornToScene.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_SearchFilters.cs" />
    <Compile Include="Assets\Script\Editor\Utils\GenEnumCodeByTable.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\FolderFinder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\Camera\CameraControlPreview.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\BRT_WindowSettings.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AxlesEditor.cs" />
    <Compile Include="Assets\Script\Editor\Utils\ExportPrefabToTexture.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\SplitterView.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\IntegratedToolWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\BRT_WindowUtility.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\Com\GroupInspector.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MeshBakerEditorInternal.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LocalizationEditor.cs" />
    <Compile Include="Assets\Script\Editor\Builder\PCBuilder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\Editor\FyLocalizationTextEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\TableCheckTool.cs" />
    <Compile Include="Assets\Script\Editor\ObjectFindHelper.cs" />
    <Compile Include="Assets\Script\Editor\GenRevivePoint.cs" />
    <Compile Include="Assets\I2\Localization\Editor\UpgradeManager.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelineTrackItemView.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\AttachToolEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\TrapsLocationGenerator.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_AssetListScreen.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Integrated\EditorAssistUtilityIntegrated.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\ClipDraw\SimpleClipDraw.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\MenuGameObject.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\SpriteMultiplePivotMenu.cs" />
    <Compile Include="Assets\XLua\Src\Editor\TemplateRef.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\TreeDataModel\AssetTreeViewItem.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_FixAxisWindow.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\SelectModifyAsset.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\CheckSupplyHeight.cs" />
    <Compile Include="Assets\Script\Editor\Builder\BaseBuilder.cs" />
    <Compile Include="Assets\Script\Editor\AnimtionClipOptimizeToolKit.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\LandscapeMonsterGenerator.cs" />
    <Compile Include="Assets\AQUAS 2020\Scripts\Editor\PW_Water_MaterialEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\PlayParticlePreview.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\ResourceCheckTool.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\LoadPrefabPreview.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_BaseScreen.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\OverlapCoefficient.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\SmoothTrailEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_ExteriorCamerasEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\SectorIdAreaDisplay.cs" />
    <Compile Include="Assets\SDK\Sky\DataBind\Editor\ContextUnityEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\PointerEventData.cs" />
    <Compile Include="Assets\XLua\Editor\ExampleConfig.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\NBLayout.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Editor\SPCRJointDynamicsFashionEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\GuiEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Options\BRT_Options.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\TrailEditor_Base.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerExitHandler.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\ReferenceWindow\AssetReferenceTreeView.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Editor\PopupToggleTemplate.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\PreferencesWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_BuildInfo.cs" />
    <Compile Include="Assets\SGC\Editor\AssetBundleDebugger\AssetBundleDebuggerWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\JaccardDistance.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsSceneResolver.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\DldUtil_BackwardFileReader.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\NestedObjectFinder.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\CutholeInEditor.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\BuildResult.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Languages\LanEN.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByMaterial.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_OverviewScreen.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_ReportGenerator_PublicAPI.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_GameObjectMaterialInfo.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LocalizationParamsManagerInspector.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_GroundMaterialsEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\IrregularObstructionFinder.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\ResourceManagerInspector.cs" />
    <Compile Include="Assets\Script\Editor\ImageMaskReplace.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\TerrainProcessorAndTreeCoordinateGenerator.cs" />
    <Compile Include="Assets\Script\Editor\Utils\AutoSetAsset.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_ReportGenerator.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\DuplicateNodeNamePrinter.cs" />
    <Compile Include="Assets\Script\Editor\Validator\CharacterValidator.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\SectorIdFinder.cs" />
    <Compile Include="Assets\Script\Editor\URPPlayershader.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\UIResolver.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\GetLodGroupValue.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\DrawTools.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\WeaponEditorManager.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_CameraEditor.cs" />
    <Compile Include="Assets\Editor\GetAllChinese.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelineView.cs" />
    <Compile Include="Assets\Script\Editor\DisplayProgressUtils.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenTreasurePointEditor.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\core\MB3_TextureCombinerEditorFunctions.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\GzmosManager.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\TerrainTextureArrayCreate.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_AssetDependencyGenerator.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\IntegratedNode.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_NoLocalized.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\MonsterAreaEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_OtherAddonsEditor.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_ParseTerms.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenFireFlyEditor.cs" />
    <Compile Include="Assets\Script\Editor\CheckGuideInSector.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\InfoDialogueSpawner.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\TagChangePanel.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\AllResourceWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\JaroWinklerDistance.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Editor\F3DTurretEditor.cs" />
    <Compile Include="Assets\Editor\SwitchToIsland2.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\TerrainMsgAssetsCreate.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\Extensions\DirectableAssetExtensions.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\BRT_Util.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_GearboxEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Integrated\QueryHelperIntegrated.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsTblResolver.cs" />
    <Compile Include="Assets\GPUSkinning\Editor\GPUSkinningSamplerEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\InitLoad\RCCP_InitLoad.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\StaticObjectCoordinatesGenerator.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\BRT_BuildReportWindow.cs" />
    <Compile Include="Assets\Script\Editor\GenBornPosition.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_SetScriptingSymbol.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\TblResolver.cs" />
    <Compile Include="Assets\Script\Editor\CreateControlle.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DuplicateWindow\AssetDuplicateTreeModel.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Integrated\FeatureGeneratorIntegrated.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\Extensions\TrackExtends.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\GroundedObjectChecker.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_CharSet.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\MonsterOldToNewID.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Options\BRT_FileFilters.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\PrefabResolver.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByStatic.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\ExploreScene.cs" />
    <Compile Include="Assets\Script\Editor\PathPoint\PathPoint.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\FurnitureTemplateEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapSceneUIEditor.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Editor\ErosionBrushEditor.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\IBuildTask.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariSetting.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MeshBakerGrouperEditor.cs" />
    <Compile Include="Assets\Script\Editor\Validator\GameObjectValidator.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\AIMonsterEditor.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\TreasureObjectTemplateCopyEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\MonsterObstacleCoordinateGenerator.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Constructor\Editor\F3DTurretEditorWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_TextureDataGenerator.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\Extension.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\MeshReadWriteEnabler.cs" />
    <Compile Include="Assets\Script\Editor\GenTrapResPosition.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariHandler.cs" />
    <Compile Include="Assets\Script\Editor\SectorSwitch\SectorSwitchEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Options\BRT_FiltersUsed.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Base\ItemViewBase.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsUIResolver.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DuplicateWindow\AssetDuplicateTreeView.cs" />
    <Compile Include="Assets\Script\Editor\CleanPlayerPrefs.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\SorensenDiceDistance.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\EditorTools.cs" />
    <Compile Include="Assets\XLua\Editor\SetModLuaInfoWnd.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTextureTools.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\BuildingEditorController.cs" />
    <Compile Include="Assets\Script\Editor\SceneDoorEditor.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\CheckMissPrefab.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Preview\PreviewBase.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_AssetDependencies.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\PackageSettings\PackageSettings.cs" />
    <Compile Include="Assets\Script\Editor\Builder\Utils\GradleConfigParser.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapRotationDrawer.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_AssetListScreen_AssetUsage.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_Script.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\SearchSectorId.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_BuildInfo_Commands.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\ShowSectorIDArea.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Training\Editor\TargetMovePlatformEditor.cs" />
    <Compile Include="Assets\Script\Editor\PBRShaderGUI.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByShader.cs" />
    <Compile Include="Assets\SGC\Editor\EditorTools.cs" />
    <Compile Include="Assets\Script\Editor\Validator\Texture2DValidator.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DuplicateWindow\AssetDuplicateWindow.cs" />
    <Compile Include="Assets\Script\Editor\URPPlayer_laserShader.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Sampler\AudioSampler.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\GenerateTreesTable.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\IContextObject.cs" />
    <Compile Include="Assets\VolumetricFog\Editor\FogVolumeExtensions.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\Editor\FyLocalizationImageEditor.cs" />
    <Compile Include="Assets\Script\Effect\Shared\Generic\Editor\InfoDialogue.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\Customization\RCCP_VehicleUpgrade_SirenEditor.cs" />
    <Compile Include="Assets\Editor\ExportSceneToObj\SceneMeshExport.cs" />
    <Compile Include="Assets\FORGE3D\HelpMenu\Editor\F3DReadme.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\TeleportPointLocationGenerator.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AxleEditor.cs" />
    <Compile Include="Assets\Editor\ExportSceneInfo\SceneInfoBuilder.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\BRT_AssetList_Sort_Texture.cs" />
    <Compile Include="Assets\Script\Editor\Validator\TransformValidator.cs" />
    <Compile Include="Assets\Script\Editor\TerrainExportForAI.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_CheckBeforePlay.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_InputEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_BuildSettingsCategoryType.cs" />
    <Compile Include="Assets\GPUSkinning\Editor\GPUSkinningPlayerMonoEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\IntegratedFeatureAttribute.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelinePointerView.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\SceneNameAdjuster.cs" />
    <Compile Include="Assets\Script\Editor\GenDontBuildPoint.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_DamageEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\TreeCountInAreaFinder.cs" />
    <Compile Include="Assets\Script\Editor\MobileTerminalTerrainConverter.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\SearchDirectory.cs" />
    <Compile Include="Assets\SGC\Editor\EditorDefine.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Terms.cs" />
    <Compile Include="Assets\Script\Editor\GenSupplyPosition.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_IDataFile.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\BRT_LibCacheUtil.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\RespawnPointGenerator.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\LuaResolver.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\DldUtil_GetRspDefines.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\BuildMinMaxHeightMap.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\DuplicateObjectNameFinder.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\EditAssistRename.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\FuzzyStringComparisonTolerance.cs" />
    <Compile Include="Assets\Script\Editor\NPCAreaEditor.cs" />
    <Compile Include="Assets\Script\Editor\BatchRemoveComponent.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\HotFixBuilder.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\BuildContext.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_LightsEditor.cs" />
    <Compile Include="Assets\Script\Editor\GenTreasureArea.cs" />
    <Compile Include="Assets\XLua\Src\Editor\Hotfix.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_LimiterEditor.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Warnings.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Preview\AssetPlayer.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\ApproximatelyEquals.cs" />
    <Compile Include="Assets\Script\Editor\GenAirDropPointPosition.cs" />
    <Compile Include="Assets\VolumetricFog\Editor\VolumetricFogInspector.cs" />
    <Compile Include="Assets\I2\Localization\Editor\PostProcessBuild_ANDROID.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_AssetListScreen_Search.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AeroDynamicsEditor.cs" />
    <Compile Include="Assets\Script\Editor\ReimportAllFBX.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Languages\LanCHS.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AIEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AudioEditor.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\StreamingAssetsHelper\StreamingAssetsHelper.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsCommonResolver.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\ParticleCheckMax.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\ICustomized.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenSupplyEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\TblEnumCodeGenerator.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\ResolverFolder.cs" />
    <Compile Include="Assets\Editor\BakeNodeReset.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\InspectorPreviewAssetInspector.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\SPCRJointDynamics\Editor\SPCRJointDynamicsEditor.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\DependenciesWindow\AssetDependenciesTreeView.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\ActionEditorWindow.cs" />
    <Compile Include="Assets\Script\Editor\SceneHierarchyWindow.cs" />
    <Compile Include="Assets\Script\Editor\StreamLoadExporter.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\languageDiffChange.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MARSkinStockingParmGUI.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerDragHandler.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapDoorEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\JaroDistance.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\ABBuilder.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\Transform\MoveToPreview.cs" />
    <Compile Include="Assets\Script\Editor\Builder\AndroidBuilder.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MAR_DiamondParmGUI.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\HammingDistance.cs" />
    <Compile Include="Assets\UIParticles\Editor\UiParticlesEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_SettingsEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\ClipDraw\SignalClipDraw.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerDownHandler.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\ObjectTypeRetriever.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenNotAllowAreaEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\PackageSettings\ScriptableSingleton.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\UpdateUnityEditorTitle.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\MissingReferenceFinder.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Def\PrefsConst.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\AudioChecker.cs" />
    <Compile Include="Assets\Script\Editor\GenTreesPosition.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\AssetBundleBuilder.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\TanimotoCoefficient.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\SceneAudioConfigGenerator.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\TrailPreviewUtillity.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\EditorUtils.cs" />
    <Compile Include="Assets\Script\Editor\Validator\CharacterHairValidator.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\TrailEditor.cs" />
    <Compile Include="Assets\Script\Effect\Trails\Editor\SmokePlumeEditor.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Spreadsheet_Local.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Languages\ILanguages.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\NpcLocationGenerator.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariHandlerDemo.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\LongestCommonSubstring.cs" />
    <Compile Include="Assets\Editor\AssembliesReferencingCSharpViewer.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IDragEndHandler.cs" />
    <Compile Include="Assets\Script\Editor\ModelImport.cs" />
    <Compile Include="Assets\Script\Editor\Validator\LODGroupValidator.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IDragBeginHandler.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\TaskAttribute.cs" />
    <Compile Include="Assets\Script\Editor\AssetPrefabBuilder.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByAlreadyAdded.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\TreeDataModel\AssetMultiColumnHeader.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\GenResourceEditor.cs" />
    <Compile Include="Assets\Script\Editor\URPDiffuseShader.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MARChiffonParmGUI.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapElevatorEditor.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_LightEditor.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\CheckSceneSameName.cs" />
    <Compile Include="Assets\Ultimate Game Tools\MeshSimplify\Editor\MeshSimplifyEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_UnityBuildSettingsUtility.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UGUI\LoopScrollView\Editor\SGDefaultControls.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\HotFixEditorTools.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Editor\PolyfewMenu.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByLightmapIndex.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\searchFilters\MB3_GroupByOutOfBoundsUVs.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\CheckResources.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerClickHandler.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\FindNestedPrefabs.cs" />
    <Compile Include="Assets\FORGE3D\Sci-Fi Effects\Code\Turrets\Constructor\Editor\F3DTurretConstructorEditor.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariWindow.cs" />
    <Compile Include="Assets\Script\Editor\FGPrefabLightmapDataEditor.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MAR_SimpleLODParmGUI.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\BRT_AssetList_Sort.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MAR_Visualization_Channel_Tool.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Header\HeaderBase.cs" />
    <Compile Include="Assets\Script\Editor\GenNPCPosition.cs" />
    <Compile Include="Assets\Script\Occlusion\Editor\OcclusionEditor.cs" />
    <Compile Include="Assets\SDK\Sky\Scene\UI\Editor\UIWindowControlViewPrefabSaver.cs" />
    <Compile Include="Assets\FORGE3D\PoolManager\Editor\F3DPoolManagerEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\MonsterSpawnCoordinatesGenerator.cs" />
    <Compile Include="Assets\Script\Editor\Validator\MeshFilterValidator.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\FindPrefabsWithSpecificImageTool.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_BoneWeightCopierEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapSwitchEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\PublicEquipmentCoordinateGenerator.cs" />
    <Compile Include="Assets\XLua\Src\Editor\LinkXmlGen\LinkXmlGen.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_TextureBakerEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_SizePart.cs" />
    <Compile Include="Assets\Script\Editor\NewEditorTest.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\HavenBaseControllEditor.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\SceneResolver.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\DldUtil_TraverseDirectory.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildSystem\BuildRunner.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\Builder.cs" />
    <Compile Include="Assets\Script\Editor\PBR_MARSilkParmGUI.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportGeneration\BRT_MeshDataGenerator.cs" />
    <Compile Include="Assets\Script\Editor\Validator\BaseValidator.cs" />
    <Compile Include="Assets\Script\Editor\GenFacilityBox.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\AIObstacleConfigEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\ShaderFinder.cs" />
    <Compile Include="Assets\Script\Editor\ExportHeightmap.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\EditorCustomFactory.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\WelcomeView.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapLaserTriggerEditor.cs" />
    <Compile Include="Assets\Script\Editor\WeaponEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\Operations.cs" />
    <Compile Include="Assets\Editor\WaterSurfaceEdit\Inspector_WaterSurfaceEdit.cs" />
    <Compile Include="Assets\Script\Editor\ChangeFormat.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\StrongholdInfoGenerator.cs" />
    <Compile Include="Assets\Script\Model\InputSystem\Editor\InputLayoutEditorRegister.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\LevenshteinDistance.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\QueryHelper\UIQueryTool.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Spreadsheet_Google.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\ClipDragger.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelineHeaderView.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Languages\Lan.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\Customization\RCCP_VehicleUpgrade_PaintEditor.cs" />
    <Compile Include="Assets\Script\Editor\LodAnimationClip.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\CreatSubMeshToPrefab.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\ImageDimensions.cs" />
    <Compile Include="Assets\Script\EditorCoroutines\Editor\EditorCoroutines.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\ClipDraw\ClipDrawer.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\Customization\RCCP_VehicleUpgrade_SpoilerEditor.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsLuaResolver.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Trap\Editor\TrapMovePlatformEditor.cs" />
    <Compile Include="Assets\Editor\ScenePhsyXExport\ScenePhsyXExportWnd.cs" />
    <Compile Include="Assets\Script\Editor\ValidatorPro\AssetValidationPostprocessor.cs" />
    <Compile Include="Assets\ErosionBrush\Scripts\Editor\Layout.cs" />
    <Compile Include="Assets\Script\Editor\CollectionCircleAreaEditor.cs" />
    <Compile Include="Assets\XLua\Editor\LuaFrameworkConfig.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\AirdropPointGenerator.cs" />
    <Compile Include="Assets\Script\Editor\SearchTool\SencePrefabUseError.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Events\Interfaces\IPointerEnterHandler.cs" />
    <Compile Include="Assets\Editor\TextFontEditor.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\GenMonsterPosition.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\FuzzyString\RatcliffObershelpSimilarity.cs" />
    <Compile Include="Assets\Script\Editor\PathPoint\Serialization.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildParameters.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\TermsPopup_Drawer.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\ClipDraw\BasicClipDraw.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_ExhaustsEditor.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools_Scenes.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\struct\MeshMessage.cs" />
    <Compile Include="Assets\HotFixSystem\Editor\BuildParametersContext.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\Delaunay.cs" />
    <Compile Include="Assets\Editor\AssembliesViewer.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\MountAllowedZone.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\ABBuilder.cs" />
    <Compile Include="Assets\Script\Occlusion\Editor\OcclusionExporter.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Door\Editor\DoorResUIEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\CheckMonsterBornSite.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\DldUtil_BigFileReader.cs" />
    <Compile Include="Assets\Script\Editor\CollectionAreaEditor.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\TerrrainPrefabTool.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundleBuilder\Editor\Resolver.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Training\Editor\TrainingResUIEditor.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Preview\TimePointers.cs" />
    <Compile Include="Assets\SGC\Editor\ABBuilder\ABBuilderUI.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\BRT_VersionInfo.cs" />
    <Compile Include="Assets\Script\Editor\GenSceneSound.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\GenZombieTide.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\EditorAssistUtility\GizmoVisualizer.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\ResourcePointConfigGenerator.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_ParticlesEditor.cs" />
    <Compile Include="Assets\Script\Editor\HLodAssetsGenerater.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\struct\Triangle.cs" />
    <Compile Include="Assets\Script\Editor\MineralCircleAreaEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\FeatureGenerator\PersonalChestLocationGenerator.cs" />
    <Compile Include="Assets\Script\Editor\MeshReduction\struct\Matrix.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelineBottomView.cs" />
    <Compile Include="Assets\XLua\Src\Editor\Generator.cs" />
    <Compile Include="Assets\Script\Editor\Validator\MeshColliderValidator.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Timeline\TimelineMiddleView.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\SetPropertyDrawer.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Common\MaterialOptimizer.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ActionEditor\Views\Base\ViewBase.cs" />
    <Compile Include="Assets\Editor\EditorSceneManagerSceneSaved.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\Customization\RCCP_CustomizationApplierEditor.cs" />
    <Compile Include="Assets\Script\Editor\IntegratedTool\Monster\ZombieHordeTargetConfigGenerator.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_MeshBakerEditor.cs" />
    <Compile Include="Assets\SGC\Editor\UxmlDefine.cs" />
    <Compile Include="Assets\ActionEditor\Editor\Tools\Extensions\FieldInfoExtensions.cs" />
    <Compile Include="Assets\SGC\Editor\AssetBundleDebugger\RemotePlayerSession.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\DldUtil_UnityVersion.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Localization\LocalizationEditor_Tools.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\CustomizedDrawTools.cs" />
    <Compile Include="Assets\Script\Editor\Builder\IOSBuilder.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\BMFontExporter.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\SplitAnimsProcessor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Window\Screen\BRT_OptionsScreen.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\Utility\BRT_AssetList_Sort_Mesh.cs" />
    <Compile Include="Assets\SDK\MeshBaker\scripts\Editor\MB3_DisableHiddenAnimationsEditor.cs" />
    <Compile Include="Assets\PolyFew\Scripts\Editor\InspectorDrawer.cs" />
    <Compile Include="Assets\Script\Editor\TextureSRGBTool.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\Customized\Inspectors\Com\AssetInspector.cs" />
    <Compile Include="Assets\Script\Editor\URPStaticObjectshader.cs" />
    <Compile Include="Assets\Script\Editor\AIMonstor\GenZombieTideTarget.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LanguageSourceAssetInspector.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_EngineEditor.cs" />
    <Compile Include="Assets\SDK\Sky\AssetBundle\Editor\FpsResolver.cs" />
    <Compile Include="Assets\Realistic Car Controller Pro\Editor\RCCP_AIWPEditor.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\ActionSystem\Editor\Preview\Clips\Transform\MoveByPreview.cs" />
    <Compile Include="Assets\I2\Localization\Editor\Inspectors\LocalizeDropdownInspector.cs" />
    <Compile Include="Assets\ActionEditor\Editor\GUIS\ObjectSelectorWindow.cs" />
    <Compile Include="Assets\Script\SurvivalBattle\Localization\Editor\FYLocalizationEditor.cs" />
    <Compile Include="Assets\Script\Editor\TerrainTools\CreateTexture.cs" />
    <Compile Include="Assets\Script\Editor\EditorAssist\MoveHoldTool.cs" />
    <Compile Include="Assets\RTOcclusionCulling\Editor\RTOcclusionCullingEditor.cs" />
    <Compile Include="Assets\UnityAssetDanshari-master\Editor\AssetDanshariWatcher.cs" />
    <Compile Include="Assets\Script\Editor\ResourceGen\NewOneTapAllExportEditor.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_TextureData.cs" />
    <Compile Include="Assets\Script\Editor\HotfixCfg.cs" />
    <Compile Include="Assets\Script\Editor\Validator\ValidatorWindow.cs" />
    <Compile Include="Assets\BuildReport\Scripts\Editor\ReportData\BRT_AssetList.cs" />
    <Compile Include="Assets\Script\Editor\GenAreaSection.cs" />
    <Compile Include="Assets\VolumetricFog\Editor\VolumetricFogAbout.cs" />
    <Compile Include="Assets\Script\EditorCoroutines\Editor\EditorCoroutineExtensions.cs" />
    <Compile Include="Assets\SDK\Sky\Editor\ParticleAndAnimationInspector.cs" />
    <Compile Include="Assets\Script\Editor\GenPlayerPrefabFromFBX.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Script\Editor\TerrainTools\BuildMinMaxHeightMap.compute" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r13p0-00rel0.dll" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Editor\VacuumShaders.TerrainToMesh_Editor.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaInterfaceBridge.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\msvcp110.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r5p0-00rel0.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaWrapPusher.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r7p0-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-400_r6p1-00rel0.dll" />
    <None Include="Assets\VacuumShaders\Editor\VacuumShaders.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r8p0-00rel0.dll" />
    <None Include="Assets\EasyRoads3D\Editor\EasyRoads3Dv3Editor.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\msvcr120.dll" />
    <None Include="Assets\BuildReport\Scripts\Editor\FuzzyString\FuzzyStringLicense.txt" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaDelegateWrap.tpl.txt" />
    <None Include="Assets\SGC\Editor\AssetBundleDebugger\VisualViewers\DebuggerBundleListViewer.uxml" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaRegister.tpl.txt" />
    <None Include="Assets\Editor\ScenePhsyXExport\Plugins\x64\PhysX3Common_x64.dll" />
    <None Include="Assets\Demigiant\DOTween\Editor\DOTweenEditor.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaClassWrap.tpl.txt" />
    <None Include="Assets\FORGE3D\HelpMenu\Editor\F3DEditorReference.asmref" />
    <None Include="Assets\Editor\ScenePhsyXExport\Plugins\x64\PxSerialization.dll" />
    <None Include="Assets\Script\Editor\PriorityQueue.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaRegisterGCM.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r4p1-00rel0.dll" />
    <None Include="Assets\Editor\ScenePhsyXExport\Plugins\x64\PhysX3_x64.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r12p0-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r11p0-00rel0.dll" />
    <None Include="Assets\Editor\ScenePhsyXExport\Plugins\x64\PhysX3Cooking_x64.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-400_r7p0-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-400_r5p0-01rel0.dll" />
    <None Include="Assets\XLua\Src\Editor\LinkXmlGen\LinkXmlGen.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\compiler_manager.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r3p0-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\msvcr110.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\THIRD-PARTY-LICENSES.txt" />
    <None Include="Assets\SGC\Editor\AssetBundleDebugger\AssetBundleDebuggerWindow.uxml" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r5p1-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r9p0-00rel0.dll" />
    <None Include="Assets\VacuumShaders\Terrain To Mesh\Editor\VacuumShaders.TerrainToMeshVC.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\PackUnpack.tpl.txt" />
    <None Include="Assets\RTOcclusionCulling\Editor\OcclusionInternal.shader" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaEnumWrapGCM.tpl.txt" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaClassWrapGCM.tpl.txt" />
    <None Include="Assets\I2\Localization\Editor\Unity XCode\UnityEditor.iOS_I2Loc.Xcode.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\TemplateCommon.lua.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\samples\README.txt" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaDelegateBridge.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-400_r4p0-00rel1.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\msvcp120.dll" />
    <None Include="Assets\Demigiant\DOTween\Editor\DOTweenEditor.XML" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r10p0-00rel0.dll" />
    <None Include="Assets\XLua\Src\Editor\Template\LuaEnumWrap.tpl.txt" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r6p0-00rel0.dll" />
    <None Include="Assets\ShaderComplexAnalysis\Editor\Mali_Offline_Compiler_v5.6.0\openglessl\Mali-T600_r4p0-00rel0.dll" />
    <None Include="Assets\I2\Localization\Editor\Unity XCode\Xcode.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinValidator.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Demigiant\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet">
      <HintPath>Assets\Plugins\Editor\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="I18N">
      <HintPath>Assets\Plugins\I18N.dll</HintPath>
    </Reference>
    <Reference Include="EasyRoads3Dv3">
      <HintPath>Assets\EasyRoads3D\lib\EasyRoads3Dv3.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net">
      <HintPath>Assets\Plugins\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Unsafe">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Packages\com.unity.collections@1.3.1\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="DelaunayER">
      <HintPath>Assets\EasyRoads3D\lib\DelaunayER.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets\I2\Localization\Editor\Unity XCode\UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="FullSerializer">
      <HintPath>Assets\ActionEditor\Runtime\ThirdParty\FullSerializer.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.AOTGenerated">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\AOT\Sirenix.Serialization.AOTGenerated.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Pdb">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.0.2\Runtime\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="VacuumShaders.TerrainToMesh_Editor">
      <HintPath>Assets\VacuumShaders\Terrain To Mesh\Editor\VacuumShaders.TerrainToMesh_Editor.dll</HintPath>
    </Reference>
    <Reference Include="PriorityQueue">
      <HintPath>Assets\Script\Editor\PriorityQueue.dll</HintPath>
    </Reference>
    <Reference Include="VacuumShaders.TerrainToMesh">
      <HintPath>Assets\VacuumShaders\Terrain To Mesh\Scripts\VacuumShaders.TerrainToMesh.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Plugins\protobuf-net.dll</HintPath>
    </Reference>
    <Reference Include="EasyRoads3Dv3Editor">
      <HintPath>Assets\EasyRoads3D\Editor\EasyRoads3Dv3Editor.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets\Plugins\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
    </Reference>
    <Reference Include="I18N.CJK">
      <HintPath>Assets\Plugins\I18N.CJK.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Mdb">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>Assets\Plugins\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats">
      <HintPath>Assets\Plugins\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>Assets\Plugins\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="Ude">
      <HintPath>Assets\Plugins\Ude.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="UWA_GPM">
      <HintPath>Assets\UWA\UWA_GPM\Runtime\ManagedLibs\UWA_GPM.dll</HintPath>
    </Reference>
    <Reference Include="FluentFTP">
      <HintPath>Assets\Plugins\Editor\FluentFTP.dll</HintPath>
    </Reference>
    <Reference Include="VacuumShaders">
      <HintPath>Assets\VacuumShaders\Editor\VacuumShaders.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Rocks">
      <HintPath>Library\PackageCache\com.unity.burst@1.7.3\Unity.Burst.CodeGen\Unity.Burst.Cecil.Rocks.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>F:\Unity 2021.3.15f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
      <HintPath>Library\ScriptAssemblies\UnityEditor.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
      <HintPath>Library\ScriptAssemblies\UnityEngine.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Cinemachine">
      <HintPath>Library\ScriptAssemblies\Cinemachine.dll</HintPath>
    </Reference>
    <Reference Include="com.unity.cinemachine.editor">
      <HintPath>Library\ScriptAssemblies\com.unity.cinemachine.editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj">
      <Project>{3A1B43A0-276E-34FB-197E-1ACDE9001A0F}</Project>
      <Name>Assembly-CSharp-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp.csproj">
      <Project>{A2859A56-AB4A-51F9-60BB-1B6FC96E8C70}</Project>
      <Name>Assembly-CSharp</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp-Editor-firstpass.csproj">
      <Project>{6719E071-572B-6877-B821-8A6B33EC3A6C}</Project>
      <Name>Assembly-CSharp-Editor-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Runtime.csproj">
      <Project>{47D3AC7D-994B-FF43-7BD2-399821E768B0}</Project>
      <Name>HybridCLR.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Shaders.csproj">
      <Project>{24A3728F-9EC4-6FC4-F6AF-9F7B197997CA}</Project>
      <Name>Unity.RenderPipelines.Universal.Shaders</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRF.Editor.csproj">
      <Project>{11A79E45-962C-EBDF-05EC-54FE51EE8C4C}</Project>
      <Name>StompyRobot.SRF.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.Editor.csproj">
      <Project>{2E319F8D-9E1D-0A9A-B421-889026ABEECA}</Project>
      <Name>YooAsset.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="com.bartofzo.nativetrees.samples.csproj">
      <Project>{881DA922-C840-2495-D6F1-8361CFB31DD1}</Project>
      <Name>com.bartofzo.nativetrees.samples</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRDebugger.Editor.csproj">
      <Project>{4B8DC056-4E1B-382A-F1F1-16E4F448DBA5}</Project>
      <Name>StompyRobot.SRDebugger.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Sirenix.OdinInspector.Modules.UnityMathematics.csproj">
      <Project>{10DB790B-7E9A-5842-FBDE-7C0ACBB264BA}</Project>
      <Name>Sirenix.OdinInspector.Modules.UnityMathematics</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{B220812B-9F36-B090-CEA1-BBA27BEB3963}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="SPCRJointDynamics.csproj">
      <Project>{EA626DCB-547D-DC2C-ED04-916E485DEC52}</Project>
      <Name>SPCRJointDynamics</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.FSM.csproj">
      <Project>{69BC925E-6C80-301F-C49D-B0815D9CC38A}</Project>
      <Name>Animancer.FSM</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.TextMeshPro.csproj">
      <Project>{AA75BE1D-D383-01A4-15DC-B38CACF827E2}</Project>
      <Name>UniTask.TextMeshPro</Name>
    </ProjectReference>
    <ProjectReference Include="RideMalbersSystem.csproj">
      <Project>{31313ECF-AD69-E4E1-B4D0-7A0DCC86E057}</Project>
      <Name>RideMalbersSystem</Name>
    </ProjectReference>
    <ProjectReference Include="RideMalbersSystemEditor.csproj">
      <Project>{9EF83544-2A1B-89F5-C22F-0F2EC419539C}</Project>
      <Name>RideMalbersSystemEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Whinarn.UnityMeshSimplifier.Editor.csproj">
      <Project>{999D9AC0-0504-57C2-3551-7C7EA26D33B4}</Project>
      <Name>Whinarn.UnityMeshSimplifier.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="zstring.csproj">
      <Project>{183E02AF-BAB0-4091-376E-05D6D94D9C93}</Project>
      <Name>zstring</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipeline.Universal.ShaderLibrary.csproj">
      <Project>{6DF604D4-361A-B9F2-EB1F-784F47182D41}</Project>
      <Name>Unity.RenderPipeline.Universal.ShaderLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.csproj">
      <Project>{8505417A-FC25-BCB9-D556-603B8F4CD50F}</Project>
      <Name>Animancer</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.Editor.csproj">
      <Project>{A737B58D-C244-1FCB-7884-DF080DBEE2B2}</Project>
      <Name>Unity.Mathematics.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Collections.csproj">
      <Project>{92277F0B-731A-8F80-2073-C19E264C2B04}</Project>
      <Name>Unity.Collections</Name>
    </ProjectReference>
    <ProjectReference Include="YooAsset.csproj">
      <Project>{66A4DF8A-E657-4C7A-BEA4-51B7AECF0597}</Project>
      <Name>YooAsset</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_gpm.csproj">
      <Project>{F1224434-5197-8351-C0E1-3B44EFD16B04}</Project>
      <Name>youhu.unity_uwa_gpm</Name>
    </ProjectReference>
    <ProjectReference Include="OffScreenParticleRendering.csproj">
      <Project>{B7331974-73C3-A19D-5E67-E90538624BCA}</Project>
      <Name>OffScreenParticleRendering</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Addressables.csproj">
      <Project>{C95E8050-8D17-81EA-FAB8-39CEBB1ABC10}</Project>
      <Name>UniTask.Addressables</Name>
    </ProjectReference>
    <ProjectReference Include="OverdrawMonitor.csproj">
      <Project>{2AFBFC97-5138-74DB-F1A5-CBA57ED95054}</Project>
      <Name>OverdrawMonitor</Name>
    </ProjectReference>
    <ProjectReference Include="Whinarn.UnityMeshSimplifier.Runtime.csproj">
      <Project>{923E1988-1640-D9A6-6E0F-0315D5A35B51}</Project>
      <Name>Whinarn.UnityMeshSimplifier.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Localization.csproj">
      <Project>{78D9E99F-E53F-BDCD-F36C-4CBCC6468FDA}</Project>
      <Name>Localization</Name>
    </ProjectReference>
    <ProjectReference Include="Animancer.Examples.csproj">
      <Project>{4D345168-ACC0-58B3-9CAB-44FC0409F3F7}</Project>
      <Name>Animancer.Examples</Name>
    </ProjectReference>
    <ProjectReference Include="WaterSystem.csproj">
      <Project>{B31A9E1B-C59F-4889-4BA1-E8BF4CF7A72E}</Project>
      <Name>WaterSystem</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj">
      <Project>{937E0ECA-6A9C-46C7-F2FF-0F16CC228C0C}</Project>
      <Name>Unity.RenderPipelines.Universal.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="PPv2URPConverters.csproj">
      <Project>{DF942A2F-4BCF-43AA-A508-9C7F5CA5CF45}</Project>
      <Name>PPv2URPConverters</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRDebugger.csproj">
      <Project>{59F2960F-44FD-5DC1-8CBF-85558885274E}</Project>
      <Name>StompyRobot.SRDebugger</Name>
    </ProjectReference>
    <ProjectReference Include="com.bartofzo.nativetrees.csproj">
      <Project>{FE16973C-C194-FC9A-C1F3-8F7ECF48D69F}</Project>
      <Name>com.bartofzo.nativetrees</Name>
    </ProjectReference>
    <ProjectReference Include="MalbersAnimations.Cinemachine.csproj">
      <Project>{13BDA4B4-D002-D150-1299-EA05B554F8AC}</Project>
      <Name>MalbersAnimations.Cinemachine</Name>
    </ProjectReference>
    <ProjectReference Include="youhu.unity_uwa_gpm.Editor.csproj">
      <Project>{A640743C-E7EE-1538-8CB2-0748CF5E6518}</Project>
      <Name>youhu.unity_uwa_gpm.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="SGCResComponent.csproj">
      <Project>{785EA918-9D8D-93CA-0458-C7FAE74B1B4D}</Project>
      <Name>SGCResComponent</Name>
    </ProjectReference>
    <ProjectReference Include="WaterSystem.Editor.csproj">
      <Project>{E6E9EB74-4F83-DE6C-4092-3B304B72BD58}</Project>
      <Name>WaterSystem.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.DOTween.csproj">
      <Project>{41F38BD8-5A7D-2DFB-C665-647B3BE73561}</Project>
      <Name>UniTask.DOTween</Name>
    </ProjectReference>
    <ProjectReference Include="MagicaCloth.Editor.csproj">
      <Project>{E81811E8-FCB9-4674-22EB-89FE0F736A68}</Project>
      <Name>MagicaCloth.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Linq.csproj">
      <Project>{C8E48634-E7D8-1F70-2E3E-F3339CF23ACD}</Project>
      <Name>UniTask.Linq</Name>
    </ProjectReference>
    <ProjectReference Include="MagicaCloth.csproj">
      <Project>{FD597BE5-A559-54A1-2E57-3D1D615853F1}</Project>
      <Name>MagicaCloth</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Editor.csproj">
      <Project>{F7AFCDAB-EB8C-50EA-AEB8-4F5402D7E818}</Project>
      <Name>HybridCLR.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="HotFixSystem.csproj">
      <Project>{FF2A6F0E-1821-838D-C66E-5782B98B2A8D}</Project>
      <Name>HotFixSystem</Name>
    </ProjectReference>
    <ProjectReference Include="UniRx.csproj">
      <Project>{57272F7A-F591-FE44-425A-8D5F90D3EFDF}</Project>
      <Name>UniRx</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.csproj">
      <Project>{1E4C0B2A-7147-7D8B-74DB-D77243411067}</Project>
      <Name>Unity.Mathematics</Name>
    </ProjectReference>
    <ProjectReference Include="ParrelSync.csproj">
      <Project>{4A3BCF82-86B9-2A78-FB08-9D6BA4CAD2CC}</Project>
      <Name>ParrelSync</Name>
    </ProjectReference>
    <ProjectReference Include="AmplifyShaderEditor.csproj">
      <Project>{B5B5FC54-5793-0FBB-F9AA-7885AFED430D}</Project>
      <Name>AmplifyShaderEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Editor.csproj">
      <Project>{21F18CB6-20D6-CED1-0542-3AA20B219CEC}</Project>
      <Name>Unity.RenderPipelines.Universal.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="EasyCharacterMovement.CharacterMovement.csproj">
      <Project>{EB9D1ECB-1020-127B-5F3F-E7FE89DD7AA5}</Project>
      <Name>EasyCharacterMovement.CharacterMovement</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.csproj">
      <Project>{2971DB37-05CE-F0FF-87E2-A8069DD40BC2}</Project>
      <Name>Unity.ScriptableBuildPipeline</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.Editor.csproj">
      <Project>{F049DAA6-13BD-E157-237A-D48707F847E4}</Project>
      <Name>Unity.ScriptableBuildPipeline.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.csproj">
      <Project>{B8F6F662-F773-4F2F-CC1A-C6AB1D3E2A6E}</Project>
      <Name>UniTask</Name>
    </ProjectReference>
    <ProjectReference Include="StompyRobot.SRF.csproj">
      <Project>{E2AAE656-27ED-8885-B163-9F425709F0EE}</Project>
      <Name>StompyRobot.SRF</Name>
    </ProjectReference>
    <ProjectReference Include="ProtocolData.csproj">
      <Project>{958E9035-E704-7965-9AE4-9CCA3A8355EA}</Project>
      <Name>ProtocolData</Name>
    </ProjectReference>
    <ProjectReference Include="OverdrawMonitor.Editor.csproj">
      <Project>{88593884-3C2D-16C4-0E56-67B2826F5683}</Project>
      <Name>OverdrawMonitor.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="SPCRJointDynamicsEditor.csproj">
      <Project>{1F36D130-8091-39AF-24A4-91F2E4B1C3D5}</Project>
      <Name>SPCRJointDynamicsEditor</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="GenerateTargetFrameworkMonikerAttribute" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
